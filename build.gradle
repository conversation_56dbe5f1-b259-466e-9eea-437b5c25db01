plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.3'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'org.hibernate.orm' version '6.4.2.Final'
    id 'org.graalvm.buildtools.native' version '0.9.28'
    id 'org.jetbrains.kotlin.jvm' version '1.9.22'
    id 'org.jetbrains.kotlin.plugin.spring' version '1.9.22'
    id 'org.jetbrains.kotlin.plugin.jpa' version '1.9.22'
    id 'org.jetbrains.kotlin.kapt' version '1.9.22'
    id 'com.ewerk.gradle.plugins.querydsl' version '1.0.10'
    id 'idea'
}

group = 'com.lucablock'

java {
    sourceCompatibility = '21'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-authorization-server'
    implementation "org.springframework.boot:spring-boot-starter-oauth2-client"
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.jetbrains.kotlin:kotlin-reflect'
    implementation 'org.liquibase:liquibase-core'
    implementation 'org.hibernate:hibernate-core:6.4.4.Final'
    implementation 'org.postgresql:postgresql'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-api:2.3.0'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'
    implementation 'io.jsonwebtoken:jjwt:0.9.1'
    implementation 'redis.clients:jedis'
    implementation platform('software.amazon.awssdk:bom:2.5.29')
    implementation 'software.amazon.awssdk:s3'
    runtimeOnly 'org.postgresql:postgresql'

//    implementation("com.querydsl:querydsl-jpa:5.1.0")
    api 'com.querydsl:querydsl-jpa:5.1.0:jakarta'
    kapt 'com.querydsl:querydsl-apt:5.1.0:jakarta'
    annotationProcessor 'com.querydsl:querydsl-apt:5.1.0:jakarta'

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'io.mockk:mockk:1.13.8'
    testImplementation 'com.ninja-squad:springmockk:4.0.2'
    implementation 'jakarta.mail:jakarta.mail-api'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf:3.0.4'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'jakarta.xml.bind:jakarta.xml.bind-api:4.0.0'

    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign:4.1.3'

    implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.jetbrains.kotlin:kotlin-stdlib")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("io.mockk:mockk:1.13.12")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5:1.9.24")
}

kotlin {
    jvmToolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

graalvmNative {
    toolchainDetection = true
    binaries {
        main {
            javaLauncher = javaToolchains.launcherFor {
                languageVersion = JavaLanguageVersion.of(21)
                vendor = JvmVendorSpec.matching("GraalVM Community")
            }
        }
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

