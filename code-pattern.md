# DigiBoxs API - Code Patterns & Conventions

This document outlines the specific code patterns, conventions, and architectural decisions used throughout the DigiBoxs API project. All examples are taken from the actual codebase to ensure consistency when contributing to the project.

## 1. Entity Patterns

### JPA Entity Structure
All entities follow a consistent pattern with data classes implementing Serializable:

```kotlin
@Entity
@Table(name = "users")
data class User(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  var firstName: String? = null,
  var lastName: String? = null,
  var phoneNumber: String? = null,
  val phoneVerified: Boolean = false,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  @Column(name = "created_date")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")  
  @Column(name = "modified_date")
  val modifiedDate: Date = Date(),
  @Column(name = "is_deleted")
  var isDeleted: Boolean? = false,
  
  // Relationships
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  @JsonManagedReference
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "userTypeId", insertable = false, updatable = false)
  val userType: UserType? = null,
  
  @OneToMany(mappedBy = "userId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var address: List<Address?> = ArrayList()
) : Serializable
```

**Key Conventions:**
- All entities are Kotlin `data class` extending `Serializable`
- Primary keys use `@GeneratedValue(strategy = GenerationType.IDENTITY)` with `Long` type
- Date fields use `@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")`
- Soft delete pattern with `isDeleted: Boolean? = false`
- Relationships use `fetch = FetchType.LAZY` by default
- Foreign key fields use `insertable = false, updatable = false` for read-only relationships

### Enum Patterns
Enums contain business logic with Thai language values:

```kotlin
enum class StatusEnum(val key: Int, val value: String) {
  WAITING_FOR_PO(1, "รอเสนอราคา"),
  WAITING_FOR_PAYMENT(2, "รอชำระเงิน"), 
  PAYMENT_VERIFICATION(3, "ตรวจสอบการชำระเงิน"),
  PRODUCTION(4, "ดำเนินการผลิต"),
  COMPLETED(5, "สำเร็จ"),
  CANCELLED(6, "ยกเลิก");

  companion object {
    fun fromKey(key: Int): String? {
      return entries.find { it.key == key }?.value
    }
  }
}
```

## 2. DTO Patterns  

### QueryProjection DTOs
All DTOs use QueryDSL's QueryProjection for efficient database queries:

```kotlin
data class ProductDto
@QueryProjection
constructor(
  val id: Long,
  val name: String,
  val description: String? = null,
  val guideDetail: String,
  val installmentDetail: String,
  val shippingDetail: String,
  val isActive: Boolean,
  val isDeleted: Boolean,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
  val category: List<ProductCategoryConfigDto> = mutableListOf(),
  val tag: List<ProductTagConfigDto> = mutableListOf(),
  val type: List<ProductTypeConfigDto> = mutableListOf(),
  val specialTechnic: List<ProductSpecialTechnicDto> = mutableListOf(),
  val gallery: List<ProductGalleryDto> = mutableListOf()
)
```

**Key Conventions:**
- Use `@QueryProjection` annotation for QueryDSL integration
- Constructor parameters for QueryDSL generation
- Immutable properties with `val`
- Date fields with consistent JSON formatting
- Default values for optional fields
- Nested DTOs for related entities

### Request DTOs with Validation
Request classes use Jakarta validation with Thai error messages:

```kotlin
class CreateUserRequest {
  @NotBlank(message = "กรุณาระบุชื่อ")
  val firstName: String = ""
  
  @NotBlank(message = "กรุณาระบุนามสกุล")
  val lastName: String = ""
  
  @NotBlank(message = "กรุณาระบุอีเมล")
  @Email(message = "รูปแบบอีเมลไม่ถูกต้อง")
  val email: String = ""
  
  @NotBlank(message = "กรุณาระบุรหัสผ่าน")
  @Size(min = 8, message = "รหัสผ่านต้องมีความยาวอย่างน้อย 8 ตัวอักษร")
  val password: String = ""
  
  @Valid
  val address: List<CreateUserAddressRequest> = mutableListOf()
}
```

## 3. Repository Patterns

### JpaRepository with Custom Implementation
Repositories extend JpaRepository and custom interface for complex queries:

```kotlin
@Repository
interface ProductRepository : JpaRepository<Product, Long>, ProductRepositoryCustom {
  fun findAllByNameContainingIgnoreCase(name: String, pageable: Pageable): Page<Product>
  fun existsByNameContainingIgnoreCaseAndIdNot(name: String, id: Long): Boolean
  fun findByIdAndIsDeletedFalse(id: Long): Product?
}

interface ProductRepositoryCustom {
  fun getProductPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    categoryId: List<Long>?,
    isActive: Boolean?
  ): Page<ProductDto>
}
```

### QueryDSL Implementation Pattern
Custom repository implementations use JPAQueryFactory:

```kotlin
@Service
class ProductRepositoryImpl : ProductRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qProduct = QProduct.product
  private val qProductCategory = QProductCategory.productCategory
  
  override fun getProductPage(
    pageable: Pageable,
    ascending: Boolean, 
    search: String?,
    categoryId: List<Long>?,
    isActive: Boolean?
  ): Page<ProductDto> {
    
    var criteria = qProduct.isDeleted.eq(false)
    
    // Dynamic query building
    search?.let {
      criteria = criteria.and(qProduct.name.containsIgnoreCase(it))
    }
    
    categoryId?.let {
      criteria = criteria.and(qProduct.id.`in`(
        JPAExpressions.select(qProductCategory.productId)
          .from(qProductCategory)
          .where(qProductCategory.categoryId.`in`(it))
      ))
    }
    
    // Execute query with pagination
    val results = queryFactory
      .select(QProductDto(qProduct.id, qProduct.name, /* ... */))
      .from(qProduct)
      .where(criteria)
      .orderBy(if (ascending) qProduct.name.asc() else qProduct.name.desc())
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()
      
    val total = queryFactory
      .selectFrom(qProduct)
      .where(criteria)
      .fetchCount()
      
    return PageImpl(results, pageable, total)
  }
}
```

## 4. Service Layer Patterns

### Service Interface Design
Clean interface segregation with consistent naming:

```kotlin
interface UserService {
  fun createUser(createUserRequest: CreateUserRequest, role: String, host: String?): UserDto
  fun getUserById(userId: Long): UserDto
  fun updateUserMe(userId: Long, updateUserRequest: String, file: MultipartFile?): UserDto
  fun deleteAccount(userId: Long): Boolean
  fun verifyEmail(userPrincipal: UserPrincipal, host: String?): Boolean
  fun forgotPassword(forgotPasswordDto: ForgotPasswordDto): Boolean
}
```

### Service Implementation Pattern
Constructor injection with transaction management:

```kotlin
@Service
class UserServiceImpl @Autowired constructor(
  private val userRepository: UserRepository,
  private val addressRepository: AddressRepository,
  private val userTypeRepository: UserTypeRepository,
  private val emailService: EmailService,
  private val s3Service: S3Service,
  private val passwordEncoder: PasswordEncoder,
  private val jwtTokenProvider: JWTTokenProvider
) : UserService {

  @Transactional
  override fun createUser(
    createUserRequest: CreateUserRequest,
    role: String,
    host: String?
  ): UserDto {
    // Check if user exists
    userRepository.findByEmail(createUserRequest.email)?.let {
      throw BadRequestException("อีเมลนี้ถูกใช้งานแล้ว")
    }
    
    // Business logic implementation
    val userType = userTypeRepository.findByName(role) 
      ?: throw NotFoundException("ไม่พบประเภทผู้ใช้งาน")
    
    val user = User(
      firstName = createUserRequest.firstName,
      lastName = createUserRequest.lastName,
      email = createUserRequest.email,
      password = passwordEncoder.encode(createUserRequest.password),
      userTypeId = userType.id
    )
    
    val savedUser = userRepository.save(user)
    
    // Send verification email
    emailService.sendVerificationEmail(savedUser, host)
    
    return savedUser.toUserDto()
  }
}
```

**Key Conventions:**
- Constructor dependency injection with `@Autowired`
- `@Transactional` annotation for data modification methods
- Custom exception throwing with Thai messages
- Entity to DTO conversion methods
- Null-safe operations with Kotlin's `?.` and `?:`

## 5. Controller Patterns

### REST Controller Structure
Consistent controller pattern with comprehensive error handling:

```kotlin
@RestController
@RequestMapping("/api/user")
class UserController {
  private val logger: Logger = LoggerFactory.getLogger(UserController::class.java)

  @Autowired
  lateinit var userService: UserService

  @Autowired
  lateinit var authenticationManager: AuthenticationManager

  @Autowired
  lateinit var jwtTokenProvider: JWTTokenProvider

  @PostMapping("/login")
  fun login(
    @Valid @RequestBody loginRequest: LoginRequest,
    httpServletRequest: HttpServletRequest,
    httpServletResponse: HttpServletResponse
  ): ResponseEntity<Any> {
    try {
      val authentication = authenticationManager.authenticate(
        UsernamePasswordAuthenticationToken(
          loginRequest.username,
          loginRequest.password
        )
      )
      
      if (authentication.isAuthenticated) {
        val userPrincipal = authentication.principal as UserPrincipal
        
        if (!userPrincipal.getEmailVerify()!!) {
          return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(HttpResponse(false, "กรุณายืนยันอีเมลของคุณ ก่อนเข้าสู่ระบบ!"))
        }
        
        val token = jwtTokenProvider.generateToken(userPrincipal)
        
        return ResponseEntity.ok().body(
          HttpResponse(true, "เข้าสู่ระบบสำเร็จ", token)
        )
      }
      
      return ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่สามารถเข้าสู่ระบบได้ อีเมลหรือรหัสผ่านไม่ถูกต้อง"))
        
    } catch (e: BadCredentialsException) {
      logger.error("login error : ${e.message}")
      return ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถเข้าสู่ระบบได้"))
    } catch (e: Exception) {
      logger.error("login error : ${e.message}")
      return ResponseEntity.status(HttpStatus.FORBIDDEN)
        .body(HttpResponse(false, e.message ?: "คุณไม่มีสิทธิ์การเข้าถึงข้อมูลในหน้านี้"))
    }
  }

  @GetMapping("/me")
  fun getUser(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(true, "แสดงข้อมูลผู้ใช้สำเร็จ", userService.getUserById(userPrincipal.getUserId()))
      )
    } catch (e: Exception) {
      logger.error("get user error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }
}
```

**Key Conventions:**
- `@RestController` with `@RequestMapping` base path
- SLF4J logger with class-specific loggers
- Field injection with `@Autowired lateinit var`
- Comprehensive try-catch exception handling
- `HttpResponse` wrapper for all responses
- Thai language error messages
- `@AuthenticationPrincipal` for user context
- `@Parameter(hidden = true)` for Swagger documentation

### Security and Authorization Patterns
Method-level security with role-based access:

```kotlin
@PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
@PostMapping("/admin/product")
fun createProduct(@Valid @RequestBody productRequest: ProductRequest): ResponseEntity<Any> {
  return try {
    ResponseEntity.ok(
      HttpResponse(true, "เพิ่มผลิตภัณฑ์สำเร็จ", productService.createProduct(productRequest))
    )
  } catch (e: BadRequestException) {
    logger.error("create product error : ${e.message}")
    ResponseEntity.status(HttpStatus.BAD_REQUEST)
      .body(HttpResponse(false, "ผลิตภัณฑ์นี้มีอยู่แล้ว"))
  }
}
```

## 6. Response Standardization

### HttpResponse Wrapper
All API responses use a consistent wrapper structure:

```kotlin
data class HttpResponse(
  val status: Boolean,
  val message: String,
  val data: Any? = null
)

// Usage examples:
HttpResponse(true, "สำเร็จ", userData)           // Success with data
HttpResponse(true, "บันทึกสำเร็จ")                // Success without data  
HttpResponse(false, "เกิดข้อผิดพลาด")            // Error response
```

## 7. Exception Handling Patterns

### Custom Exception Classes
Simple exception hierarchy with message-based constructors:

```kotlin
class BadRequestException(message: String) : Exception(message)
class NotFoundException(message: String) : Exception(message)
class ConflictException(message: String) : Exception(message)
class ForbiddenException(message: String) : Exception(message)
class InternalServerException(message: String) : Exception(message)
class NoContentException(message: String) : Exception(message)
```

### Global Exception Handler
Centralized exception handling with consistent responses:

```kotlin
@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
class RestExceptionHandler {

  @ExceptionHandler(value = [(BadRequestException::class)])
  protected fun badRequestException(ex: BadRequestException): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Bad Request")
    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError)
  }

  @ExceptionHandler(value = [(NotFoundException::class)])
  protected fun notFoundException(ex: NotFoundException): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Not Found")
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(apiError)
  }

  @ExceptionHandler(MethodArgumentNotValidException::class)
  protected fun handleMethodArgumentNotValid(
    ex: MethodArgumentNotValidException
  ): ResponseEntity<Any> {
    val errors = ex.bindingResult.fieldErrors
      .map { "${it.field}: ${it.defaultMessage}" }
      .joinToString(", ")
    
    val apiError = ErrorResponseMessage(errorMessage = errors)
    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError)
  }
}
```

## 8. Configuration Patterns

### Security Configuration
Spring Security setup with JWT and OAuth2:

```kotlin
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
class SecurityConfig @Autowired constructor(
  private val customOAuth2UserService: CustomOAuth2UserService,
  private val customOidcUserService: CustomOidcUserService,
  private val oAuth2AuthenticationSuccessHandler: OAuth2AuthenticationSuccessHandler,
  private val oAuth2AuthenticationFailureHandler: OAuth2AuthenticationFailureHandler,
  private val httpCookieOAuth2AuthorizationRequestRepository: HttpCookieOAuth2AuthorizationRequestRepository
) {
  
  @Bean
  fun filterChain(http: HttpSecurity): SecurityFilterChain {
    http
      .cors(Customizer.withDefaults())
      .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
      .csrf { it.disable() }
      .formLogin { it.disable() }
      .httpBasic { it.disable() }
      .exceptionHandling {
        it.authenticationEntryPoint(RestAuthenticationEntryPoint())
      }
      .authorizeHttpRequests { authz ->
        authz
          .requestMatchers("/", "/error", "/favicon.ico").permitAll()
          .requestMatchers("/api/web/**").permitAll()
          .requestMatchers("/api/user/login", "/api/user/register").permitAll()
          .requestMatchers("/oauth2/**").permitAll()
          .anyRequest().authenticated()
      }
      .oauth2Login { oauth2 ->
        oauth2
          .authorizationEndpoint {
            it.authorizationRequestRepository(httpCookieOAuth2AuthorizationRequestRepository)
          }
          .userInfoEndpoint {
            it.userService(customOAuth2UserService)
            it.oidcUserService(customOidcUserService)
          }
          .successHandler(oAuth2AuthenticationSuccessHandler)
          .failureHandler(oAuth2AuthenticationFailureHandler)
      }
      
    http.addFilterBefore(
      JWTAuthorizationFilter(JWTTokenProvider()), 
      UsernamePasswordAuthenticationFilter::class.java
    )
    
    return http.build()
  }
}
```

### Properties Configuration
Structured configuration with nested classes:

```kotlin
@ConfigurationProperties(prefix = "app")
class AppProperties {
  val auth = Auth()
  val oauth2 = OAuth2()

  class Auth {
    var tokenSecret: String? = null
    var tokenExpirationMsec: Long = 0
  }

  class OAuth2 {
    var authorizedRedirectUris: List<String> = ArrayList()
  }
}
```

## 9. Utility and Extension Patterns

### Entity to DTO Extension Functions
Conversion methods for clean entity-DTO mapping:

```kotlin
fun User.toUserDto(): UserDto {
  return UserDto(
    id = this.id,
    firstName = this.firstName,
    lastName = this.lastName,
    email = this.email,
    fullName = this.fullName,
    imageUrl = this.imageUrl,
    emailVerified = this.emailVerified,
    userType = this.userType?.toUserTypeDto(),
    address = this.address.filterNotNull().map { it.toAddressDto() }
  )
}

fun Product.toProductDto(): ProductDto {
  return ProductDto(
    id = this.id,
    name = this.name,
    description = this.description,
    // ... other mappings
  )
}
```

### Thai Character Utilities
Special handling for Thai character encoding:

```kotlin
object DecodeThailandCharacters {
  fun String.decodeThailandCharacters(): String {
    return String(this.toByteArray(Charset.forName("ISO-8859-1")), StandardCharsets.UTF_8)
  }
}
```

## 10. Testing Patterns

### Service Testing with MockK
Kotlin-specific testing patterns:

```kotlin
@ExtendWith(MockKExtension::class)
class UserServiceImplTest {
  private lateinit var userService: UserServiceImpl
  
  @MockK
  private lateinit var userRepository: UserRepository
  
  @MockK  
  private lateinit var passwordEncoder: PasswordEncoder
  
  @MockK
  private lateinit var emailService: EmailService

  @BeforeEach
  fun setUp() {
    userService = UserServiceImpl(
      userRepository,
      passwordEncoder,
      emailService
    )
  }

  @Test
  fun `should create user successfully`() {
    // Given
    val createUserRequest = CreateUserRequest(
      firstName = "John",
      email = "<EMAIL>",
      password = "password123"
    )
    
    every { userRepository.findByEmail(any()) } returns null
    every { passwordEncoder.encode(any()) } returns "encodedPassword"
    every { userRepository.save(any()) } returns mockUser
    every { emailService.sendVerificationEmail(any(), any()) } just Runs

    // When
    val result = userService.createUser(createUserRequest, "USER", null)

    // Then
    assertThat(result.email).isEqualTo("<EMAIL>")
    verify { userRepository.save(any()) }
    verify { emailService.sendVerificationEmail(any(), any()) }
  }
}
```

## 11. File Upload Patterns

### Multipart File Handling
Consistent file upload pattern with S3 integration:

```kotlin
@PutMapping(
  "/updateProfile", 
  consumes = [MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE]
)
fun updateProfile(
  @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
  @RequestPart("updateUserRequest", required = true) updateUserRequest: String,
  @RequestPart("file", required = false) file: MultipartFile?
): ResponseEntity<Any> {
  return try {
    ResponseEntity.ok(
      HttpResponse(
        true,
        "อัปโหลดรูปโปรไฟล์สำเร็จ",
        userService.updateUserMe(userPrincipal.getUserId(), updateUserRequest, file)
      )
    )
  } catch (e: Exception) {
    logger.error("update user error : ${e.message}")
    ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
      .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
  }
}
```

## Key Architectural Decisions Summary

1. **Language & Framework**: Kotlin with Spring Boot 3.2.3
2. **Database**: PostgreSQL with JPA/Hibernate
3. **Query Framework**: QueryDSL for complex queries with @QueryProjection
4. **Security**: Spring Security with JWT tokens and OAuth2
5. **Validation**: Jakarta validation with Thai error messages
6. **Testing**: JUnit 5 with MockK for Kotlin-specific testing
7. **API Documentation**: SpringDoc OpenAPI with Swagger UI
8. **Caching**: Redis for token management and session storage
9. **File Storage**: AWS S3 for media and document storage
10. **Response Format**: Standardized HttpResponse wrapper
11. **Error Handling**: Global exception handling with custom exceptions
12. **Internationalization**: Thai language throughout the application
13. **Code Style**: Kotlin idioms with null safety and data classes
14. **Dependency Injection**: Constructor injection preferred, field injection for controllers

This guide ensures consistency across all development efforts in the DigiBoxs API project.