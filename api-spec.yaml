openapi: 3.0.3
info:
  title: DigiBoxs API
  description: |
    B2B e-commerce platform API for custom digital printing and packaging services in Thailand.
    
    ## Authentication
    The API uses JWT Bearer tokens for authentication. Obtain a token via the `/api/user/login` endpoint.
    
    ## Response Format
    All API responses are wrapped in a standard `HttpResponse` format with status, message, and optional data fields.
    
    ## Language
    All messages and error responses are in Thai language.
  version: 1.0.0
  contact:
    name: DigiBoxs Support
    email: <EMAIL>
  license:
    name: Proprietary
    
servers:
  - url: http://localhost:8080
    description: Local development server
  - url: https://api.digiboxs.com
    description: Production server
    
security:
  - bearerAuth: []
    
tags:
  - name: Authentication
    description: User authentication and registration
  - name: User Management
    description: User profile and account management  
  - name: Products
    description: Product catalog operations
  - name: Customization
    description: Product customization and configuration
  - name: Cart
    description: Shopping cart management
  - name: Orders
    description: Order processing and management
  - name: Admin
    description: Administrative operations (requires admin role)
  - name: Discounts
    description: Discount and coupon management
  - name: Company
    description: Company settings and configuration
  - name: Reviews
    description: Product and model reviews
  - name: Support
    description: FAQ and customer support
    
paths:
  # Authentication Endpoints
  /api/user/register:
    post:
      tags:
        - Authentication
      summary: Register new user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '200':
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
        '400':
          description: Email already exists or validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  /api/user/login:
    post:
      tags:
        - Authentication
      summary: User login
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        type: string
                        description: JWT token
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        '401':
          description: Invalid credentials or email not verified
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  /api/user/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  # User Management Endpoints
  /api/user/me:
    get:
      tags:
        - User Management
      summary: Get current user profile
      responses:
        '200':
          description: User profile retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserDto'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
          
    put:
      tags:
        - User Management
      summary: Update user profile
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                updateUserRequest:
                  type: string
                  description: JSON string of update request
                file:
                  type: string
                  format: binary
                  description: Profile image file
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  /api/user/change-password:
    put:
      tags:
        - User Management
      summary: Change user password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePasswordDto'
      responses:
        '200':
          description: Password changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
        '400':
          description: Invalid old password
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  /api/user/forgot-password:
    post:
      tags:
        - User Management
      summary: Request password reset
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForgotPasswordDto'
      responses:
        '200':
          description: Password reset email sent
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  # Product Endpoints
  /api/web/product/page:
    get:
      tags:
        - Products
      summary: Get paginated product list
      security: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          schema:
            type: integer
            default: 20
        - name: ascending
          in: query
          schema:
            type: boolean
            default: false
        - name: categoryId
          in: query
          schema:
            type: array
            items:
              type: integer
          style: form
          explode: true
        - name: tagId
          in: query
          schema:
            type: array
            items:
              type: integer
          style: form
          explode: true
        - name: typeId
          in: query
          schema:
            type: array
            items:
              type: integer
          style: form
          explode: true
        - name: search
          in: query
          schema:
            type: string
        - name: isActive
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Product list retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PageProductDto'
                        
  /api/web/product/{id}:
    get:
      tags:
        - Products
      summary: Get product by ID
      security: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Product retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ProductDto'
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  /api/admin/product:
    post:
      tags:
        - Admin
        - Products
      summary: Create new product (Admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductRequest'
      responses:
        '200':
          description: Product created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
        '400':
          description: Product already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
        '403':
          $ref: '#/components/responses/ForbiddenError'
          
  # Customization Endpoints
  /api/customize:
    post:
      tags:
        - Customization
      summary: Create product customization
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomizeRequest'
      responses:
        '200':
          description: Customization created
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/CustomizeDto'
                        
  /api/customize/{id}:
    get:
      tags:
        - Customization
      summary: Get customization details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customization retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/CustomizeDto'
                        
    put:
      tags:
        - Customization
      summary: Update customization
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomizeRequest'
      responses:
        '200':
          description: Customization updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  # Cart Endpoints
  /api/cart:
    post:
      tags:
        - Cart
      summary: Add item to cart
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CartRequest'
      responses:
        '200':
          description: Item added to cart
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  /api/cart/list:
    get:
      tags:
        - Cart
      summary: Get cart items
      parameters:
        - name: sessionId
          in: query
          schema:
            type: string
          description: Session ID for anonymous users
      responses:
        '200':
          description: Cart items retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/CartDto'
                          
  /api/cart/{id}:
    delete:
      tags:
        - Cart
      summary: Remove item from cart
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Item removed from cart
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  # Order Endpoints
  /api/order:
    post:
      tags:
        - Orders
      summary: Create order from cart
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '200':
          description: Order created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OrderDto'
                        
  /api/order/list:
    get:
      tags:
        - Orders
      summary: Get user orders
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [WAITING_FOR_QUOTATION, WAITING_FOR_PAYMENT, PAYMENT_VERIFICATION, PRODUCTION, COMPLETED, CANCELLED]
      responses:
        '200':
          description: Orders retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PageOrderDto'
                        
  /api/order/{id}:
    get:
      tags:
        - Orders
      summary: Get order details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Order retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OrderDto'
                        
  /api/order/payment:
    post:
      tags:
        - Orders
      summary: Process payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentRequest'
      responses:
        '200':
          description: Payment processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  /api/admin/order/{id}/status:
    put:
      tags:
        - Admin
        - Orders
      summary: Update order status (Admin only)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOrderStatusRequest'
      responses:
        '200':
          description: Order status updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
        '403':
          $ref: '#/components/responses/ForbiddenError'
          
  # Discount Endpoints
  /api/discount/validate:
    post:
      tags:
        - Discounts
      summary: Validate discount code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: Discount code
                orderTotal:
                  type: number
                  description: Order total amount
      responses:
        '200':
          description: Discount valid
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/DiscountDto'
        '400':
          description: Invalid or expired discount
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
  # FAQ Endpoints
  /api/faq/list:
    get:
      tags:
        - Support
      summary: Get FAQ list
      security: []
      responses:
        '200':
          description: FAQ list retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/HttpResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/FaqDto'
                          
  /api/faq/submit:
    post:
      tags:
        - Support
      summary: Submit new FAQ question
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFaqRequest'
      responses:
        '200':
          description: Question submitted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HttpResponse'
                
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from login endpoint
      
  responses:
    UnauthorizedError:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/HttpResponse'
            
    ForbiddenError:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/HttpResponse'
            
  schemas:
    # Common Response Schema
    HttpResponse:
      type: object
      required:
        - status
        - message
      properties:
        status:
          type: boolean
          description: Operation success status
        message:
          type: string
          description: Response message (in Thai)
        data:
          description: Response data (varies by endpoint)
          
    # User Schemas
    CreateUserRequest:
      type: object
      required:
        - firstName
        - lastName
        - email
        - password
        - phoneNumber
      properties:
        firstName:
          type: string
          minLength: 1
          description: First name
        lastName:
          type: string
          minLength: 1
          description: Last name
        email:
          type: string
          format: email
          description: Email address
        password:
          type: string
          minLength: 8
          description: Password (min 8 characters)
        phoneNumber:
          type: string
          pattern: '^[0-9]{10}$'
          description: Phone number (10 digits)
        companyName:
          type: string
          description: Company name (optional)
        address:
          type: array
          items:
            $ref: '#/components/schemas/CreateUserAddressRequest'
            
    CreateUserAddressRequest:
      type: object
      required:
        - address
        - provinceId
        - districtId
        - subDistrictId
        - zipcode
      properties:
        address:
          type: string
          description: Street address
        provinceId:
          type: integer
          description: Province ID
        districtId:
          type: integer
          description: District ID
        subDistrictId:
          type: integer
          description: Sub-district ID
        zipcode:
          type: string
          description: Postal code
        isDefault:
          type: boolean
          default: false
          description: Set as default address
          
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          description: Email address
        password:
          type: string
          description: Password
          
    UpdatePasswordDto:
      type: object
      required:
        - oldPassword
        - newPassword
        - confirmPassword
      properties:
        oldPassword:
          type: string
          description: Current password
        newPassword:
          type: string
          minLength: 8
          description: New password
        confirmPassword:
          type: string
          description: Confirm new password
          
    ForgotPasswordDto:
      type: object
      required:
        - email
      properties:
        email:
          type: string
          format: email
          description: Email address
          
    UserDto:
      type: object
      properties:
        id:
          type: integer
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        phoneNumber:
          type: string
        companyName:
          type: string
        imageUrl:
          type: string
        emailVerified:
          type: boolean
        userType:
          $ref: '#/components/schemas/UserTypeDto'
        address:
          type: array
          items:
            $ref: '#/components/schemas/AddressDto'
        createdDate:
          type: string
          format: date-time
        modifiedDate:
          type: string
          format: date-time
          
    UserTypeDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          enum: [USER, ADMIN, SUPER_ADMIN, GUEST]
        description:
          type: string
          
    AddressDto:
      type: object
      properties:
        id:
          type: integer
        address:
          type: string
        province:
          type: string
        district:
          type: string
        subDistrict:
          type: string
        zipcode:
          type: string
        isDefault:
          type: boolean
          
    # Product Schemas
    ProductDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        guideDetail:
          type: string
        installmentDetail:
          type: string
        shippingDetail:
          type: string
        isActive:
          type: boolean
        category:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategoryDto'
        tag:
          type: array
          items:
            $ref: '#/components/schemas/ProductTagDto'
        type:
          type: array
          items:
            $ref: '#/components/schemas/ProductTypeDto'
        specialTechnic:
          type: array
          items:
            $ref: '#/components/schemas/SpecialTechnicDto'
        gallery:
          type: array
          items:
            $ref: '#/components/schemas/ProductGalleryDto'
        models:
          type: array
          items:
            $ref: '#/components/schemas/ModelDto'
            
    ProductCategoryDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
          
    ProductTagDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          
    ProductTypeDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          
    SpecialTechnicDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        price:
          type: number
        isPercentage:
          type: boolean
          
    ProductGalleryDto:
      type: object
      properties:
        id:
          type: integer
        imageUrl:
          type: string
        fileType:
          type: string
          
    ModelDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        thumbnails:
          type: array
          items:
            $ref: '#/components/schemas/ModelThumbnailDto'
        sizes:
          type: array
          items:
            $ref: '#/components/schemas/ModelSizeDto'
            
    ModelThumbnailDto:
      type: object
      properties:
        id:
          type: integer
        imageUrl:
          type: string
        isDefault:
          type: boolean
          
    ModelSizeDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        width:
          type: number
        height:
          type: number
        length:
          type: number
        basePrice:
          type: number
          
    ProductRequest:
      type: object
      required:
        - name
        - guideDetail
        - installmentDetail
        - shippingDetail
      properties:
        name:
          type: string
        description:
          type: string
        guideDetail:
          type: string
        installmentDetail:
          type: string
        shippingDetail:
          type: string
        categoryIds:
          type: array
          items:
            type: integer
        tagIds:
          type: array
          items:
            type: integer
        typeIds:
          type: array
          items:
            type: integer
            
    PageProductDto:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ProductDto'
        totalElements:
          type: integer
        totalPages:
          type: integer
        size:
          type: integer
        number:
          type: integer
        first:
          type: boolean
        last:
          type: boolean
          
    # Customization Schemas
    CustomizeRequest:
      type: object
      required:
        - productId
        - modelId
        - modelSizeId
        - quantity
      properties:
        productId:
          type: integer
        modelId:
          type: integer
        modelSizeId:
          type: integer
        quantity:
          type: integer
          minimum: 1
        materialId:
          type: integer
        gramId:
          type: integer
        coatingIds:
          type: array
          items:
            type: integer
        specialTechnicIds:
          type: array
          items:
            type: integer
        customWidth:
          type: number
        customHeight:
          type: number
        customLength:
          type: number
        notes:
          type: string
          
    CustomizeDto:
      type: object
      properties:
        id:
          type: integer
        product:
          $ref: '#/components/schemas/ProductDto'
        model:
          $ref: '#/components/schemas/ModelDto'
        modelSize:
          $ref: '#/components/schemas/ModelSizeDto'
        quantity:
          type: integer
        material:
          $ref: '#/components/schemas/MaterialDto'
        gram:
          $ref: '#/components/schemas/GramDto'
        coatings:
          type: array
          items:
            $ref: '#/components/schemas/CoatingDto'
        specialTechnics:
          type: array
          items:
            $ref: '#/components/schemas/SpecialTechnicDto'
        customWidth:
          type: number
        customHeight:
          type: number
        customLength:
          type: number
        unitPrice:
          type: number
        totalPrice:
          type: number
        notes:
          type: string
          
    MaterialDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        priceModifier:
          type: number
          
    GramDto:
      type: object
      properties:
        id:
          type: integer
        weight:
          type: integer
        priceModifier:
          type: number
          
    CoatingDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        price:
          type: number
          
    # Cart Schemas
    CartRequest:
      type: object
      required:
        - customizeId
        - quantity
      properties:
        customizeId:
          type: integer
        quantity:
          type: integer
          minimum: 1
        sessionId:
          type: string
          description: Session ID for anonymous users
          
    CartDto:
      type: object
      properties:
        id:
          type: integer
        customize:
          $ref: '#/components/schemas/CustomizeDto'
        quantity:
          type: integer
        unitPrice:
          type: number
        totalPrice:
          type: number
        addedDate:
          type: string
          format: date-time
          
    # Order Schemas
    CreateOrderRequest:
      type: object
      required:
        - cartIds
        - shippingAddressId
      properties:
        cartIds:
          type: array
          items:
            type: integer
        shippingAddressId:
          type: integer
        billingAddressId:
          type: integer
        discountCode:
          type: string
        notes:
          type: string
          
    OrderDto:
      type: object
      properties:
        id:
          type: integer
        orderNumber:
          type: string
        status:
          type: string
          enum: [WAITING_FOR_QUOTATION, WAITING_FOR_PAYMENT, PAYMENT_VERIFICATION, PRODUCTION, COMPLETED, CANCELLED]
        statusValue:
          type: string
          description: Status in Thai
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItemDto'
        subtotal:
          type: number
        discountAmount:
          type: number
        tax:
          type: number
        shippingFee:
          type: number
        total:
          type: number
        shippingAddress:
          $ref: '#/components/schemas/AddressDto'
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        paymentMethod:
          type: string
        notes:
          type: string
        createdDate:
          type: string
          format: date-time
        updatedDate:
          type: string
          format: date-time
          
    OrderItemDto:
      type: object
      properties:
        id:
          type: integer
        customize:
          $ref: '#/components/schemas/CustomizeDto'
        quantity:
          type: integer
        unitPrice:
          type: number
        totalPrice:
          type: number
          
    PageOrderDto:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/OrderDto'
        totalElements:
          type: integer
        totalPages:
          type: integer
        size:
          type: integer
        number:
          type: integer
          
    UpdateOrderStatusRequest:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [WAITING_FOR_QUOTATION, WAITING_FOR_PAYMENT, PAYMENT_VERIFICATION, PRODUCTION, COMPLETED, CANCELLED]
        notes:
          type: string
          
    PaymentRequest:
      type: object
      required:
        - orderId
        - paymentMethod
      properties:
        orderId:
          type: integer
        paymentMethod:
          type: string
          enum: [BANK_TRANSFER, CREDIT_CARD, PROMPTPAY]
        slipImage:
          type: string
          description: Base64 encoded payment slip image (for bank transfer)
        transactionId:
          type: string
          description: Transaction reference number
          
    # Discount Schemas
    DiscountDto:
      type: object
      properties:
        id:
          type: integer
        code:
          type: string
        description:
          type: string
        discountType:
          type: string
          enum: [PERCENTAGE, FIXED_AMOUNT]
        discountValue:
          type: number
        minimumOrderAmount:
          type: number
        maximumDiscountAmount:
          type: number
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        usageLimit:
          type: integer
        usageCount:
          type: integer
          
    # FAQ Schemas
    FaqDto:
      type: object
      properties:
        id:
          type: integer
        question:
          type: string
        answer:
          type: string
        category:
          type: string
        isPublished:
          type: boolean
          
    CreateFaqRequest:
      type: object
      required:
        - question
        - category
      properties:
        question:
          type: string
        category:
          type: string
          enum: [GENERAL, ORDER, PAYMENT, SHIPPING, PRODUCT, CUSTOMIZATION]
        email:
          type: string
          format: email
          description: Email for response notification