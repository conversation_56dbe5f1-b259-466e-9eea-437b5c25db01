version: '3.8'

services:
  db:
    image: postgres
    ports:
      - "5432:5432"
    restart: always
    environment:
      POSTGRES_DB: digoboxs
      POSTGRES_USER: root
      POSTGRES_PASSWORD: 12341234
    volumes:
      - postgres-data:/var/lib/postgresql/data

  redis:
    image: redis:7.0.8-alpine
    restart: always
    command: redis-server
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data

volumes:
  postgres-data:
  redis-data: