apiVersion: v1
kind: PersistentVolume
metadata:
  name: local-digibox-storage-pv1
  namespace: digibox-dev
spec:
  storageClassName: local-dev-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/var/lib/data1"

---

apiVersion: v1
kind: PersistentVolume
metadata:
  name: local-digibox-storage-pv2
  namespace: digibox-dev
spec:
  storageClassName: local-dev-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/var/lib/data2"

---

apiVersion: v1
kind: PersistentVolume
metadata:
  name: local-digibox-storage-pv3
  namespace: digibox-dev
spec:
  storageClassName: local-dev-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/var/lib/data3"
