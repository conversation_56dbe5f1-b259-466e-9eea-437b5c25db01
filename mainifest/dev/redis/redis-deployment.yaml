apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-dev
  namespace: digibox-dev
spec:
  serviceName: redis-dev
  replicas: 3
  selector:
    matchLabels:
      app: redis-dev
  template:
    metadata:
      labels:
        app: redis-dev
    spec:
      initContainers:
        - name: config
          image: redis:7.0.0-alpine
          command: [ "sh", "-c" ]
          args:
            - |
              cp /tmp/redis/redis.conf /etc/redis/redis.conf

              echo "finding master..."
              MASTER_FDQN=`hostname  -f | sed -e 's/redis-dev-[0-9]\./redis-dev-0./'`
              if [ "$(redis-cli -h sentinel -p 5000 ping)" != "PONG" ]; then
                echo "master not found, defaulting to redis-dev-0"

                if [ "$(hostname)" == "redis-dev-0" ]; then
                  echo "this is redis-dev-0, not updating config..."
                else
                  echo "updating redis.conf..."
                  echo "slaveof $MASTER_FDQN 6379" >> /etc/redis/redis.conf
                fi
              else
                echo "sentinel found, finding master"
                MASTER="$(redis-cli -h sentinel -p 5000 sentinel get-master-addr-by-name mymaster | grep -E '(^redis-dev-\d{1,})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})')"
                echo "master found : $MASTER, updating redis.conf"
                echo "slaveof $MASTER 6379" >> /etc/redis/redis.conf
              fi
          volumeMounts:
            - name: redis-config-dev
              mountPath: /etc/redis/
            - name: config
              mountPath: /tmp/redis/
      containers:
        - name: redis
          image: redis:6.2.3-alpine
          command: [ "redis-server" ]
          args: [ "/etc/redis/redis.conf" ]
          ports:
            - containerPort: 6379
              name: redis
          volumeMounts:
            - name: data
              mountPath: /data
            - name: redis-config-dev
              mountPath: /etc/redis/
      volumes:
        - name: redis-config-dev
          emptyDir: { }
        - name: config
          configMap:
            name: redis-config-dev
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: [ "ReadWriteOnce" ]
        storageClassName: "local-dev-storage"
        resources:
          requests:
            storage: 500Mi
