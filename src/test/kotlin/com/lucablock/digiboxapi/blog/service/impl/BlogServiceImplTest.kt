package com.lucablock.digiboxapi.blog.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.blog.dto.BlogByIdDto
import com.lucablock.digiboxapi.blog.dto.BlogBySlugDto
import com.lucablock.digiboxapi.blog.repository.*
import com.lucablock.digiboxapi.blog.request.BlogRequest
import com.lucablock.digiboxapi.blog.request.BlogTypeRequest
import com.lucablock.digiboxapi.blog.request.CategoriesRequest
import com.lucablock.digiboxapi.blog.response.BlogGalleryResponse
import com.lucablock.digiboxapi.blog.response.BlogResponse
import com.lucablock.digiboxapi.blog.response.BlogTypeResponse
import com.lucablock.digiboxapi.blog.response.CategoriesResponse
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.awss3.response.S3Response
import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile
import java.util.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull

class BlogServiceImplTest {

    private lateinit var blogService: BlogServiceImpl
    private lateinit var blogRepository: BlogRepository
    private lateinit var categoriesMappingRepository: BlogCategoryMappingRepository
    private lateinit var categoriesRepository: BlogCategoryRepository
    private lateinit var galleryRepository: GalleryRepository
    private lateinit var s3Service: S3Service
    private lateinit var blogTypeRepository: BlogTypeRepository
    private lateinit var blogCategoryRepository: BlogCategoryRepository

    @BeforeEach
    fun setUp() {
        blogRepository = mockk()
        categoriesMappingRepository = mockk()
        categoriesRepository = mockk()
        galleryRepository = mockk()
        s3Service = mockk()
        blogTypeRepository = mockk()
        blogCategoryRepository = mockk()

        blogService = BlogServiceImpl(
            blogRepository,
            categoriesMappingRepository,
            categoriesRepository,
            galleryRepository,
            s3Service,
            blogTypeRepository,
            blogCategoryRepository
        )
    }

    // Test Data
    private fun createValidBlogRequest() = BlogRequest(
        title = "Test Blog Title",
        content = "Test blog content",
        categories = listOf(1, 2),
        blogTypeId = 1,
        keyword = "test, blog",
        isPublished = true,
        thumbNailUrl = "https://example.com/thumbnail.jpg",
        urlSlug = "test-blog-title",
        description = "Test blog description",
        author = "Test Author"
    )

    private fun createValidBlogType() = BlogType(
        id = 1,
        name = "Test Type",
        urlSlug = "test-type",
        isDeleted = false
    )

    private fun createValidBlogCategory() = BlogCategory(
        id = 1,
        name = "Test Category",
        urlSlug = "test-category",
        isDeleted = false
    )

    private fun createValidBlog() = Blog(
        id = 1,
        title = "Test Blog",
        content = "Test content",
        keyword = "test",
        thumbnailUrl = "https://example.com/thumb.jpg",
        isPublished = true,
        isDeleted = false,
        blogTypeId = 1,
        description = "Test description",
        urlSlug = "test-blog",
        author = "Test Author"
    )

    // Blog CRUD Tests
    @Test
    fun `createBlog should create blog successfully when valid request`() {
        // Given
        val blogRequest = createValidBlogRequest()
        val blogType = createValidBlogType()
        val category = createValidBlogCategory()

        every { blogRepository.findBlogDuplicatedByTitle(blogRequest.title) } returns Optional.empty()
        every { blogRepository.findBlogDuplicatedBySlug(blogRequest.urlSlug) } returns Optional.empty()
        every { blogTypeRepository.findById(blogRequest.blogTypeId) } returns Optional.of(blogType)
        every { blogCategoryRepository.findById(any()) } returns Optional.of(category)
        every { blogRepository.save(any<Blog>()) } returns createValidBlog()

        // When & Then
        blogService.createBlog(blogRequest)

        verify { blogRepository.save(any<Blog>()) }
    }

    @Test
    fun `createBlog should throw BadRequestException when title is duplicated`() {
        // Given
        val blogRequest = createValidBlogRequest()
        val existingBlog = createValidBlog()

        every { blogRepository.findBlogDuplicatedByTitle(blogRequest.title) } returns Optional.of(existingBlog)

        // When & Then
        val exception = assertThrows<BadRequestException> {
            blogService.createBlog(blogRequest)
        }
        assertEquals("ไม่สามารถสร้างบทความได้ ชื่อบทความซ้ำ", exception.message)
    }

    @Test
    fun `createBlog should throw BadRequestException when urlSlug is duplicated`() {
        // Given
        val blogRequest = createValidBlogRequest()
        val existingBlog = createValidBlog()

        every { blogRepository.findBlogDuplicatedByTitle(blogRequest.title) } returns Optional.empty()
        every { blogRepository.findBlogDuplicatedBySlug(blogRequest.urlSlug) } returns Optional.of(existingBlog)

        // When & Then
        val exception = assertThrows<BadRequestException> {
            blogService.createBlog(blogRequest)
        }
        assertEquals("ไม่สามารถสร้างบทความได้ URL Slug ซ้ำ", exception.message)
    }

    @Test
    fun `createBlog should throw NotFoundException when blog type not found`() {
        // Given
        val blogRequest = createValidBlogRequest()

        every { blogRepository.findBlogDuplicatedByTitle(blogRequest.title) } returns Optional.empty()
        every { blogRepository.findBlogDuplicatedBySlug(blogRequest.urlSlug) } returns Optional.empty()
        every { blogTypeRepository.findById(blogRequest.blogTypeId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.createBlog(blogRequest)
        }
        assertEquals("ไม่พบประเภทบทความ", exception.message)
    }

    @Test
    fun `createBlog should throw BadRequestException when blog type is deleted`() {
        // Given
        val blogRequest = createValidBlogRequest()
        val deletedBlogType = createValidBlogType().copy(isDeleted = true)

        every { blogRepository.findBlogDuplicatedByTitle(blogRequest.title) } returns Optional.empty()
        every { blogRepository.findBlogDuplicatedBySlug(blogRequest.urlSlug) } returns Optional.empty()
        every { blogTypeRepository.findById(blogRequest.blogTypeId) } returns Optional.of(deletedBlogType)

        // When & Then
        val exception = assertThrows<BadRequestException> {
            blogService.createBlog(blogRequest)
        }
        assertEquals("ไม่สามารถสร้างบทความได้ ประเภทบทความถูกลบแล้ว", exception.message)
    }

    @Test
    fun `createBlog should throw NotFoundException when category not found`() {
        // Given
        val blogRequest = createValidBlogRequest()
        val blogType = createValidBlogType()

        every { blogRepository.findBlogDuplicatedByTitle(blogRequest.title) } returns Optional.empty()
        every { blogRepository.findBlogDuplicatedBySlug(blogRequest.urlSlug) } returns Optional.empty()
        every { blogTypeRepository.findById(blogRequest.blogTypeId) } returns Optional.of(blogType)
        every { blogCategoryRepository.findById(any()) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.createBlog(blogRequest)
        }
        assertEquals("ไม่พบหมวดหมู่บทความ", exception.message)
    }

    @Test
    fun `createBlog should throw BadRequestException when category is deleted`() {
        // Given
        val blogRequest = createValidBlogRequest()
        val blogType = createValidBlogType()
        val deletedCategory = createValidBlogCategory().copy(isDeleted = true)

        every { blogRepository.findBlogDuplicatedByTitle(blogRequest.title) } returns Optional.empty()
        every { blogRepository.findBlogDuplicatedBySlug(blogRequest.urlSlug) } returns Optional.empty()
        every { blogTypeRepository.findById(blogRequest.blogTypeId) } returns Optional.of(blogType)
        every { blogCategoryRepository.findById(any()) } returns Optional.of(deletedCategory)

        // When & Then
        val exception = assertThrows<BadRequestException> {
            blogService.createBlog(blogRequest)
        }
        assertEquals("ไม่สามารถสร้างบทความได้ หมวดหมู่ถูกลบแล้ว", exception.message)
    }

    @Test
    fun `updateBlog should update blog successfully when valid request`() {
        // Given
        val blogId = 1
        val blogRequest = createValidBlogRequest()
        val existingBlog = createValidBlog()
        val blogType = createValidBlogType()
        val category = createValidBlogCategory()

        every { blogRepository.findById(blogId) } returns Optional.of(existingBlog)
        every { blogRepository.findBlogDuplicatedByTitleAndId(blogRequest.title, blogId) } returns Optional.empty()
        every { blogRepository.findBlogDuplicatedBySlugAndId(blogRequest.urlSlug, blogId) } returns Optional.empty()
        every { blogTypeRepository.findById(blogRequest.blogTypeId) } returns Optional.of(blogType)
        every { blogCategoryRepository.findById(any()) } returns Optional.of(category)
        every { blogRepository.save(any<Blog>()) } returns existingBlog
        every { categoriesMappingRepository.deleteAllByBlogId(blogId) } just Runs
        every { categoriesMappingRepository.saveAll(any<List<CategoriesMapping>>()) } returns emptyList()

        // When
        blogService.updateBlog(blogId, blogRequest)

        // Then
        verify { blogRepository.save(any<Blog>()) }
        verify { categoriesMappingRepository.deleteAllByBlogId(blogId) }
        verify { categoriesMappingRepository.saveAll(any<List<CategoriesMapping>>()) }
    }

    @Test
    fun `updateBlog should throw NotFoundException when blog not found`() {
        // Given
        val blogId = 1
        val blogRequest = createValidBlogRequest()

        every { blogRepository.findById(blogId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.updateBlog(blogId, blogRequest)
        }
        assertEquals("ไม่พบบทความ", exception.message)
    }

    @Test
    fun `updateBlog should throw NotFoundException when blog is deleted`() {
        // Given
        val blogId = 1
        val blogRequest = createValidBlogRequest()
        val deletedBlog = createValidBlog().copy(isDeleted = true)

        every { blogRepository.findById(blogId) } returns Optional.of(deletedBlog)

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.updateBlog(blogId, blogRequest)
        }
        assertEquals("ไม่พบบทความ", exception.message)
    }

    @Test
    fun `deleteBlog should mark blog as deleted when valid id`() {
        // Given
        val blogId = 1
        val blog = createValidBlog()

        every { blogRepository.findById(blogId) } returns Optional.of(blog)
        every { blogRepository.save(any<Blog>()) } returns blog

        // When
        blogService.deleteBlog(blogId)

        // Then
        verify { blogRepository.save(any<Blog>()) }
    }

    @Test
    fun `deleteBlog should throw IllegalArgumentException when id is not positive`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.deleteBlog(0)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสบทความ ไม่ถูกต้อง", exception.message)
    }

    @Test
    fun `deleteBlog should throw NotFoundException when blog not found`() {
        // Given
        val blogId = 1

        every { blogRepository.findById(blogId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.deleteBlog(blogId)
        }
        assertEquals("ไม่พบบทความ", exception.message)
    }

    @Test
    fun `deleteBlog should throw NotFoundException when blog is already deleted`() {
        // Given
        val blogId = 1
        val deletedBlog = createValidBlog().copy(isDeleted = true)

        every { blogRepository.findById(blogId) } returns Optional.of(deletedBlog)

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.deleteBlog(blogId)
        }
        assertEquals("ไม่พบบทความ", exception.message)
    }

    @Test
    fun `getBlogs should delegate to repository`() {
        // Given
        val pageable: Pageable = PageRequest.of(0, 10)
        val type = 1
        val category = listOf(1, 2)
        val ascending = true
        val sortField = "title"
        val expectedPage: Page<BlogResponse> = PageImpl(emptyList())

        every {
            blogRepository.findAllBlog(pageable, type, category, ascending, sortField)
        } returns expectedPage

        // When
        val result = blogService.getBlogs(pageable, type, category, ascending, sortField)

        // Then
        assertEquals(expectedPage, result)
        verify { blogRepository.findAllBlog(pageable, type, category, ascending, sortField) }
    }

    @Test
    fun `getBlogAdmin should delegate to repository`() {
        // Given
        val pageable: Pageable = PageRequest.of(0, 10)
        val type = 1
        val category = listOf(1, 2)
        val ascending = true
        val sortField = "title"
        val expectedPage: Page<BlogResponse> = PageImpl(emptyList())

        every {
            blogRepository.getBlogAdmin(pageable, type, category, ascending, sortField)
        } returns expectedPage

        // When
        val result = blogService.getBlogAdmin(pageable, type, category, ascending, sortField)

        // Then
        assertEquals(expectedPage, result)
        verify { blogRepository.getBlogAdmin(pageable, type, category, ascending, sortField) }
    }

    @Test
    fun `getBlogByAdminId should return blog when valid id`() {
        // Given
        val blogId = 1
        val blog = createValidBlog()
        val expectedDto = mockk<BlogByIdDto>()

        every { blogRepository.findById(blogId) } returns Optional.of(blog)
        every { blogRepository.getBlogAdminById(blogId) } returns expectedDto

        // When
        val result = blogService.getBlogByAdminId(blogId)

        // Then
        assertEquals(expectedDto, result)
        verify { blogRepository.getBlogAdminById(blogId) }
    }

    @Test
    fun `getBlogByAdminId should throw IllegalArgumentException when id is not positive`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.getBlogByAdminId(0)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสบทความ ไม่ถูกต้อง", exception.message)
    }

    @Test
    fun `getBlogByAdminId should throw NotFoundException when blog not found`() {
        // Given
        val blogId = 1

        every { blogRepository.findById(blogId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.getBlogByAdminId(blogId)
        }
        assertEquals("ไม่พบบทความ", exception.message)
    }

    @Test
    fun `getBlogByAdminId should throw NotFoundException when blog is deleted`() {
        // Given
        val blogId = 1
        val deletedBlog = createValidBlog().copy(isDeleted = true)

        every { blogRepository.findById(blogId) } returns Optional.of(deletedBlog)

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.getBlogByAdminId(blogId)
        }
        assertEquals("ไม่พบบทความ", exception.message)
    }

    @Test
    fun `getBlogBySlug should return blog when valid slug`() {
        // Given
        val urlSlug = "test-blog-slug"
        val expectedDto = mockk<BlogBySlugDto>()

        every { blogRepository.getBlogBySlug(urlSlug) } returns expectedDto

        // When
        val result = blogService.getBlogBySlug(urlSlug)

        // Then
        assertEquals(expectedDto, result)
        verify { blogRepository.getBlogBySlug(urlSlug) }
    }

    @Test
    fun `getBlogBySlug should throw IllegalArgumentException when slug is blank`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.getBlogBySlug("")
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ URL Slug ไม่ถูกต้อง", exception.message)
    }

    // Blog Type Tests
    @Test
    fun `createBlogType should create blog type successfully when valid request`() {
        // Given
        val blogTypeRequest = BlogTypeRequest(
            name = "Test Type",
            urlSlug = "test-type"
        )

        every { blogTypeRepository.findBlogTypeByName(blogTypeRequest.name) } returns Optional.empty()
        every { blogTypeRepository.findBlogTypeByUrlSlug(blogTypeRequest.urlSlug) } returns Optional.empty()
        every { blogTypeRepository.save(any<BlogType>()) } returns createValidBlogType()

        // When
        blogService.createBlogType(blogTypeRequest)

        // Then
        verify { blogTypeRepository.save(any<BlogType>()) }
    }

    @Test
    fun `createBlogType should throw BadRequestException when name is duplicated`() {
        // Given
        val blogTypeRequest = BlogTypeRequest(
            name = "Test Type",
            urlSlug = "test-type"
        )
        val existingBlogType = createValidBlogType()

        every { blogTypeRepository.findBlogTypeByName(blogTypeRequest.name) } returns Optional.of(existingBlogType)

        // When & Then
        val exception = assertThrows<BadRequestException> {
            blogService.createBlogType(blogTypeRequest)
        }
        assertEquals("ไม่สามารถบันทึกประเภทบทความได้ ชื่อประเภทซ้ำ", exception.message)
    }

    @Test
    fun `createBlogType should throw BadRequestException when urlSlug is duplicated`() {
        // Given
        val blogTypeRequest = BlogTypeRequest(
            name = "Test Type",
            urlSlug = "test-type"
        )
        val existingBlogType = createValidBlogType()

        every { blogTypeRepository.findBlogTypeByName(blogTypeRequest.name) } returns Optional.empty()
        every { blogTypeRepository.findBlogTypeByUrlSlug(blogTypeRequest.urlSlug) } returns Optional.of(existingBlogType)

        // When & Then
        val exception = assertThrows<BadRequestException> {
            blogService.createBlogType(blogTypeRequest)
        }
        assertEquals("ไม่สามารถบันทึกประเภทบทความได้ URL Slug ซ้ำ", exception.message)
    }

    @Test
    fun `updateBlogType should update blog type successfully when valid request`() {
        // Given
        val blogTypeId = 1
        val blogTypeRequest = BlogTypeRequest(
            name = "Updated Type",
            urlSlug = "updated-type"
        )
        val existingBlogType = createValidBlogType()

        every { blogTypeRepository.findById(blogTypeId) } returns Optional.of(existingBlogType)
        every { blogTypeRepository.findBlogTypeByNameAndId(blogTypeRequest.name, blogTypeId) } returns Optional.empty()
        every { blogTypeRepository.findBlogTypeByUrlSlugAndId(blogTypeRequest.urlSlug, blogTypeId) } returns Optional.empty()
        every { blogTypeRepository.save(any<BlogType>()) } returns existingBlogType

        // When
        blogService.updateBlogType(blogTypeId, blogTypeRequest)

        // Then
        verify { blogTypeRepository.save(any<BlogType>()) }
    }

    @Test
    fun `updateBlogType should throw IllegalArgumentException when id is not positive`() {
        // Given
        val blogTypeRequest = BlogTypeRequest(
            name = "Test Type",
            urlSlug = "test-type"
        )

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.updateBlogType(0, blogTypeRequest)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสประเภทบทความ ไม่ถูกต้อง", exception.message)
    }

    @Test
    fun `updateBlogType should throw NotFoundException when blog type not found`() {
        // Given
        val blogTypeId = 1
        val blogTypeRequest = BlogTypeRequest(
            name = "Test Type",
            urlSlug = "test-type"
        )

        every { blogTypeRepository.findById(blogTypeId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.updateBlogType(blogTypeId, blogTypeRequest)
        }
        assertEquals("ไม่พบประเภทบทความ", exception.message)
    }

    @Test
    fun `getBlogTypes should delegate to repository`() {
        // Given
        val expectedTypes = listOf<BlogTypeResponse>()

        every { blogRepository.findAllBlogType() } returns expectedTypes

        // When
        val result = blogService.getBlogTypes()

        // Then
        assertEquals(expectedTypes, result)
        verify { blogRepository.findAllBlogType() }
    }

    @Test
    fun `getBlogTypeById should return blog type when valid id`() {
        // Given
        val blogTypeId = 1
        val expectedResponse = mockk<BlogTypeResponse>()

        every { blogRepository.getBlogTypeById(blogTypeId) } returns expectedResponse

        // When
        val result = blogService.getBlogTypeById(blogTypeId)

        // Then
        assertEquals(expectedResponse, result)
        verify { blogRepository.getBlogTypeById(blogTypeId) }
    }

    @Test
    fun `getBlogTypeById should throw IllegalArgumentException when id is not positive`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.getBlogTypeById(0)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสประเภทบทความ ไม่ถูกต้อง", exception.message)
    }

    // Category Tests
    @Test
    fun `createCategory should create category successfully when valid request`() {
        // Given
        val categoryRequest = CategoriesRequest(
            name = "Test Category",
            urlSlug = "test-category"
        )

        every { categoriesRepository.findByCategoryByName(categoryRequest.name) } returns Optional.empty()
        every { categoriesRepository.findByCategoryByUrlSlug(categoryRequest.urlSlug) } returns Optional.empty()
        every { categoriesRepository.save(any<BlogCategory>()) } returns createValidBlogCategory()

        // When
        blogService.createCategory(categoryRequest)

        // Then
        verify { categoriesRepository.save(any<BlogCategory>()) }
    }

    @Test
    fun `updateCategory should update category successfully when valid request`() {
        // Given
        val categoryId = 1
        val categoryRequest = CategoriesRequest(
            name = "Updated Category",
            urlSlug = "updated-category"
        )
        val existingCategory = createValidBlogCategory()

        every { categoriesRepository.findByIdAndIsDelete(categoryId) } returns Optional.of(existingCategory)
        every { categoriesRepository.findByCategoryByNameAndId(categoryRequest.name, categoryId) } returns Optional.empty()
        every { categoriesRepository.findByCategoryByUrlSlugAndId(categoryRequest.urlSlug, categoryId) } returns Optional.empty()
        every { categoriesRepository.save(any<BlogCategory>()) } returns existingCategory

        // When
        blogService.updateCategory(categoryId, categoryRequest)

        // Then
        verify { categoriesRepository.save(any<BlogCategory>()) }
    }

    @Test
    fun `updateCategory should throw IllegalArgumentException when id is not positive`() {
        // Given
        val categoryRequest = CategoriesRequest(
            name = "Test Category",
            urlSlug = "test-category"
        )

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.updateCategory(0, categoryRequest)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสหมวดหมู่บทความ ไม่ถูกต้อง", exception.message)
    }

    @Test
    fun `updateCategory should throw NotFoundException when category not found`() {
        // Given
        val categoryId = 1
        val categoryRequest = CategoriesRequest(
            name = "Test Category",
            urlSlug = "test-category"
        )

        every { categoriesRepository.findByIdAndIsDelete(categoryId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.updateCategory(categoryId, categoryRequest)
        }
        assertEquals("ไม่พบหมวดหมู่บทความ", exception.message)
    }

    @Test
    fun `deleteCategory should soft delete when category is in use`() {
        // Given
        val categoryId = 1
        val category = createValidBlogCategory()

        every { blogCategoryRepository.findById(categoryId) } returns Optional.of(category)
        every { categoriesMappingRepository.existsByCategoryId(categoryId) } returns true
        every { blogCategoryRepository.save(any<BlogCategory>()) } returns category

        // When
        blogService.deleteCategory(categoryId)

        // Then
        verify { blogCategoryRepository.save(any<BlogCategory>()) }
    }

    @Test
    fun `deleteCategory should hard delete when category is not in use`() {
        // Given
        val categoryId = 1
        val category = createValidBlogCategory()

        every { blogCategoryRepository.findById(categoryId) } returns Optional.of(category)
        every { categoriesMappingRepository.existsByCategoryId(categoryId) } returns false
        every { categoriesRepository.deleteById(categoryId) } just Runs

        // When
        blogService.deleteCategory(categoryId)

        // Then
        verify { categoriesRepository.deleteById(categoryId) }
    }

    @Test
    fun `deleteCategory should throw IllegalArgumentException when id is not positive`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.deleteCategory(0)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสหมวดหมู่บทความ ไม่ถูกต้อง", exception.message)
    }

    @Test
    fun `deleteCategory should throw NotFoundException when category not found`() {
        // Given
        val categoryId = 1

        every { blogCategoryRepository.findById(categoryId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.deleteCategory(categoryId)
        }
        assertEquals("ไม่พบหมวดหมู่บทความ", exception.message)
    }

    @Test
    fun `getBlogCategories should delegate to repository`() {
        // Given
        val expectedCategories = listOf<CategoriesResponse>()

        every { blogRepository.findAllCategories() } returns expectedCategories

        // When
        val result = blogService.getBlogCategories()

        // Then
        assertEquals(expectedCategories, result)
        verify { blogRepository.findAllCategories() }
    }

    @Test
    fun `getCategoryById should return category when valid id`() {
        // Given
        val categoryId = 1
        val expectedResponse = mockk<CategoriesResponse>()

        every { blogRepository.getCategoryById(categoryId) } returns expectedResponse

        // When
        val result = blogService.getCategoryById(categoryId)

        // Then
        assertEquals(expectedResponse, result)
        verify { blogRepository.getCategoryById(categoryId) }
    }

    @Test
    fun `getCategoryById should throw IllegalArgumentException when id is not positive`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.getCategoryById(0)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสหมวดหมู่บทความ ไม่ถูกต้อง", exception.message)
    }

    // Gallery Tests
    @Test
    fun `createGallery should create gallery successfully when valid file`() {
        // Given
        val file = mockk<MultipartFile>()
        val uploadResult = S3Response(url = "https://example.com/image.jpg")
        val expectedGallery = Gallery(id = 1, url = "https://example.com/image.jpg")

        every { file.isEmpty } returns false
        every { s3Service.uploadFile(file) } returns uploadResult
        every { galleryRepository.save(any<Gallery>()) } returns expectedGallery

        // When
        val result = blogService.createGallery(file)

        // Then
        assertEquals(expectedGallery, result)
        verify { s3Service.uploadFile(file) }
        verify { galleryRepository.save(any<Gallery>()) }
    }

    @Test
    fun `createGallery should throw BadRequestException when file is empty`() {
        // Given
        val file = mockk<MultipartFile>()

        every { file.isEmpty } returns true

        // When & Then
        val exception = assertThrows<BadRequestException> {
            blogService.createGallery(file)
        }
        assertEquals("ไม่สามารถอัพโหลดไฟล์ได้ กรุณาเลือกไฟล์", exception.message)
    }

    @Test
    fun `getGalleries should delegate to repository`() {
        // Given
        val pageable: Pageable = PageRequest.of(0, 10)
        val expectedPage: Page<BlogGalleryResponse> = PageImpl(emptyList())

        every { blogRepository.getGalleries(pageable) } returns expectedPage

        // When
        val result = blogService.getGalleries(pageable)

        // Then
        assertEquals(expectedPage, result)
        verify { blogRepository.getGalleries(pageable) }
    }

    @Test
    fun `deleteGallery should mark gallery as deleted when valid id`() {
        // Given
        val galleryId = 1
        val gallery = Gallery(id = galleryId, url = "https://example.com/image.jpg")

        every { galleryRepository.findById(galleryId) } returns Optional.of(gallery)
        every { galleryRepository.save(any<Gallery>()) } returns gallery

        // When
        blogService.deleteGallery(galleryId)

        // Then
        verify { galleryRepository.save(any<Gallery>()) }
    }

    @Test
    fun `deleteGallery should throw IllegalArgumentException when id is not positive`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.deleteGallery(0)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสแกลเลอรี่ ไม่ถูกต้อง", exception.message)
    }

    @Test
    fun `deleteGallery should throw NotFoundException when gallery not found`() {
        // Given
        val galleryId = 1

        every { galleryRepository.findById(galleryId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.deleteGallery(galleryId)
        }
        assertEquals("ไม่พบแกลเลอรี่", exception.message)
    }

    // Additional validation tests
    @Test
    fun `deleteBlogType should mark blog type as deleted when valid id and in use`() {
        // Given
        val blogTypeId = 1
        val blogType = createValidBlogType()
        val blogsUsingType = listOf(createValidBlog())

        every { blogTypeRepository.findByIdAndIsDelete(blogTypeId) } returns Optional.of(blogType)
        every { blogRepository.findByTypeId(blogTypeId) } returns blogsUsingType
        every { blogTypeRepository.save(any<BlogType>()) } returns blogType

        // When
        blogService.deleteBlogType(blogTypeId)

        // Then
        verify { blogTypeRepository.save(any<BlogType>()) }
    }

    @Test
    fun `deleteBlogType should hard delete when not in use`() {
        // Given
        val blogTypeId = 1
        val blogType = createValidBlogType()

        every { blogTypeRepository.findByIdAndIsDelete(blogTypeId) } returns Optional.of(blogType)
        every { blogRepository.findByTypeId(blogTypeId) } returns emptyList()
        every { blogTypeRepository.deleteById(blogTypeId) } just Runs

        // When
        blogService.deleteBlogType(blogTypeId)

        // Then
        verify { blogTypeRepository.deleteById(blogTypeId) }
    }

    @Test
    fun `deleteBlogType should throw IllegalArgumentException when id is not positive`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            blogService.deleteBlogType(0)
        }
        assertEquals("ไม่สามารถแสดงข้อมูลได้ รหัสประเภทบทความ ไม่ถูกต้อง", exception.message)
    }

    @Test
    fun `deleteBlogType should throw NotFoundException when blog type not found`() {
        // Given
        val blogTypeId = 1

        every { blogTypeRepository.findByIdAndIsDelete(blogTypeId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<NotFoundException> {
            blogService.deleteBlogType(blogTypeId)
        }
        assertEquals("ไม่พบประเภทบทความ", exception.message)
    }
}
