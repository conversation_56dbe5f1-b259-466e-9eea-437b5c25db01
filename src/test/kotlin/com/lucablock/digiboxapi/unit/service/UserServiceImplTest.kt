package com.lucablock.digiboxapi.unit.service

import com.google.gson.Gson
import com.lucablock.digiboxapi.app.utility.decodeThailandCharacters
import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.email.service.EmailService
import com.lucablock.digiboxapi.entity.Address
import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.oauth2.dto.AuthProvider
import com.lucablock.digiboxapi.token.JWTTokenProvider
import com.lucablock.digiboxapi.token.dto.ForgotTokenDto
import com.lucablock.digiboxapi.token.dto.TokenDto
import com.lucablock.digiboxapi.token.service.TokenService
import com.lucablock.digiboxapi.user.dto.EmailForgotPasswordDto
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.repository.AddressRepository
import com.lucablock.digiboxapi.user.repository.UserRepository
import com.lucablock.digiboxapi.user.request.CreateUserAddressRequest
import com.lucablock.digiboxapi.user.request.CreateUserRequest
import com.lucablock.digiboxapi.user.request.UpdateProfileRequest
import com.lucablock.digiboxapi.user.service.impl.UserServiceImpl
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.web.multipart.MultipartFile
import java.util.*

class UserServiceImplTest {

  private lateinit var userService: UserServiceImpl
  private val userRepository = mockk<UserRepository>()
  private val addressRepository = mockk<AddressRepository>()
  private val bCryptPasswordEncoder = mockk<BCryptPasswordEncoder>()
  private val jwtTokenProvider = mockk<JWTTokenProvider>()
  private val authenticationManager = mockk<AuthenticationManager>()
  private val tokenService = mockk<TokenService>()
  private val emailService = mockk<EmailService>()
  private val s3Service = mockk<S3Service>()
  private val gson = mockk<Gson>()

  @BeforeEach
  fun setUp() {
    userService = UserServiceImpl(
      authenticationManager,
      userRepository,
      bCryptPasswordEncoder,
      jwtTokenProvider,
      tokenService,
      addressRepository,
      emailService as JavaMailSender,
      templateEngine = mockk(),
      s3Service,
    )
    clearAllMocks()
  }

  @Test
  fun `createUser should create user and send verification email when valid input`() {
    // Arrange
    val createUserRequest = CreateUserRequest(
      email = "<EMAIL>",
      password = "password123",
      firstname = "John",
      lastname = "Doe",
      username = "johndoe"
    )
    val userType = 1L
    val host = "http://localhost"
    val user = User(
      id = 1L,
      email = createUserRequest.email,
      password = "encodedPassword",
      userTypeId = userType,
      provider = AuthProvider.local.toString(),
      emailVerified = false,
      phoneVerified = false,
      firstName = createUserRequest.firstname,
      lastName = createUserRequest.lastname,
      fullName = "${createUserRequest.firstname} ${createUserRequest.lastname}",
      username = createUserRequest.username
    )
    val userPrincipal = UserPrincipal().apply {
      setUserId(user.id)
      setEmail(user.email)
      setUsername(user.username)
      setPassword(user.password)
      setUserTypeId(user.userTypeId)
      setEmailVerify(user.emailVerified)
      setUserType("USER") // Assuming "USER" is the default user type
    }
    val token = "jwtToken"

    every { userRepository.findByEmail(createUserRequest.email) } returns Optional.empty()
    every { bCryptPasswordEncoder.encode(createUserRequest.password) } returns "encodedPassword"
    every { userRepository.save(any<User>()) } returns user
    every {
      authenticationManager.authenticate(
        UsernamePasswordAuthenticationToken(createUserRequest.email, createUserRequest.password)
      )
    } returns mockk {
      every { principal } returns userPrincipal
    }
    every { jwtTokenProvider.generateToken(userPrincipal) } returns token
    every { tokenService.cacheToken(any()) } just Runs
    every { userRepository.getUserDetailById(user.id) } returns mockk {
      every { isUser } returns true
    }
    every { jwtTokenProvider.generateToken(userPrincipal) } returns token
    every { emailService.sendVerificationEmail(userPrincipal, token, host) } just Runs

    // Act
    userService.createUser(createUserRequest, userType, host)

    // Assert
    verify {
      userRepository.save(match {
        it.email == createUserRequest.email &&
            it.firstName == createUserRequest.firstname &&
            it.lastName == createUserRequest.lastname &&
            it.fullName == "${createUserRequest.firstname} ${createUserRequest.lastname}"
      })
      tokenService.cacheToken(
        TokenDto(
          userId = user.id.toString(),
          token = token,
          multipleLogin = true
        )
      )
      emailService.sendVerificationEmail(userPrincipal, token, host)
    }
  }

  @Test
  fun `createUser should throw BadRequestException when email is invalid`() {
    // Arrange
    val createUserRequest = CreateUserRequest(
      email = "invalid-email",
      password = "password123",
      firstname = "John",
      lastname = "Doe",
      username = "johndoe"
    )

    // Act & Assert
    assertThrows<BadRequestException> {
      userService.createUser(createUserRequest, 1L, "http://localhost")
    }.also {
      assertEquals("สร้างบัญชีไม่สำเร็จ รูปแบบอีเมลไม่ถูกต้อง", it.message)
    }
  }

  @Test
  fun `createUser should throw BadRequestException when email already exists`() {
    // Arrange
    val createUserRequest = CreateUserRequest(
      email = "<EMAIL>",
      password = "password123",
      firstname = "John",
      lastname = "Doe",
      username = "johndoe"
    )
    every { userRepository.findByEmail(createUserRequest.email) } returns Optional.of(mockk())

    // Act & Assert
    assertThrows<BadRequestException> {
      userService.createUser(createUserRequest, 1L, "http://localhost")
    }.also {
      assertEquals("สร้างบัญชีไม่สำเร็จ อีเมลนี้ถูกใช้แล้ว", it.message)
    }
  }

  @Test
  fun `updateUserMe should update user profile and upload new image`() {
    // Arrange
    val userId = 1L
    val user = User(
      id = userId,
      email = "<EMAIL>",
      firstName = "Old",
      lastName = "User",
      fullName = "Old User",
      phoneNumber = "**********",
      imageUrl = "old-image-url",
      provider = "local"
    )
    val updateProfileRequest = UpdateProfileRequest(
      firstname = "New",
      lastname = "User",
      phoneNumber = "**********"
    )
    val file = mockk<MultipartFile>()
    val jsonRequest = """{"firstname":"New","lastname":"User","phoneNumber":"**********"}"""
    val newImageUrl = "new-image-url"

    every { userRepository.findById(userId) } returns Optional.of(user)
    every {
      gson.fromJson(
        jsonRequest.decodeThailandCharacters(),
        UpdateProfileRequest::class.java
      )
    } returns updateProfileRequest
    every { s3Service.deleteFile("old-image-url") } just Runs
    every { s3Service.uploadFile(file) } returns mockk { every { url } returns newImageUrl }
    every { userRepository.save(any<User>()) } returns user

    // Act
    userService.updateUserMe(userId, jsonRequest, file)

    // Assert
    verify {
      userRepository.save(match {
        it.firstName == "New" &&
            it.lastName == "User" &&
            it.fullName == "New User" &&
            it.phoneNumber == "**********" &&
            it.imageUrl == newImageUrl
      })
      s3Service.deleteFile("old-image-url")
      s3Service.uploadFile(file)
    }
  }

  @Test
  fun `updateUserMe should throw NotFoundException when user not found`() {
    // Arrange
    val userId = 1L
    every { userRepository.findById(userId) } returns Optional.empty()

    // Act & Assert
    assertThrows<NotFoundException> {
      userService.updateUserMe(userId, "{}", null)
    }.also {
      assertEquals("ไม่มีบัญชีผู้ใช้งาน", it.message)
    }
  }

  @Test
  fun `emailForgotPassword should send reset email when user exists`() {
    // Arrange
    val email = "<EMAIL>"
    val user = User(id = 1L, email = email)
    val token = "reset-token"

    every { userRepository.findByEmail(email) } returns Optional.of(user)
    every { jwtTokenProvider.generateTokenForgotEmail(user) } returns token
    every { tokenService.cacheForgotToken(ForgotTokenDto(user.id.toString(), token)) } just Runs
    every { emailService.sendPasswordResetEmail(user, token) } just Runs

    // Act
    userService.emailForgotPassword(EmailForgotPasswordDto(email))

    // Assert
    verify {
      tokenService.cacheForgotToken(ForgotTokenDto(user.id.toString(), token))
      emailService.sendPasswordResetEmail(user, token)
    }
  }

  @Test
  fun `emailForgotPassword should throw NotFoundException when email not found`() {
    // Arrange
    val email = "<EMAIL>"
    every { userRepository.findByEmail(email) } returns Optional.empty()

    // Act & Assert
    assertThrows<NotFoundException> {
      userService.emailForgotPassword(EmailForgotPasswordDto(email))
    }.also {
      assertEquals("ไม่พบอีเมล", it.message)
    }
  }

  @Test
  fun `createAddress should create address and set as default if no default exists`() {
    // Arrange
    val userId = 1
    val createUserAddressRequest = CreateUserAddressRequest(
      name = "Test Address",
      phoneNumber = "**********",
      address = "123 Street",
      zipCode = "12345",
      province = "Province",
      district = "District",
      subDistrict = "SubDistrict",
      isTax = false,
      taxPayerType = null,
      taxId = null,
      email = "<EMAIL>",
      isDefault = false
    )
    val address = Address(
      userId = userId,
      name = createUserAddressRequest.name,
      phoneNumber = createUserAddressRequest.phoneNumber,
      address = createUserAddressRequest.address,
      zipCode = createUserAddressRequest.zipCode,
      province = createUserAddressRequest.province,
      district = createUserAddressRequest.district,
      subDistrict = createUserAddressRequest.subDistrict,
      isTax = createUserAddressRequest.isTax,
      taxPayerType = createUserAddressRequest.taxPayerType,
      taxId = createUserAddressRequest.taxId,
      email = createUserAddressRequest.email,
      isDefault = true
    )

    every { addressRepository.findDefaultAddressByUserId(userId, false) } returns null
    every { addressRepository.save(any<Address>()) } returns address

    // Act
    userService.createAddress(userId, createUserAddressRequest)

    // Assert
    verify {
      addressRepository.save(match {
        it.isDefault && it.name == createUserAddressRequest.name
      })
    }
  }

  @Test
  fun `deleteAddress should delete address and set new default if deleted address was default`() {
    // Arrange
    val userId = 1
    val addressId = 1
    val address = Address(
      id = addressId,
      userId = userId,
      isDefault = true,
      isTax = false,
      name = "Test Address",
      phoneNumber = "**********",
      address = "123 Street",
      zipCode = "12345",
      province = "Province",
      district = "District",
      subDistrict = "SubDistrict",
      email = "<EMAIL>"
    )
    val newDefaultAddress = Address(
      id = 2,
      userId = userId,
      isDefault = false,
      isTax = false,
      name = "New Default Address",
      phoneNumber = "**********",
      address = "456 Avenue",
      zipCode = "67890",
      province = "NewProvince",
      district = "NewDistrict",
      subDistrict = "NewSubDistrict",
      email = "<EMAIL>"
    )

    every { addressRepository.findById(addressId) } returns Optional.of(address)
    every { addressRepository.findNotDefaultByUserId(userId, false) } returns listOf(
      newDefaultAddress
    )
    every { addressRepository.findById(2) } returns Optional.of(newDefaultAddress)
    every { addressRepository.save(any<Address>()) } returns newDefaultAddress
    every { addressRepository.deleteById(addressId) } just Runs

    // Act
    userService.deleteAddress(userId, addressId)

    // Assert
    verify {
      addressRepository.save(match { it.id == 2 && it.isDefault })
      addressRepository.deleteById(addressId)
    }
  }

  @Test
  fun `deleteAddress should throw NotFoundException when address not found`() {
    // Arrange
    val userId = 1
    val addressId = 1
    every { addressRepository.findById(addressId) } returns Optional.empty()

    // Act & Assert
    assertThrows<NotFoundException> {
      userService.deleteAddress(userId, addressId)
    }.also {
      assertEquals("ไม่มีข้อมูลการจัดส่ง", it.message)
    }
  }
}