package com.lucablock.digiboxapi.thaiAddress.repository

import com.lucablock.digiboxapi.entity.Zipcode
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.util.*

interface ZipcodeRepository: JpaRepository<Zipcode, Long> {
  fun findBySubDistrictId(subDistrictId: Long): Optional<Zipcode>

  @Query(value = "SELECT * FROM zipcode WHERE zipcode = :zipcode LIMIT 1", nativeQuery = true)
  fun findFirstByZipcode(@Param("zipcode") zipcode: String): Optional<Zipcode>
  fun findByProvinceIdAndDistrictIdAndSubDistrictIdAndZipcode(provinceId: Long, districtId: Long, subDistrictId: Long, zipcode: String): Optional<Zipcode>
  fun existsByZipcode(zipcode: String): <PERSON><PERSON><PERSON>
}