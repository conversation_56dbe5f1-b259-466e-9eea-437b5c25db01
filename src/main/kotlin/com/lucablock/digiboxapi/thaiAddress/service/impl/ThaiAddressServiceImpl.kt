package com.lucablock.digiboxapi.thaiAddress.service.impl

import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.thaiAddress.dto.*
import com.lucablock.digiboxapi.thaiAddress.repository.DistrictRepository
import com.lucablock.digiboxapi.thaiAddress.repository.ProvinceRepository
import com.lucablock.digiboxapi.thaiAddress.repository.SubDistrictRepository
import com.lucablock.digiboxapi.thaiAddress.repository.ZipcodeRepository
import com.lucablock.digiboxapi.thaiAddress.service.ThaiAddressService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ThaiAddressServiceImpl @Autowired internal constructor(
  private val provinceRepository: ProvinceRepository,
  private val districtRepository: DistrictRepository,
  private val subDistrictRepository: SubDistrictRepository,
  private val zipcodeRepository: ZipcodeRepository,
) : ThaiAddressService {

  override fun getProvince(): List<ProvinceDto> {
    return provinceRepository.findAll()
      .sortedBy { it.name }
      .map { province ->
        ProvinceDto(
          id = province.id,
          name = province.name,
          geoId = province.geoId,
          provinceCode = province.provinceCode
        )
      }
  }

  override fun getDistrict(provinceId: Long): List<DistrictDto> {
    return districtRepository.findByProvinceId(provinceId)
      .sortedBy { it.name }
      .map { district ->
        DistrictDto(
          id = district.id,
          name = district.name,
          geoId = district.geoId,
          districtCode = district.districtCode
        )
      }
  }

  override fun getSubDistrict(districtId: Long): List<SubDistrictDto> {
    return subDistrictRepository.findByDistrictId(districtId)
      .sortedBy { it.name }
      .map { subDistrict ->
        SubDistrictDto(
          id = subDistrict.id,
          name = subDistrict.name,
          geoId = subDistrict.geoId,
          subDistrictCode = subDistrict.subDistrictCode
        )
      }
  }

  override fun getZipcode(subDistrictId: Long): ZipcodeDto {
    return zipcodeRepository.findBySubDistrictId(subDistrictId)
      .map { zipcode ->
        ZipcodeDto(
          id = zipcode.id,
          zipcode = zipcode.zipcode,
          districtId = zipcode.districtId,
          provinceId = zipcode.provinceId,
          subDistrictId = zipcode.subDistrictId
        )
      }.orElseThrow { NotFoundException("ไม่พบรหัสไปรษณีย์") }
  }

  override fun getZipcodeByZipcode(zipcode: String): ZipcodeByZipcodeDto {
    return zipcodeRepository.findFirstByZipcode(zipcode)
      .map { zipcode ->
        ZipcodeByZipcodeDto(
          id = zipcode.id,
          zipcode = zipcode.zipcode,
          districtId = zipcode.district?.let { district ->
            DistrictDto(
              id = district.id,
              geoId = district.geoId,
              districtCode = district.districtCode,
              name = district.name
            )
          },
          provinceId = zipcode.province?.let { province ->
            ProvinceDto(
              id = province.id,
              geoId = province.geoId,
              provinceCode = province.provinceCode,
              name = province.name
            )
          }
        )
      }.orElseThrow { NotFoundException("ไม่พบรหัสไปรษณีย์") }
  }
}
