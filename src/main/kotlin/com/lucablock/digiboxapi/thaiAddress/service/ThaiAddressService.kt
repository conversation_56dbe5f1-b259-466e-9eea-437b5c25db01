package com.lucablock.digiboxapi.thaiAddress.service

import com.lucablock.digiboxapi.thaiAddress.dto.*

interface ThaiAddressService {
  fun getProvince(): List<ProvinceDto>

  fun getDistrict(provinceId: Long): List<DistrictDto>

  fun getSubDistrict(districtId: Long): List<SubDistrictDto>

  fun getZipcode(subDistrictId: Long): ZipcodeDto

  fun getZipcodeByZipcode(zipcode: String): ZipcodeByZipcodeDto
}