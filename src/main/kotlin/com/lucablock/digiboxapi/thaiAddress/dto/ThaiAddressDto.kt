package com.lucablock.digiboxapi.thaiAddress.dto

import com.querydsl.core.annotations.QueryProjection

data class ProvinceDto
@QueryProjection
constructor(
    val id: Long,
    val geoId: Int,
    val provinceCode: String,
    val name: String,
)

data class DistrictDto
@QueryProjection
constructor(
    val id: Long,
    val geoId: Int,
    val districtCode: String,
    val name: String,
)

data class SubDistrictDto
@QueryProjection
constructor(
    val id: Long,
    val geoId: Int,
    val subDistrictCode: String,
    val name: String,
)

data class ZipcodeDto
@QueryProjection
constructor(
    val id: Long,
    val zipcode: String,
    val districtId: Long,
    val provinceId: Long,
    val subDistrictId: Long,
)

data class ZipcodeByZipcodeDto
@QueryProjection
constructor(
    val id: Long,
    val zipcode: String,
    val districtId: DistrictDto?,
    val provinceId: ProvinceDto?,
)