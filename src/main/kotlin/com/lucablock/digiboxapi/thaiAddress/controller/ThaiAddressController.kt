package com.lucablock.digiboxapi.thaiAddress.controller

import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.thaiAddress.service.ThaiAddressService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/thai-address")
class ThaiAddressController {
  private val logger: Logger = LoggerFactory.getLogger(ThaiAddressController::class.java)

  @Autowired
  lateinit var thaiAddressService: ThaiAddressService

  @GetMapping("/province")
  fun getListProvince(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหาจังหวัดสำเร็จ",
          thaiAddressService.getProvince(),
        )
      )
    } catch (e: Exception) {
      logger.error("Get Province Failure : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/district/{provinceId}")
  fun getListDistrict(
    @PathVariable("provinceId") provinceId: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหาอำเภอ หรือเขต ด้วยจังหวัดสำเร็จ",
          thaiAddressService.getDistrict(
            provinceId
          ),
        )
      )
    } catch (e: Exception) {
      logger.error("Get District By Province ID Failure : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @GetMapping("/sub-district/{districtId}")
  fun getListSubDistrict(
    @PathVariable("districtId") districtId: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหาตำบล หรือแขวง สำเร็จ",
          thaiAddressService.getSubDistrict(
            districtId
          ),
        )
      )
    } catch (e: Exception) {
      logger.error("Get Sub District By District ID Failure : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @GetMapping("/zipcode/{subDistrictId}")
  fun getZipcode(
    @PathVariable("subDistrictId") subDistrictId: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหารหัสไปรษณีย์ด้วยตำบลสำเร็จ",
          thaiAddressService.getZipcode(
            subDistrictId
          ),
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Get Zipcode By Sub District ID Failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบรหัสไปรษณีย์"))
    } catch (e: Exception) {
      logger.error("Get Zipcode By Sub District ID Failure : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @GetMapping("/zipcode-by-zipcode/{zipcode}")
  fun getZipcodeByZipcode(
    @PathVariable("zipcode") zipcode: String,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหารหัสไปรษณีย์ด้วยรหัสไปรษณีย์สำเร็จ",
          thaiAddressService.getZipcodeByZipcode(
            zipcode
          ),
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Get Zipcode By Sub District ID Failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบรหัสไปรษณีย์"))
    } catch (e: Exception) {
      logger.error("Get Zipcode By Zipcode Failure : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }
}