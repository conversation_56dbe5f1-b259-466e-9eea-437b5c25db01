package com.lucablock.digiboxapi.modelsize.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.lucablock.digiboxapi.modelsizeconfig.request.ModelSizeConfigRequest
import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive


class ModelSizeRequest {
  @NotNull(message = "กรุณาระบุรหัสโมเดลสินค้า")
  var modelId: Long = 0

  @NotNull(message = "กรุณาระบุรหัสขนาดกางออก")
  var unfoldedSizeId: Long = 0

  @NotNull(message = "กรุณาระบุความกว้าง")
  @Positive(message = "กรุณาระบุความกว้างให้ถูกต้อง")
  var width: Double = 0.0

  @NotNull(message = "กรุณาระบุความสูง")
  @Positive(message = "กรุณาระบุความสูงให้ถูกต้อง")
  var height: Double = 0.0

  var length: Double = 0.0
  @NotNull(message = "กรุณาระบุความสูงของขนาดกางออก")
  var unfoldedHeight : Double = 0.0
  @NotNull(message = "กรุณาระบุความกว้างของขนาดกางออก")
  var unfoldedWidth : Double = 0.0
  @JsonProperty("isThreeD")
  var isThreeD: Boolean = true

//  @NotEmpty(message = "กรุณาระบุขนาดโมเดลสินค้า")
//  @Valid
//  var modelSizeConfig: List<ModelSizeConfigRequest> = mutableListOf()
}