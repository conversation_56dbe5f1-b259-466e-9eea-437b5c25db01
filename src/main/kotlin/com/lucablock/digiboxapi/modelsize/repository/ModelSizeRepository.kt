package com.lucablock.digiboxapi.modelsize.repository

import com.lucablock.digiboxapi.entity.ModelSize
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ModelSizeRepository : JpaRepository<ModelSize, Long>, ModelSizeRepositoryCustom {
  fun findByModelId(modelId: Long): MutableList<ModelSize>
  fun findAllByWidthAndModelId(width: Double, modelId: Long): MutableList<ModelSize>
  fun findAllByWidthAndHeightAndLengthAndModelIdAndIsThreeD(
    width: Double,
    height: Double,
    length: Double,
    modelId: Long,
    isThreeD: Boolean
  ): MutableList<ModelSize>

  fun findAllByWidthAndHeightAndModelId(
    width: Double,
    height: Double,
    modelId: Long
  ): MutableList<ModelSize>

  fun findAllByWidthAndHeightAndLengthAndModelIdAndIsThreeDAndIdNot(
    width: Double,
    height: Double,
    length: Double,
    modelId: Long,
    isThreeD: Boolean,
    id: Long
  ): MutableList<ModelSize>

  fun findAllByModelId(modelId: Long): MutableList<ModelSize>
  fun findAllByUnfoldedSizeIdIn(unfoldedSizeIds: MutableCollection<Long>): MutableList<ModelSize>
  fun findAllByUnfoldedSizeId(unfoldedSizeId: Long): MutableList<ModelSize>
  fun findAllByUnfoldedSizeIdAndModelId(unfoldedSizeId: Long, modelId: Long): MutableList<ModelSize>
}