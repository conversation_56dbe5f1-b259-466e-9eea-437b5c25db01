package com.lucablock.digiboxapi.modelsize.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.model.request.ModelRequest
import com.lucablock.digiboxapi.modelsize.request.ModelSizeRequest
import com.lucablock.digiboxapi.modelsize.service.ModelSizeService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class ModelSizeController {
  private val logger: Logger = LoggerFactory.getLogger(ModelSizeController::class.java)

  @Autowired lateinit var modelSizeService: ModelSizeService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/model-size")
  fun createModelSize(
    @RequestBody @Valid modelSizeRequest: ModelSizeRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลขนาดโมเดลสินค้าสำเร็จ",
          modelSizeService.createModelSize(modelSizeRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("create model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่มีข้อมูลสินค้านี้"))
    } catch (e: BadRequestException) {
      logger.error("create model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message?: "มีโมเดลสินค้านี้อยู่แล้ว"))
    } catch (e: Exception) {
      logger.error("create model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถสร้างข้อมูลขนาดโมเดลสินค้า กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/model-size/{id}")
  fun updateModel(
    @PathVariable id: Long,
    @Valid @RequestBody modelSizeRequest: ModelSizeRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลขนาดโมเดลสินค้าสำเร็จ",
          modelSizeService.updateModelSize(id, modelSizeRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้านี้"))
    } catch (e: BadRequestException) {
      logger.error("update model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "มีข้อมูลโมเดลสินค้านี้อยู่แล้ว"))
    } catch (e: Exception) {
      logger.error("update model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลขนาดโมเดลสินค้า กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/model-size/model/{id}")
  fun findAllModelSizeByModelId(
    @PathVariable id: Long,
    ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลขนาดโมเดลด้วยรหัสโมเดลสินค้าสำเร็จ",
          modelSizeService.getModelSizeByModelId(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้านี้"))
    } catch (e: Exception) {
      logger.error("find model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/model-size/{id}")
  fun findAllModelSizeById(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลขนาดโมเดลสินค้าสำเร็จ",
          modelSizeService.getModelSizeById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้านี้"))
    } catch (e: Exception) {
      logger.error("find model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/model-size/{id}")
  fun deleteModelSizeById(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลขนาดโมเดลสินค้าสำเร็จ",
          modelSizeService.deleteModelSize(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้านี้"))
    } catch (e: Exception) {
      logger.error("delete model size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}