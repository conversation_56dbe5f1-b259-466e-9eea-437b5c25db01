package com.lucablock.digiboxapi.modelsize.service.impl

import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.modelsize.dto.*
import com.lucablock.digiboxapi.modelsize.repository.ModelSizeRepositoryCustom
import com.querydsl.core.types.Projections
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.stereotype.Service

@Service
class ModelSizeRepositoryImpl : ModelSizeRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  private val qModelSize = QModelSize.modelSize
  private val qModelSizeConfig = QModelSizeConfig.modelSizeConfig
  private val qPrinting = QPrinting.printing
  private val qPrintingConfig = QPrintingConfig.printingConfig
  private val qMaterial = QMaterials.materials
  private val qMaterialConfig = QMaterialConfig.materialConfig
  private val qGram = QGram.gram

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }


  override fun findAllModelSizeByModelId(modelId: Long): MutableList<ModelSizeCustomsDto> {
    val criteria = qModelSize.modelId.eq(modelId)
    val query = queryFactory

    val modelSize = query
      .select(
        Projections.constructor(
          ModelSizeCustomsQueryDto::class.java,
          qModelSize.id,
          qModelSize.modelId,
          qModelSize.unfoldedSizeId,
          qModelSize.unfoldedSize.name,
          qModelSize.unfoldedWidth,
          qModelSize.unfoldedHeight,
          qModelSize.width,
          qModelSize.height,
          qModelSize.length,
          qModelSize.isThreeD,
        )
      )
      .from(qModelSize)
      .where(criteria)
      .fetch()

    val modelSizeConfig = query
      .select(
        Projections.constructor(
          ModelSizeConfigListPrintCustomQueryDto::class.java,
          qModelSizeConfig.modelSizeId,
          qModelSizeConfig.id,
          qModelSizeConfig.materialConfigId,
          qMaterial.name,
          qMaterial.imageUrl,
          qGram.gsm,
          qGram.mm,
        )
      )
      .from(qModelSizeConfig)
      .leftJoin(qMaterialConfig).on(qModelSizeConfig.materialConfigId.eq(qMaterialConfig.id))
      .leftJoin(qMaterial).on(qMaterialConfig.materialsId.eq(qMaterial.id))
      .leftJoin(qGram).on(qMaterialConfig.gramId.eq(qGram.id))
      .where(qModelSizeConfig.modelSizeId.`in`(modelSize.map { it.modelSizeId }))
      .fetch()


    val printingConfig = query
      .select(
        Projections.constructor(
          PrintingConfigListCustomQueryQueryDto::class.java,
          qPrintingConfig.id,
          qPrinting.id,
          qPrinting.name,
          qPrintingConfig.modelSizeConfigId,
        )
      )
      .from(qPrintingConfig)
      .leftJoin(qPrinting).on(qPrintingConfig.printingId.eq(qPrinting.id))
      .where(qPrintingConfig.modelSizeConfigId.`in`(modelSizeConfig.map { it.modelSizeConfigId }))
      .fetch()

    return modelSize.map { modelSize ->
      ModelSizeCustomsDto(
        modelSizeId = modelSize.modelSizeId,
        modelId = modelSize.modelId,
        unfoldedSizeId = modelSize.unfoldedSizeId,
        unfoldedSizeName = modelSize.unfoldedSizeName,
        unfoldedWidth = modelSize.unfoldedWidth,
        unfoldedHeight = modelSize.unfoldedHeight,
        width = modelSize.width,
        height = modelSize.height,
        length = modelSize.length,
        isThreeD = modelSize.isThreeD,
        modelSizeConfig = modelSizeConfig.filter { it.modelSizeId == modelSize.modelSizeId }
          .map { config ->
            ModelSizeConfigListPrintCustomDto(
              modelSizeConfigId = config.modelSizeConfigId,
              materialConfigId = config.materialConfigId,
              materialName = config.materialName,
              materialImageUrl = config.materialImageUrl,
              materialGSM = config.materialGSM,
              materialMM = config.materialMM,
              printingConfig = printingConfig.filter { printing -> printing.modelSizeConfigId == config.modelSizeConfigId }
                .map { printing ->
                  PrintingConfigListCustomDto(
                    printingConfigId = printing.printingConfigId,
                    printingId = printing.printingId,
                    printingName = printing.printingName,
                  )
                }
            )
          }
      )
    }.toMutableList()
  }
}