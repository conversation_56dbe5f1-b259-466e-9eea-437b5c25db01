package com.lucablock.digiboxapi.modelsize.service.impl

import com.lucablock.digiboxapi.ModelSizeConfigCoating.repository.ModelSizeConfigCoatingRepository
import com.lucablock.digiboxapi.entity.ModelSize
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.model.repository.ModelRepository
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeNewDto
import com.lucablock.digiboxapi.modelsize.repository.ModelSizeRepository
import com.lucablock.digiboxapi.modelsize.request.ModelSizeRequest
import com.lucablock.digiboxapi.modelsize.service.ModelSizeService
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigListPrintDto
import com.lucablock.digiboxapi.modelsizeconfig.repository.ModelSizeConfigRepository
import com.lucablock.digiboxapi.modelsizeconfigdetail.repository.ModelSizeConfigDetailRepository
import com.lucablock.digiboxapi.printing.repository.PrintingRepository
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigListDto
import com.lucablock.digiboxapi.printingconfig.repository.PrintingConfigRepository
import com.lucablock.digiboxapi.unfoldedsize.repository.UnfoldedSizeRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ModelSizeServiceImpl @Autowired constructor(
  private val modelSizeRepository: ModelSizeRepository,
  private val modelRepository: ModelRepository,
  private val unfoldedSizeRepository: UnfoldedSizeRepository,
  private val modelSizeConfigRepository: ModelSizeConfigRepository,
  private val printingRepository: PrintingRepository,
  private val printingConfigRepository: PrintingConfigRepository,
  private val modelSizeConfigDetailRepository: ModelSizeConfigDetailRepository,
  private val modelSizeConfigCoatingRepository: ModelSizeConfigCoatingRepository
) : ModelSizeService {
  override fun createModelSize(modelSizeRequest: ModelSizeRequest): ModelSizeNewDto {
    val model = modelRepository.findById(modelSizeRequest.modelId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้")
    }
    val unfoldedSize =
      unfoldedSizeRepository.findById(modelSizeRequest.unfoldedSizeId).orElseThrow {
        throw NotFoundException("ไม่พบข้อมูลขนาดกางออก")
      }
    if (modelSizeRequest.length == null || modelSizeRequest.length == 0.0 && modelSizeRequest.isThreeD) {
      throw BadRequestException("กรุณาระบุความยาว")
    }
    if (modelSizeRequest.unfoldedHeight >= unfoldedSize.height && modelSizeRequest.unfoldedWidth >= unfoldedSize.width ||
      modelSizeRequest.unfoldedHeight >= unfoldedSize.width && modelSizeRequest.unfoldedWidth >= unfoldedSize.height
    ) {
      throw BadRequestException("ขนาดกางออกโมเดลไม่ถูกต้อง")
    }
    val sizeThreeD = modelSizeRepository.findAllByWidthAndHeightAndLengthAndModelIdAndIsThreeD(
      modelSizeRequest.width,
      modelSizeRequest.height,
      modelSizeRequest.length,
      model.id,
      modelSizeRequest.isThreeD
    )
    if (sizeThreeD.isNotEmpty()) {
      throw BadRequestException("โมเดลนี้มีขนาดนี้อยู่แล้ว")
    }

    val modelSize = modelSizeRepository.save(
      ModelSize(
        modelId = model.id,
        unfoldedSizeId = unfoldedSize.id,
        width = modelSizeRequest.width,
        height = modelSizeRequest.height,
        length = modelSizeRequest.length,
        unfoldedWidth = modelSizeRequest.unfoldedWidth,
        unfoldedHeight = modelSizeRequest.unfoldedHeight,
        isThreeD = modelSizeRequest.isThreeD,
      )
    )
    return ModelSizeNewDto(
      id = modelSize.id,
      modelId = modelSize.modelId,
      unfoldedSizeId = modelSize.unfoldedSizeId,
      unfoldedSizeName = unfoldedSize.name,
      unfoldedHeight = modelSize.unfoldedHeight,
      unfoldedWidth = modelSize.unfoldedWidth,
      width = modelSize.width,
      height = modelSize.height,
      length = modelSize.length,
      isThreeD = modelSize.isThreeD,
    )
  }

  override fun updateModelSize(id: Long, modelSizeRequest: ModelSizeRequest): ModelSizeNewDto {
    val modelSize = modelSizeRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดลสินค้านี้")
    }
    unfoldedSizeRepository.findById(modelSizeRequest.unfoldedSizeId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลขนาดกางออก")
    }
    if (modelSizeRequest.length == null || modelSizeRequest.length == 0.0 && modelSizeRequest.isThreeD) {
      throw BadRequestException("กรุณาระบุความยาว")
    }
    val sizeThreeD =
      modelSizeRepository.findAllByWidthAndHeightAndLengthAndModelIdAndIsThreeDAndIdNot(
        modelSizeRequest.width,
        modelSizeRequest.height,
        modelSizeRequest.length,
        modelSize.modelId,
        modelSizeRequest.isThreeD,
        id
      )
    if (sizeThreeD.isNotEmpty()) {
      throw BadRequestException("โมเดลนี้มีขนาดนี้อยู่แล้ว")
    }

    modelSize.width = modelSizeRequest.width
    modelSize.height = modelSizeRequest.height
    modelSize.length = modelSizeRequest.length
    modelSize.unfoldedWidth = modelSizeRequest.unfoldedWidth
    modelSize.unfoldedHeight = modelSizeRequest.unfoldedHeight
    modelSize.unfoldedSizeId = modelSizeRequest.unfoldedSizeId
    modelSize.isThreeD = modelSizeRequest.isThreeD
    val savedModelSize = modelSizeRepository.save(modelSize)
    return ModelSizeNewDto(
      id = savedModelSize.id,
      modelId = savedModelSize.modelId,
      unfoldedSizeId = savedModelSize.unfoldedSizeId,
      unfoldedSizeName = savedModelSize.unfoldedSize?.name,
      unfoldedHeight = savedModelSize.unfoldedHeight,
      unfoldedWidth = savedModelSize.unfoldedWidth,
      width = savedModelSize.width,
      height = savedModelSize.height,
      length = savedModelSize.length,
      isThreeD = savedModelSize.isThreeD,
    )
  }

  override fun getModelSizeById(id: Long): ModelSizeNewDto {
    val modelSize = modelSizeRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดลสินค้านี้")
    }
    return ModelSizeNewDto(
      id = modelSize.id,
      modelId = modelSize.modelId,
      unfoldedSizeId = modelSize.unfoldedSizeId,
      unfoldedSizeName = modelSize.unfoldedSize?.name,
      unfoldedHeight = modelSize.unfoldedHeight,
      unfoldedWidth = modelSize.unfoldedWidth,
      width = modelSize.width,
      height = modelSize.height,
      length = modelSize.length,
      isThreeD = modelSize.isThreeD,
      modelSizeConfig = modelSize.modelSizeConfig.filter { config ->
        config.materialConfig?.isActive == true
      }.map {
        ModelSizeConfigListPrintDto(
          id = it.id,
          materialConfig = it.materialConfig?.toMaterialConfigDto(),
          printingConfig = it.printingConfig.map { printingConfig ->
            PrintingConfigListDto(
              id = printingConfig.id,
              printingId = printingConfig.printingId,
              printingName = printingConfig.printing?.name,
              modelSizeConfigId = printingConfig.modelSizeConfigId
            )
          }
        )
      }
    )

  }

  override fun getModelSizeByModelId(id: Long): List<ModelSizeNewDto> {
    val model = modelRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้")
    }
    val modelSize = modelSizeRepository.findByModelId(model.id)
    return modelSize.map {
      ModelSizeNewDto(
        id = it.id,
        modelId = it.modelId,
        unfoldedSizeId = it.unfoldedSizeId,
        unfoldedSizeName = it.unfoldedSize?.name,
        unfoldedHeight = it.unfoldedHeight,
        unfoldedWidth = it.unfoldedWidth,
        width = it.width,
        height = it.height,
        length = it.length,
        isThreeD = it.isThreeD,
        modelSizeConfig = it.modelSizeConfig.filter { config ->
          config.materialConfig?.isActive == true
        }.map { config ->
          ModelSizeConfigListPrintDto(
            id = config.id,
            materialConfig = config.materialConfig?.toMaterialConfigDto(),
            printingConfig = config.printingConfig.map { printingConfig ->
              PrintingConfigListDto(
                id = printingConfig.id,
                printingId = printingConfig.printingId,
                printingName = printingConfig.printing?.name,
                modelSizeConfigId = printingConfig.modelSizeConfigId
              )
            }
          )
        }
      )
    }
  }


//  Example for Custom Query
//  override fun getModelSizeByModelId(id: Long): MutableList<ModelSizeCustomsDto> {
//    val model = modelRepository.findById(id).orElseThrow {
//      throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้")
//    }
//    return modelSizeRepository.findAllModelSizeByModelId(model.id)
//
//  }

  override fun deleteModelSize(id: Long): Boolean {
    val modelSize = modelSizeRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดลสินค้านี้")
    }
    val modelSizeConfig = modelSizeConfigRepository.findAllByModelSizeId(id)
    val printingConfig =
      printingConfigRepository.findAllByModelSizeConfigIdIn(modelSizeConfig.map { it.id })
    val modelSizeConfigDetail =
      modelSizeConfigDetailRepository.findAllByPrintingConfigIdIn(printingConfig.map { it.id })
    val modelSizeConfigCoating =
      modelSizeConfigCoatingRepository.findAllByModelSizeConfigDetailIdIn(printingConfig.map { it.id })

    modelSizeConfigCoatingRepository.deleteAllById(modelSizeConfigCoating.map { it.id })
    modelSizeConfigDetailRepository.deleteAllById(modelSizeConfigDetail.map { it.id })
    printingConfigRepository.deleteAllById(printingConfig.map { it.id })
    modelSizeConfigRepository.deleteAllById(modelSizeConfig.map { it.id })
    modelSizeRepository.deleteById(modelSize.id)
    return true
  }
}