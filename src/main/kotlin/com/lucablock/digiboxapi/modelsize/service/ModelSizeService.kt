package com.lucablock.digiboxapi.modelsize.service

import com.lucablock.digiboxapi.modelsize.dto.ModelSizeNewDto
import com.lucablock.digiboxapi.modelsize.request.ModelSizeRequest

interface ModelSizeService {
  fun createModelSize(modelSizeRequest: ModelSizeRequest): ModelSizeNewDto
  fun updateModelSize(id: Long, modelSizeRequest: ModelSizeRequest): ModelSizeNewDto
  fun getModelSizeById(id: Long): ModelSizeNewDto

  fun getModelSizeByModelId(id: Long): List<ModelSizeNewDto>

//    Example for Custom Query
//  fun getModelSizeByModelId(id: Long): MutableList<ModelSizeCustomsDto>

  fun deleteModelSize(id: Long): Boolean
}