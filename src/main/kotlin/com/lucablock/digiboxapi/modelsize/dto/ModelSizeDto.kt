package com.lucablock.digiboxapi.modelsize.dto

import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigListPrintDto
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable

data class ModelSizeNewDto
@QueryProjection
constructor(
  val id: Long,
  var modelId: Long,
  var unfoldedSizeId: Long,
  var unfoldedSizeName: String?,
  var unfoldedWidth: Double,
  var unfoldedHeight: Double,
  var width: Double,
  var height: Double,
  var length: Double?,
  var isThreeD: Boolean = true,
  var modelSizeConfig: List<ModelSizeConfigListPrintDto> = mutableListOf(),
) : Serializable

data class ModelSizeCustomDto
@QueryProjection
constructor(
  val id: Long,
  var modelId: Long,
  var unfoldedSizeId: Long,
  var unfoldedSizeName: String?,
  var width: Double,
  var height: Double,
  var length: Double?,
  var isThreeD: Boolean = true,
) : Serializable

data class ModelSizeListDto
@QueryProjection
constructor(
  val id: Long,
  var unfoldedSizeId: Long,
  var unfoldedSizeName: String,
  var width: Double,
  var height: Double,
  var length: Double?,
) : Serializable


// Example for Custom Query
data class ModelSizeCustomsDto
@QueryProjection
constructor(
  val modelSizeId: Long,
  var modelId: Long,
  var unfoldedSizeId: Long,
  var unfoldedSizeName: String,
  var unfoldedWidth: Double,
  var unfoldedHeight: Double,
  var width: Double,
  var height: Double,
  var length: Double,
  var isThreeD: Boolean = true,
  var modelSizeConfig: List<ModelSizeConfigListPrintCustomDto> = emptyList(),
) : Serializable

data class ModelSizeConfigListPrintCustomDto
@QueryProjection
constructor(
  val modelSizeConfigId: Long,
  val materialConfigId: Long,
  val materialName: String,
  val materialImageUrl: String,
  val materialGSM: Int,
  val materialMM: Double,
  var printingConfig: List<PrintingConfigListCustomDto> = emptyList(),
) : Serializable

data class PrintingConfigListCustomDto
@QueryProjection constructor(
  val printingConfigId: Long,
  val printingId: Long,
  val printingName: String,
) : Serializable


data class ModelSizeCustomsQueryDto
@QueryProjection
constructor(
  val modelSizeId: Long,
  var modelId: Long,
  var unfoldedSizeId: Long,
  var unfoldedSizeName: String,
  var unfoldedWidth: Double,
  var unfoldedHeight: Double,
  var width: Double,
  var height: Double,
  var length: Double,
  var isThreeD: Boolean = true,
) : Serializable

data class ModelSizeConfigListPrintCustomQueryDto
@QueryProjection
constructor(
  val modelSizeId: Long,
  val modelSizeConfigId: Long,
  val materialConfigId: Long,
  val materialName: String,
  val materialImageUrl: String,
  val materialGSM: Int,
  val materialMM: Double,
) : Serializable

data class PrintingConfigListCustomQueryQueryDto
@QueryProjection constructor(
  val printingConfigId: Long,
  val printingId: Long,
  val printingName: String,
  val modelSizeConfigId: Long,
) : Serializable