package com.lucablock.digiboxapi.areasizepercentage.controller

import com.lucablock.digiboxapi.areasizepercentage.request.AreaSizePercentageRequest
import com.lucablock.digiboxapi.areasizepercentage.service.AreaSizePercentageService
import com.lucablock.digiboxapi.coating.request.CoatingRequest
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("api")
class AreaSizePercentageController {
  private val logger: Logger = LoggerFactory.getLogger(AreaSizePercentageController::class.java)

  @Autowired
  lateinit var areaSizePercentageService: AreaSizePercentageService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/area-size")
  fun createAreaSize(
    @Valid @RequestBody areaSizeRequest: AreaSizePercentageRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลพื้นที่(เปอร์เซ็นต์)สำเร็จ",
          areaSizePercentageService.createAreaSize(areaSizeRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "พื้นที่(เปอร์เซ็นต์)นี้มีอยู่แล้ว"))
    } catch (e: Exception) {
      logger.error("create area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถสร้างข้อมูลพื้นที่(เปอร์เซ็นต์) กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/area-size/{id}")
  fun updateAreaSizePercentage(
    @PathVariable id: Long,
    @Valid @RequestBody areaSizeRequest: AreaSizePercentageRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลพื้นที่(เปอร์เซ็นต์)สำเร็จ",
          areaSizePercentageService.updateAreaSize(id, areaSizeRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("update area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "พื้นที่(เปอร์เซ็นต์)นี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error("update area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลพื้นที่(เปอร์เซ็นต์)"))
    } catch (e: Exception) {
      logger.error("update area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลพื้นที่(เปอร์เซ็นต์) กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @GetMapping("/web/area-size")
  fun findAllAreaSize(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          areaSizePercentageService.getAllAreaSize()
        )
      )
    } catch (e: Exception) {
      logger.error("find all area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/area-size/{id}")
  fun findAreaSizeById(@PathVariable id: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          areaSizePercentageService.getAreaSizeById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find area size by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลพื้นที่(เปอร์เซ็นต์)"))
    } catch (e: Exception) {
      logger.error("find area size by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/area-size/{id}")
  fun deleteAreaSize(@PathVariable id: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลสำเร็จ",
          areaSizePercentageService.deleteAreaSize(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลพื้นที่(เปอร์เซ็นต์)"))
    } catch (e: Exception) {
      logger.error("delete area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/area-size/page")
  fun findCoatingPage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("sortByPercent", required = false) sortByPercent: Boolean?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = areaSizePercentageService.getAreaSizePage(
            pageable,
            ascending,
            search,
            sortByPercent,
            isActive,
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find area size error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

}