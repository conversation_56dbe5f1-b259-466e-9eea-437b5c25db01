package com.lucablock.digiboxapi.areasizepercentage.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero

class AreaSizePercentageRequest {
  @NotBlank(message = "กรุณากรอกชื่อพื้นที่")
  val name : String = ""
  @NotNull(message = "กรุณากรอกพื้นที่ (เปอร์เซ็นต์)")
  @Positive(message = "กรุณากรอกพื้นที่ มากกว่า 0")
  val percentage : Int = 0
  @NotBlank(message = "กรุณากรอก url รูปภาพ")
  val imageUrl : String = ""
  @JsonProperty("isActive")
  val isActive : Boolean = true
}