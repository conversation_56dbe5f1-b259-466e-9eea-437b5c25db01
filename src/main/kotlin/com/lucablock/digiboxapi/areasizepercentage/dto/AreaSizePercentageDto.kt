package com.lucablock.digiboxapi.areasizepercentage.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class AreaSizePercentageDto
@QueryProjection
constructor(
  val id: Int,
  val name: String,
  val percentage: Int,
  val imageUrl: String,
  val isActive: Boolean,
  val isDeleted: <PERSON><PERSON>an,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
)
