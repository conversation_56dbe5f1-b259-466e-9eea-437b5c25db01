package com.lucablock.digiboxapi.areasizepercentage.service.impl

import com.lucablock.digiboxapi.areasizepercentage.dto.AreaSizePercentageDto
import com.lucablock.digiboxapi.areasizepercentage.repository.AreaSizePercentageRepositoryCustom
import com.lucablock.digiboxapi.coating.repository.CoatingRepositoryCustom
import com.lucablock.digiboxapi.entity.QAreaSizePercentage
import com.lucablock.digiboxapi.entity.QCoating
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class AreaSizePercentageRepositoryImpl : AreaSizePercentageRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qAreaSizePercentage = QAreaSizePercentage.areaSizePercentage

  override fun getAreaSizePage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    sortByPercent: Boolean?,
    isActive: Boolean?,
  ): Page<AreaSizePercentageDto> {
    var criteria = qAreaSizePercentage.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qAreaSizePercentage.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qAreaSizePercentage.name.containsIgnoreCase(search))
    }

    val sort =
      if(sortByPercent == null){
       if (ascending) {
        qAreaSizePercentage.id.asc()
      } else {
        qAreaSizePercentage.id.desc()
      }
    }else{
      if (sortByPercent == true){
        qAreaSizePercentage.percentage.asc()
      }else{
        qAreaSizePercentage.id.asc()
      }
    }

    val query = queryFactory
      .select(qAreaSizePercentage)
      .from(qAreaSizePercentage)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toAreaSizePercentageDto()
      }

    val total = queryFactory
      .select(qAreaSizePercentage)
      .from(qAreaSizePercentage)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}