package com.lucablock.digiboxapi.areasizepercentage.service

import com.lucablock.digiboxapi.areasizepercentage.dto.AreaSizePercentageDto
import com.lucablock.digiboxapi.areasizepercentage.request.AreaSizePercentageRequest
import com.lucablock.digiboxapi.coating.dto.CoatingDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile

interface AreaSizePercentageService {
  fun createAreaSize(areaSizeRequest: AreaSizePercentageRequest):AreaSizePercentageDto
  fun updateAreaSize(id:Long, areaSizeRequest: AreaSizePercentageRequest):AreaSizePercentageDto
  fun getAllAreaSize(): List<AreaSizePercentageDto>
  fun getAreaSizeById(id:Long): AreaSizePercentageDto
  fun deleteAreaSize(id:Long): Boolean
  fun getAreaSizePage(
    pageable: Pageable,
    ascending: <PERSON>olean,
    search: String?,
    sortByPercent: Boolean?,
    isActive : Boolean?
  ): Page<AreaSizePercentageDto>
}