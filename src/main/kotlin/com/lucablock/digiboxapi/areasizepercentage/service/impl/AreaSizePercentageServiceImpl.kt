package com.lucablock.digiboxapi.areasizepercentage.service.impl

import com.lucablock.digiboxapi.areasizepercentage.dto.AreaSizePercentageDto
import com.lucablock.digiboxapi.areasizepercentage.repository.AreaSizePercentageRepository
import com.lucablock.digiboxapi.areasizepercentage.request.AreaSizePercentageRequest
import com.lucablock.digiboxapi.areasizepercentage.service.AreaSizePercentageService
import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.AreaSizePercentage
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.specialtechnicconfig.repository.SpecialTechnicConfigRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class AreaSizePercentageServiceImpl @Autowired constructor(
  private val areaSizePercentageRepository: AreaSizePercentageRepository,
  private val s3Service: S3Service,
  private val specialTechnicConfigRepository: SpecialTechnicConfigRepository
): AreaSizePercentageService {
  @Transactional
  override fun createAreaSize(areaSizeRequest: AreaSizePercentageRequest): AreaSizePercentageDto {
    areaSizePercentageRepository.existsAreaSizePercentageByPercentageAndIsDeletedFalse(areaSizeRequest.percentage).let {
      if (it){
        throw BadRequestException("ข้อมูลพื้นที่(เปอร์เซ็นต์) ถูกใช้งานแล้ว")
      }
    }
    areaSizePercentageRepository.existsAreaSizePercentageByNameIgnoreCaseAndIsDeletedFalse(areaSizeRequest.name).let {
      if (it){
        throw BadRequestException("ชื่อพื้นที่ ถูกใช้งานแล้ว")
      }
    }

    val area = AreaSizePercentage(
        name = areaSizeRequest.name,
        percentage = areaSizeRequest.percentage,
        imageUrl = areaSizeRequest.imageUrl,
      )

    val urlFile = s3Service.moveFile(areaSizeRequest.imageUrl).url
    area.imageUrl = urlFile
    val updateArea = areaSizePercentageRepository.save(area)

    return updateArea.toAreaSizePercentageDto()
  }

  override fun updateAreaSize(id: Long, areaSizeRequest: AreaSizePercentageRequest): AreaSizePercentageDto {
    val area = areaSizePercentageRepository.findById(id).orElseThrow{
      NotFoundException("ไม่พบข้อมูลพื้นที่(เปอร์เซ็นต์)")
    }
    areaSizePercentageRepository.existsAreaSizePercentageByPercentageAndIsDeletedFalseAndIdNot(areaSizeRequest.percentage,id).let {
      if (it){
        throw Exception("ข้อมูลพื้นที่(เปอร์เซ็นต์) ถูกใช้งานแล้ว")
      }
    }
    areaSizePercentageRepository.existsAreaSizePercentageByNameIgnoreCaseAndIsDeletedFalseAndIdNot(areaSizeRequest.name,id).let {
      if (it){
        throw Exception("ชื่อพื้นที่ ถูกใช้งานแล้ว")
      }
    }
    area.name = areaSizeRequest.name
    area.percentage = areaSizeRequest.percentage
    area.isActive = areaSizeRequest.isActive

    if (areaSizeRequest.imageUrl != area.imageUrl) {
      s3Service.deleteFile(area.imageUrl)
      val newFileUrl = s3Service.moveFile(areaSizeRequest.imageUrl).url
      area.imageUrl = newFileUrl
    }
    val updateArea = areaSizePercentageRepository.save(area)
    return updateArea.toAreaSizePercentageDto()
  }

  override fun getAllAreaSize(): List<AreaSizePercentageDto> {
    val area = areaSizePercentageRepository.findAll()
    return area.map { it.toAreaSizePercentageDto() }
  }

  override fun getAreaSizeById(id: Long): AreaSizePercentageDto {
    val area = areaSizePercentageRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลพื้นที่(เปอร์เซ็นต์)")
    }
    return area.toAreaSizePercentageDto()
  }

  override fun deleteAreaSize(id: Long): Boolean {
    val area = areaSizePercentageRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลพื้นที่(เปอร์เซ็นต์)")
    }
    val specialTechnic = specialTechnicConfigRepository.findByAreaSizePercentageId(id)
    if(specialTechnic.isNotEmpty()){
      throw BadRequestException("พื้นที่นี้ถูกใช้งานอยู่ ไม่สามารถลบได้")
    }
    area.isDeleted = true
    areaSizePercentageRepository.save(area)
    return true
  }

  override fun getAreaSizePage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    sortByPercent: Boolean?,
    isActive: Boolean?,
  ): Page<AreaSizePercentageDto> {
    return areaSizePercentageRepository.getAreaSizePage(
      pageable,
      ascending,
      search,
      sortByPercent,
      isActive,
    )
  }
  
}