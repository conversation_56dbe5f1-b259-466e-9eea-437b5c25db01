package com.lucablock.digiboxapi.areasizepercentage.repository

import com.lucablock.digiboxapi.entity.AreaSizePercentage
import org.springframework.data.jpa.repository.JpaRepository

interface AreaSizePercentageRepository : JpaRepository<AreaSizePercentage, Long> ,
  AreaSizePercentageRepositoryCustom
{
  fun existsAreaSizePercentageByPercentageAndIdNot(percentage: Int,id:Long):Boolean
  fun existsAreaSizePercentageByPercentageAndIsDeletedFalse(percentage: Int): Boolean
  fun existsAreaSizePercentageByPercentageAndIsDeletedFalseAndIdNot(percentage: Int, id: Long): Boolean
  fun existsAreaSizePercentageByNameIgnoreCaseAndIsDeletedFalse(name: String):Boolean
  fun existsAreaSizePercentageByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String, id: Long): <PERSON>olean

}