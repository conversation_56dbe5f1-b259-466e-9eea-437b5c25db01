package com.lucablock.digiboxapi.areasizepercentage.repository

import com.lucablock.digiboxapi.areasizepercentage.dto.AreaSizePercentageDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository

@Repository
interface AreaSizePercentageRepositoryCustom {
  fun getAreaSizePage(
    pageable: Pageable,
    ascending: <PERSON>ole<PERSON>,
    search: String?,
    sortByPercent: Boolean?,
    isActive : Boolean?,
  ): Page<AreaSizePercentageDto>
}