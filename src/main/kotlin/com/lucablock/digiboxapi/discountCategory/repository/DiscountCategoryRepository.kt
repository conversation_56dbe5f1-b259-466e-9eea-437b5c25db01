package com.lucablock.digiboxapi.discountCategory.repository

import com.lucablock.digiboxapi.entity.DiscountCategory
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface DiscountCategoryRepository : JpaRepository<DiscountCategory, Long>,
  DiscountCategoryRepositoryCustom {
  fun existsByNameIgnoreCase(name: String): Boolean
  fun existsByNameIgnoreCaseAndIdNot(name: String, id: Long): <PERSON><PERSON><PERSON>
}