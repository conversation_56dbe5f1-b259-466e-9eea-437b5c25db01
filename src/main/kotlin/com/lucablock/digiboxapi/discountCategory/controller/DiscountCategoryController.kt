package com.lucablock.digiboxapi.discountCategory.controller

import com.lucablock.digiboxapi.discountCategory.request.DiscountCategoryRequest
import com.lucablock.digiboxapi.discountCategory.service.DiscountCategoryService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/discount-category")
class DiscountCategoryController @Autowired constructor(
  private val discountCategoryService: DiscountCategoryService
) {
  @PostMapping()
  fun createDiscountCategory(
    @Valid @RequestBody request: DiscountCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างประเภทส่วนลดสำเร็จ",
          data = discountCategoryService.createDiscountCategory(request)
        )
      )
    } catch (e: BadRequestException) {
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อประเภทส่วนลดซ้ำ")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @PutMapping("/{id}")
  fun updateDiscountCategory(
    @PathVariable id: Long,
    @Valid @RequestBody request: DiscountCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แก้ไขประเภทส่วนลดสำเร็จ",
          data = discountCategoryService.updateDiscountCategory(id, request)
        )
      )
    } catch (e: BadRequestException) {
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อประเภทส่วนลดซ้ำ")
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบประเภทส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @DeleteMapping("/{id}")
  fun deletedDiscountCategory(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบประเภทส่วนลดสำเร็จ",
          data = discountCategoryService.deletedDiscountCategory(id)
        )
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบประเภทส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @GetMapping()
  fun getDiscountCategory(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal?,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงรายการประเภทส่วนลดสำเร็จ",
          data = discountCategoryService.getDiscountCategory()
        )
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @GetMapping("/{id}")
  fun getDiscountCategoryById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงประเภทส่วนลดสำเร็จ",
          data = discountCategoryService.getDiscountCategoryById(id)
        )
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบประเภทส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @GetMapping("/user-discount-category")
  fun getUserDiscountCategory(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงรายการส่วนลดสำเร็จ",
          data = discountCategoryService.getUserDiscountCategory(
            userPrincipal.getUserId().toInt(),
          )
        )
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

}