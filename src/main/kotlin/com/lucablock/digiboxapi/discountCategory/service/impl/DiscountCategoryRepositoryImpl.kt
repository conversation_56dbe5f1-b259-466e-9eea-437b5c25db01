package com.lucablock.digiboxapi.discountCategory.service.impl

import com.lucablock.digiboxapi.discountCategory.dto.DiscountCategoryAmountDto
import com.lucablock.digiboxapi.discountCategory.dto.QDiscountCategoryAmountDto
import com.lucablock.digiboxapi.discountCategory.repository.DiscountCategoryRepositoryCustom
import com.lucablock.digiboxapi.entity.QDiscount
import com.lucablock.digiboxapi.entity.QDiscountCategory
import com.lucablock.digiboxapi.entity.QUserDiscount
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.stereotype.Service

@Service
class DiscountCategoryRepositoryImpl : DiscountCategoryRepositoryCustom {

  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qDiscount = QDiscount.discount
  private val qDiscountCategory = QDiscountCategory.discountCategory
  private val qUserDiscount = QUserDiscount.userDiscount

  override fun getDiscountCategory(): List<DiscountCategoryAmountDto> {
    val criteria = qDiscountCategory.id.isNotNull

    val discountCategory = queryFactory
      .select(
        QDiscountCategoryAmountDto(
          qDiscountCategory.id,
          qDiscountCategory.name,
          qDiscountCategory.categoryImage,
          qDiscount.id.count()
        ),
      ).from(qDiscountCategory)
      .leftJoin(qDiscount)
      .where(criteria)
      .on(qDiscount.discountCategoryId.eq(qDiscountCategory.id).and(qDiscount.isActive.isTrue))
      .groupBy(qDiscountCategory.id)
      .fetch()

    return discountCategory
  }

  override fun getUserDiscountCategory(userId: Int): List<DiscountCategoryAmountDto> {
    val criteria = qDiscount.isActive.isTrue.and(qDiscount.isDeleted.isFalse)

    val discountCategory = queryFactory
      .select(
        QDiscountCategoryAmountDto(
          qDiscountCategory.id,
          qDiscountCategory.name,
          qDiscountCategory.categoryImage,
          qDiscount.id.count()
        )
      )
      .from(qUserDiscount)
      .where(criteria)
      .innerJoin(qDiscount)
      .on(qUserDiscount.discountId.eq(qDiscount.id).and(qDiscount.isActive.isTrue))
      .innerJoin(qDiscountCategory).on(qDiscount.discountCategoryId.eq(qDiscountCategory.id))
      .where(qUserDiscount.userId.eq(userId).and(qUserDiscount.isActive.isTrue))
      .groupBy(qDiscountCategory.id)
      .fetch()

    return discountCategory
  }

}
