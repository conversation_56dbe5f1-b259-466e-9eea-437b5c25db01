package com.lucablock.digiboxapi.discountCategory.service

import com.lucablock.digiboxapi.discountCategory.dto.DiscountCategoryAmountDto
import com.lucablock.digiboxapi.discountCategory.dto.DiscountCategoryDto
import com.lucablock.digiboxapi.discountCategory.request.DiscountCategoryRequest

interface DiscountCategoryService {
  fun createDiscountCategory(
    request: DiscountCategoryRequest
  ): DiscountCategoryDto

  fun updateDiscountCategory(id: Long, request: DiscountCategoryRequest): DiscountCategoryDto
  fun deletedDiscountCategory(id: Long): Boolean
  fun getDiscountCategory(): List<DiscountCategoryAmountDto>
  fun getDiscountCategoryById(id: Long): DiscountCategoryDto
  fun getUserDiscountCategory(userId: Int): List<DiscountCategoryAmountDto>
}