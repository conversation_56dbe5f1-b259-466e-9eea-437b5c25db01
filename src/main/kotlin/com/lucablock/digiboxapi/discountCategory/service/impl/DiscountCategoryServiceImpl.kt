package com.lucablock.digiboxapi.discountCategory.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.discount.repository.DiscountRepository
import com.lucablock.digiboxapi.discount.repository.UserDiscountRepository
import com.lucablock.digiboxapi.discountCategory.dto.DiscountCategoryAmountDto
import com.lucablock.digiboxapi.discountCategory.dto.DiscountCategoryDto
import com.lucablock.digiboxapi.discountCategory.repository.DiscountCategoryRepository
import com.lucablock.digiboxapi.discountCategory.request.DiscountCategoryRequest
import com.lucablock.digiboxapi.discountCategory.service.DiscountCategoryService
import com.lucablock.digiboxapi.entity.DiscountCategory
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class DiscountCategoryServiceImpl @Autowired constructor(
  private val discountCategoryRepository: DiscountCategoryRepository,
  private val discountRepository: DiscountRepository,
  private val s3Service: S3Service,
  private val userDiscountRepository: UserDiscountRepository,
) : DiscountCategoryService {
  @Transactional
  override fun createDiscountCategory(request: DiscountCategoryRequest): DiscountCategoryDto {
    if (discountCategoryRepository.existsByNameIgnoreCase(request.name)) {
      throw BadRequestException("ชื่อประเภทส่วนลดซ้ำ")
    }

    val savedCategoryImg = discountCategoryRepository.save(
      DiscountCategory(
        name = request.name,
        categoryImage = request.categoryImage
      )
    )

    val newFileUrl = s3Service.moveFile(request.categoryImage).url
    savedCategoryImg.categoryImage = newFileUrl

    val updateDiscountCategory = discountCategoryRepository.save(savedCategoryImg)

    return updateDiscountCategory.toDiscountCategoryDto()
  }

  override fun updateDiscountCategory(
    id: Long,
    request: DiscountCategoryRequest
  ): DiscountCategoryDto {
    val category = discountCategoryRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบประเภทส่วนลด")
    }

    if (discountCategoryRepository.existsByNameIgnoreCaseAndIdNot(request.name, id)) {
      throw BadRequestException("ชื่อประเภทส่วนลดซ้ำ")
    }

    category.name = request.name

    if (request.categoryImage != category.categoryImage) {
      s3Service.deleteFile(category.categoryImage)
      val newFileUrl = s3Service.moveFile(request.categoryImage).url
      category.categoryImage = newFileUrl
    }

    val saveCategory = discountCategoryRepository.save(category)

    return saveCategory.toDiscountCategoryDto()
  }

  override fun deletedDiscountCategory(id: Long): Boolean {
    val discountCategory = discountCategoryRepository.findById(id).orElseThrow {
      NotFoundException("ไม่พบประเภทส่วนลด")
    }

    if (discountRepository.findDiscountByDiscountCategoryIdAndIsActiveTrue(id).isNotEmpty()) {
      throw BadRequestException("มีประเภทส่วนลดใช้งานอยู่")
    }

    s3Service.deleteFile(discountCategory.categoryImage)

    discountCategoryRepository.deleteById(id)

    return true
  }

  override fun getDiscountCategory(): List<DiscountCategoryAmountDto> {
    return discountCategoryRepository.getDiscountCategory()
  }

  override fun getDiscountCategoryById(id: Long): DiscountCategoryDto {
    val discountCate = discountCategoryRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบส่วนลด")
    }

    return discountCate.toDiscountCategoryDto()
  }

  override fun getUserDiscountCategory(userId: Int): List<DiscountCategoryAmountDto> {
    return discountCategoryRepository.getUserDiscountCategory(userId)
  }

}