package com.lucablock.digiboxapi.order.constant

enum class ReceiptTypeEnum(val value: Int, val description: String) {
  RECEIPT_ONLY(1, "ใบเสร็จรับเงิน"),
  RECEIPT_AND_TAX(2, "ใบเสร็จรับเงิน และ ใบกำกับภาษี");

  companion object {
    fun fromValue(value: Int): ReceiptTypeEnum {
      return ReceiptTypeEnum.entries.firstOrNull { it.value == value }
        ?: throw IllegalArgumentException("ไม่พบสถานะสำหรับ value: $value")
    }
  }
}