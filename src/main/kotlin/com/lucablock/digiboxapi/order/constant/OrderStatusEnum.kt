package com.lucablock.digiboxapi.order.constant

enum class OrderStatusEnum(val value: Int, val description: String) {
  WAITING_PAYMENT(1, "รอชำระเงิน"),
  CHECKING(2, "รอตรวจสอบ"),
  ARTWORK(3, "Artwork"),
  CAPACITY(4, "กำลังผลิต"),
  SHIPPING(5, "กำลังจัดส่ง"),
  DONE(6, "สำเร็จแล้ว"),
  CANCEL(7, "ยกเลิก"),
  REFUND(8, "คืนเงิน/คืนสินค้า"),
  DRAFT(9, "แบบร่าง");

  companion object {
    fun fromValue(value: Int): OrderStatusEnum {
      return OrderStatusEnum.entries.firstOrNull { it.value == value }
        ?: throw IllegalArgumentException("ไม่พบสถานะสำหรับ value: $value")
    }
  }
}