package com.lucablock.digiboxapi.order.repository

import com.lucablock.digiboxapi.entity.OrderItem
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface OrderItemRepository: JpaRepository<OrderItem, Int>, OrderItemRepositoryCustom {
  @Query("SELECT * FROM order_item WHERE item_id = :itemId", nativeQuery = true)
  fun findByItemId(itemId: Int): OrderItem

  @Query("SELECT * FROM order_item WHERE order_id = :orderId", nativeQuery = true)
  fun findAllByOrderId(orderId: Int): List<OrderItem>
}