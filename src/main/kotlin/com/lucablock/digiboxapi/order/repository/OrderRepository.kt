package com.lucablock.digiboxapi.order.repository

import com.lucablock.digiboxapi.entity.Order
import com.lucablock.digiboxapi.order.dto.OrderDetailDto
import org.aspectj.weaver.ast.Or
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface OrderRepository: JpaRepository<Order, Long>, OrderRepositoryCustom {
  @Query("""
    SELECT MAX(CAST(SUBSTRING(o.orderNo, 3) AS Long))
    FROM Order o
    WHERE o.orderNo LIKE CONCAT('OD', :date, '%')
""")
  fun findMaxOrderNoForDate(@Param("date") date: String): Long?
}