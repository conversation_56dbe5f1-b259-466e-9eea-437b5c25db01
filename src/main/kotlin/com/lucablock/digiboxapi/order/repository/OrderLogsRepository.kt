package com.lucablock.digiboxapi.order.repository

import com.lucablock.digiboxapi.entity.OrderLogs
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.util.Optional

@Repository
interface OrderLogsRepository: JpaRepository<OrderLogs, Long> {
  @Transactional
  @Modifying
  @Query("delete from order_logs where order_id = :orderId", nativeQuery = true)
  fun deleteAllByOrderId(orderId: Int)

}