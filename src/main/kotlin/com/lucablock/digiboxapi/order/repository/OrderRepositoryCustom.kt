package com.lucablock.digiboxapi.order.repository

import com.lucablock.digiboxapi.order.dto.*
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.util.*

interface OrderRepositoryCustom {
  fun getOrderPage(
    pageable: Pageable,
    ascending: <PERSON><PERSON>an,
    search: String?,
    status: Int?,
    userPrincipal: UserPrincipal,
  ): Page<OrderPageDto>
  fun getOrderStatusCounts(): List<OrderStatusCountDto>
  fun getCheckoutOrderById(orderId: Int): CheckoutOrderDto
  fun getOrderDetailById(orderId: Int): OrderIdDetailDto
  fun getSummaryOrderById(orderId: Long): SummaryOrderDto
}