package com.lucablock.digiboxapi.order.repository

import com.lucablock.digiboxapi.entity.OrderAddress
import com.lucablock.digiboxapi.order.dto.CheckoutOrderDto
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface OrderAddressRepository : JpaRepository<OrderAddress, Long> {
  fun findAllByOrderId(orderId: Long): MutableList<OrderAddress>
}