package com.lucablock.digiboxapi.order.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.order.request.CreateCheckoutRequest
import com.lucablock.digiboxapi.order.service.OrderService
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api/")
class OrderController {
  private val logger: Logger = LoggerFactory.getLogger(OrderController::class.java)

  @Autowired
  lateinit var orderService: OrderService

  @PostMapping("web/order/checkout")
  fun createOrderCheckOut(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody createCheckoutRequest: CreateCheckoutRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างแบบร่างออเดอร์สำเร็จ",
          orderService.createOrderCheckout(userPrincipal ,createCheckoutRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูล กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถสร้างข้อมูลออเดอร์ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("web/order/summary/{orderId}")
  fun getSummaryOrder(
    @PathVariable orderId: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลออเดอร์สำเร็จ",
          orderService.summaryOrder(orderId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลออเดอร์ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถสร้างข้อมูลออเดอร์ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/order/page")
  fun findOrderPage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("status", required = false) status: Int?,
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลออเดอร์สำเร็จ",
          data = orderService.getOrderPage(
            pageable,
            ascending,
            search,
            status,
            userPrincipal,
          )
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("web/order/{orderId}")
  fun getOrderById(
    @PathVariable orderId: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลออเดอร์สำเร็จ",
          orderService.getOrderById(orderId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลออเดอร์ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถแสดงข้อมูลออเดอร์ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("web/order/status/count")
  fun getOrderStatusCounts(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสถานะออเดอร์สำเร็จ",
          orderService.getOrderStatusCounts()
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลสถานะออเดอร์ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถแสดงข้อมูลออเดอร์ กรุณาลองอีกครั้ง"))
    }
  }

  @PutMapping("web/order/update/status/{id}")
  fun updateOrderStatus(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลสถานะออเดอร์สำเร็จ",
          orderService.updateOrderStatus(id)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message?: "ไม่สามารถอัปเดตสถานะได้ กรุณาลองอีกครั้ง"))
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลสถานะออเดอร์ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถอัปเดตสถานะได้ กรุณาลองอีกครั้ง"))
    }
  }

}