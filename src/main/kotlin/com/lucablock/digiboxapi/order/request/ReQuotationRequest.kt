package com.lucablock.digiboxapi.order.request

data class ReQuotationRequest(
  val orderItems: List<ReOrderItemRequest>,
  val isTax: Boolean,
  val taxPayerType: Int? = null,
  val taxId: String? = null,
  val taxPayerName: String? = null,
  val phoneNumber: String? = null,
  val address: String? = null,
  val district: String? = null,
  val subDistrict: String? = null,
  val province: String? = null,
  val zipCode: String? = null,
  val email: String? = null
)

data class ReOrderItemRequest(
  val id: Int,
  val item: ReItemRequest,
  val shipping: Int,
  val shippingCost: Double,
  val recipientName: String? = null,
  val phoneNumber: String? = null,
  val address: String? = null,
  val district: String? = null,
  val subDistrict: String? = null,
  val province: String? = null,
  val zipCode: String? = null,
  val email: String? = null,
)

data class ReItemRequest(
  val id: Int,
  val width: Int,
  val length: Int,
  val height: Int,
  val material: ReMaterialRequest,
  val printing: RePrintingRequest,
  val coating: ReCoatingRequest,
  val specialTechnic: List<ReItemSpecialTechnicRequest?>,
  val artwork: ReArtworkRequest,
  val productDemo: ReProductDemoRequest,
  val description: String?
)

data class ReMaterialRequest(
  val id: Int,
  var amount: Int,
  val unitPrice: Double,
)

data class RePrintingRequest(
  val id: Int,
  val price: Double,
)

data class ReCoatingRequest(
  val id: Int,
  val price: Double,
)

data class ReItemSpecialTechnicRequest(
  val id: Int,
  var price: Double,
  val width: Int,
  val height: Int,
)

data class ReArtworkRequest(
  val isArtwork: Boolean,
  val price: Double,
  val artworkUrl: String?,
)

data class ReProductDemoRequest(
  val id: Int,
  val price: Double,
)

data class UpdateItemPrice(
  val id: Int,
  val totalPrice: Double,
)