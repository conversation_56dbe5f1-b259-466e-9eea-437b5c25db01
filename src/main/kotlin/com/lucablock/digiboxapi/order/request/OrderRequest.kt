package com.lucablock.digiboxapi.order.request

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.time.LocalDate
import java.time.LocalDateTime

class CreateOrderRequest {
  @NotNull(message = "ระบุประเภทผู้เสียภาษี")
  @Min(value = 1, message = "ประเภทผู้เสียภาษีไม่ถูกต้อง")
  val taxPayerType: Int = 0
  val taxId: String = ""

  @NotBlank(message = "ระบุชื่อผู้เสียภาษี")
  val taxPayerName: String = ""

  @NotBlank(message = "ระบุเบอร์โทรศัพท์")
  val phoneNumber: String = ""

  @NotBlank(message = "ระบุที่อยู่")
  val address: String = ""

  @NotBlank(message = "ระบุอำเภอ")
  val district: String = ""

  @NotBlank(message = "ระบุตำบล")
  val subDistrict: String = ""

  @NotBlank(message = "ระบุจังหวัด")
  val province: String = ""

  @NotBlank(message = "ระบุรหัสไปรษณีย์")
  val zipCode: String = ""

  @NotBlank(message = "ระบุอีเมล")
  val email: String = ""
  val discounts: List<Int> = mutableListOf()

  val isTax: Boolean = false

  @NotEmpty(message = "ระบุรหัสสินค้า")
  @Valid
  val items: List<CreateOrderItemRequest> = mutableListOf()
}

class UpdateOrderRequest {
  val items: List<CreateOrderItemRequest> = mutableListOf()
  val isTax: Boolean = false
  val taxPayerType: Int = 0
  val taxId: String = ""
  val taxPayerName: String = ""
  val phoneNumber: String = ""
  val address: String = ""
  val district: String = ""
  val subDistrict: String = ""
  val province: String = ""
  val zipCode: String = ""
  val email: String = ""
}

class CreateOrderItemRequest {
  @NotNull(message = "ระบุรหัสสินค้า")
  @Min(value = 1, message = "รหัสสินค้าไม่ถูกต้อง")
  val itemId: Int = 0

  @NotNull(message = "ระบุราคาสินค้า")
  val totalPrice: Double = 0.0

  @NotNull(message = "ระบุรหัสการจัดส่ง")
  @Min(value = 1, message = "รหัสการจัดส่งไม่ถูกต้อง")
  val shipping: Int = 0

  @NotNull(message = "ระบุราคาการจัดส่ง")
  val shippingCost: Double = 0.0
  val recipientName: String = ""
  val phoneNumber: String = ""
  val address: String = ""
  val district: String = ""
  val subDistrict: String = ""
  val province: String = ""
  val zipCode: String = ""
  val email: String = ""
}

data class CreateOrderShippingRequest(
  val deliveryDate: String,
  val trackingNumber: String,
  val shippingName: String,
  val receivedDescription: String?,
)

data class AdminConfirmOrderReceivedRequest(
  val recipientName: String,
  val receivedDescription: String?,
)

class CreateCheckoutRequest{
  @NotNull(message = "กรุณาระบุราคารวม")
  val totalPrice: Double = 0.0
  @NotNull(message = "กรุณาระบุค่าภาษีมูลค่าเพิ่ม")
  val vat: Double = 0.0
  @NotNull(message = "กรุณาระบุค่าจัดส่ง")
  val shippingCost: Double = 0.0
  @NotNull(message = "กรุณาระบุประเภทใบเสร็จรับเงิน")
  val receiptType: Int = 0
  @NotNull(message = "กรุณาระบุประเภทการจัดส่ง")
  val shippingType: Int = 0
  val shippingSubType: Int? = null
  val pickupName: String? = null
  val pickupTell: String? = null
  val orderAddress : List<Int> = mutableListOf()
  val checkoutItems: List<CreateCheckoutItemRequest> = mutableListOf()
}

class CreateCheckoutItemRequest {
  @NotNull(message = "กรุณาระบุสินค้า")
  val customizeId: Long = 0
  val addressId: Int? = null
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val deliveryDate: LocalDateTime? = null
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val periodDate: LocalDateTime? = null
  val shippingCostItem: Double = 0.0
}

