package com.lucablock.digiboxapi.order.request


import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

class PaymentTransactionRequest {
  @NotEmpty(message = "Order Number is required")
  val orderId: Int = 0
  @NotNull(message = "Amount Type Id is required")
  val amount: Double = 0.0
  @NotNull(message = "Payment Type is required")
  val paymentType: String = ""
  @NotNull(message = "Bank Account Name is required")
  val bankAccountName: String = ""
  @NotNull(message = "Bank Name is required")
  val bankName: String = ""
  @NotNull(message = "Bank Account Number is required")
  val bankAccountNumber: String = ""
  @NotEmpty(message = "Date is required")
  val dateTime: String = ""
  val description: String? = null
}