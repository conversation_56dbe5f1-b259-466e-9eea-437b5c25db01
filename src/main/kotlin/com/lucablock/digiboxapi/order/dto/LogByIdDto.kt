package com.lucablock.digiboxapi.order.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.util.*

data class LogByIdDto @QueryProjection
constructor (
  val orderNumber: String,
  val logs: List<LogsDetail?>
): Serializable

data class LogsDetail(
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val timeStamp: Date = Date(),
  val user: String,
  val status: String,
  val description: String? = null,
): Serializable