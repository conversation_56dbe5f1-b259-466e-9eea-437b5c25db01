package com.lucablock.digiboxapi.order.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.util.*

data class GetItemShippingDto
@QueryProjection
constructor(
  val orderId: Int,
  var orderNumber: String,
  var taxDetail: OrderTaxDto,
  val orderItemId: Int,
  var item: ShippingItemDetailDto,
  var shippingDetail: OrderItemShippingDto,
) : Serializable

data class ItemDetailDto
@QueryProjection
constructor(
  var itemId: Int,
  var width: Int,
  var length: Int,
  var height: Int,
  var amount: Int,
  var printing: Int,
  var model: OrderItemModelDto?,
  var material: OrderItemMaterialDto?,
  var coating: OrderItemCoatingDto?,
  var specialTechnics: List<OrderItemSpecialTechnicDto?>,
  var isArtwork: Boolean,
  var artworkUrl: String? = null,
  var productDemo: Int,
  var description: String? = null,
  var unitPrice: Double,
  var totalPrice: Double,
) : Serializable

data class ShippingItemDetailDto
@QueryProjection
constructor(
  var itemId: Int,
  var width: Int,
  var length: Int,
  var height: Int,
  var amount: Int,
  var model: OrderItemModelDto?,
  var material: OrderItemMaterialDto?,
  var unitPrice: Double,
) : Serializable

data class OrderItemShippingDto
@QueryProjection
constructor(
  var shipping: Int,
  var shippingCost: Double = 0.0,
  var trackingNumber: String? = null,
  var recipientName: String? = null,
  var phoneNumber: String? = null,
  var address: String? = null,
  var district: String? = null,
  var subDistrict: String? = null,
  var province: String? = null,
  var zipCode: String? = null,
  var email: String? = null,
  var isConfirmReceipt: Boolean = false,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val deliveryDate: Date? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var receivedDate: Date? = null,
  val shippingName: String? = null,
  val receivedDescription: String? = null
) : Serializable

data class OrderItemDto
@QueryProjection
constructor(
  val id: Int = 0,
  var status: Int,
  var item: ItemDetailDto,
  var shippingDetail: OrderItemShippingDto,
) : Serializable

data class OrderItemModelDto
@QueryProjection
constructor(
  var id: Long,
  var name: String,
  var imageUrl: String,
  var productId: Long,
) : Serializable

data class OrderItemMaterialDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
//  var gram: Int,
  var imageUrl: String,
) : Serializable

data class OrderItemCoatingDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
  var imageUrl: String,
) : Serializable

data class OrderItemSpecialTechnicDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
  var imageUrl: String,
  var width: Int,
  var height: Int
) : Serializable