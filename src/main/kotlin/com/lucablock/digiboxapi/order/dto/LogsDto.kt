package com.lucablock.digiboxapi.order.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.util.Date

data class LogsDto @QueryProjection
constructor (
  val orderNumber: String,
  val user: String,
  val status: String,
  val description: String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val timeStamp: Date = Date(),
) : Serializable
