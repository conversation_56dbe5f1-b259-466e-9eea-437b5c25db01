package com.lucablock.digiboxapi.order.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.customize.constant.ArtWorkEnum
import com.lucablock.digiboxapi.entity.DiscountCategory
import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailDto
import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodDto
import com.lucablock.digiboxapi.user.dto.AddressDto
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.time.LocalDateTime
import java.util.*

data class AdminOrderDto
@QueryProjection
constructor(
  val id: Int = 0,
  val customer: CustomerDetailDto?,
  var orderNumber: String,
  var totalPrice: Double,
  var vat: Double,
  var shippingCost: Double,
  var paymentStatus: Boolean? = false,
  var isConfirm: Boolean? = false,
  var status: Int,
  var isDelete: Boolean? = false,
  var isRevision: Boolean? = false,
  var description: String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),
  var taxDetail: OrderTaxDto,
  var orderItems: List<OrderItemDto>? = null,
  var paymentTransaction: List<PaymentTransactionDto>? = null,
  var logs: List<LogsDto>? = null,
  var discounts: List<OrderDiscountDto?> = emptyList()
) : Serializable

data class OrderTaxDto
@QueryProjection
constructor(
  var isTax: Boolean = false,
  var taxPayerType: Int? = null,
  var taxId: String? = null,
  var taxPayerName: String? = null,
  var phoneNumber: String? = null,
  var address: String? = null,
  var district: String? = null,
  var subDistrict: String? = null,
  var province: String? = null,
  var zipCode: String? = null,
  var email: String? = null,
) : Serializable

data class PaymentTransactionDto
@QueryProjection
constructor(
  val id: Int = 0,
  var orderId: Int,
  var paymentAmount: Double = 0.0,
  var paymentType: String,
  var bankAccountName: String,
  var bankAccountNumber: String,
  var bankName: String,
  var dateTime: String,
  var description: String? = null,
  var slipUrl: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date()
) : Serializable

data class GetUserOrderDto
@QueryProjection
constructor(
  val waitingQuotation: OrderByStatusDto,
  val waitingPayment: OrderByStatusDto,
  val verifyPayments: OrderByStatusDto,
  val production: OrderByStatusDto,
  val successful: OrderByStatusDto,
  val canceled: OrderByStatusDto
) : Serializable

data class OrderByStatusDto
@QueryProjection
constructor(
  val orders: List<OrderDetailDto?>,
  val count: Int,
) : Serializable

data class OrderDetailDto
@QueryProjection
constructor(
  val id: Int = 0,
  val customer: CustomerDetailDto?,
  var orderNumber: String,
  var totalPrice: Double,
  var vat: Double,
  var shippingCost: Double,
  var paymentStatus: Boolean? = false,
  var isConfirm: Boolean? = false,
  var status: Int,
  var isDelete: Boolean? = false,
  var isRevision: Boolean? = false,
  var description: String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),
  var taxDetail: OrderTaxDto,
  var orderItems: List<OrderItemDto>,
  var revisions: List<RevisionDetailDto?>,
  var discounts: List<OrderDiscountDto?> = mutableListOf()
) : Serializable

data class CustomerDetailDto
@QueryProjection
constructor(
  val id: Long,
  val fullName: String?,
  val phoneNumber: String?,
  val email: String?,
) : Serializable

data class RevisionDetailDto
@QueryProjection
constructor(
  val id: Int,
  val revisionNumber: Int,
  val detail: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var timestamp: Date = Date(),
) : Serializable

data class GetOrderListDto
@QueryProjection
constructor(
  val id: Int,
  var orderNumber: String,
  var orderItems: List<OrderItemListDto>,
) : Serializable

data class OrderItemListDto
@QueryProjection
constructor(
  val id: Int = 0,
  var item: ItemListDto,
) : Serializable

data class ItemListDto
@QueryProjection
constructor(
  var id: Int,
  var width: Int,
  var length: Int,
  var height: Int,
  var amount: Int,
  var model: OrderItemModelDto?,
  var material: OrderItemMaterialDto?,
  var unitPrice: Double,
  var totalPrice: Double,
) : Serializable

data class OrderDiscountDto
@QueryProjection
constructor(
  val id: Int = 0,
  var title: String,
  var discountCode: String,
  var percentage: Int,
  var maxDiscount: Int,
  var minPrice: Int,
  var maxUsage: Int,
  var condition: String,
  var description: String,
  var discountImage: String,
  var isDeleted: Boolean,
  var isActive: Boolean,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var startDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var endDate: Date,
  val totalDiscount: Double = 0.0,
  val category: DiscountCategory? = null
) : Serializable

data class CountOrderByStatusDto
@QueryProjection
constructor(
  var statusId: Int,
  var statusName: String? = null,
  var amount: Long,
) : Serializable

data class OrderBarChartDto
@QueryProjection
constructor(
  var date: Long,
  var status: List<CountOrderByStatusDto>
) : Serializable

data class LatestOrderDto
@QueryProjection
constructor(
  val orderNumber: String,
  val orderItems: List<OrderItemDto> = mutableListOf(),
  val timeDuration: Long,
)

data class CheckoutOrderDto
@QueryProjection
constructor(
  val id: Int,
  val orderNo: String,
  val userId: Long,
  val status : Int,
  val statusName: String?,
  val totalPrice : Double,
  val vat : Double,
  val vatCost: Double,
  val netPrice : Double,
  val shippingCost : Double,
  val receiptType: Int,
  val receiptTypeName: String,
  val shippingType: Int,
  val shippingName: String,
  val shippingSubType: Int? = null,
  val shippingSubTypeName: String? = null,
  val pickupName: String? = null,
  val pickupTell: String? = null,
  val isDeleted: Boolean = false,
  val taxAddress : AddressOrderDto? = null,
  val shippingAddress : AddressOrderDto? = null,
  val checkoutItem: List<CheckoutItemDto>,
)

data class CheckoutItemDto
@QueryProjection
constructor(
  val id: Int,
  val orderId: Int,
  val customizeId: Long,
  val shippingCostItem: Double,
  val totalPrice: Double,
  val trackingNumber: String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val deliveryDate: LocalDateTime? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val periodDate: LocalDateTime? = null,
)

data class SummaryOrderItemDto
@QueryProjection
constructor(
  val orderItemId: Int,
  val orderId: Int,
  val customizeId: Long,
  val productName: String,
  val modelName : String,
  val shippingCostItem: Double,
  val trackingNumber: String? = null,
  val itemPrice: Double,
  val addressName: String? = null,

)

data class SummaryOrderDto
@QueryProjection
constructor(
  val id: Int,
  val orderNo: String,
  val shippingCost: Double,
  val vat: Double,
  val couponListDto: List<CouponListDto> = mutableListOf(),
  val totalPrice: Double,
  val orderItem : List<SummaryOrderItemDto>
)

data class CouponListDto
@QueryProjection
constructor(
  val couponId: Long,
  val couponName: String?,
  val percentage: Int?,
  val maxPrice: Double?,
  val minPrice: Double?,
)

data class DraftOrderDto
@QueryProjection
constructor(
  val orderId: Int,
  val shippingCost: Double,
  val vat: Double,
  val totalPrice: Double,
  val couponListDto: List<CouponListDto> = mutableListOf(),
)

data class OrderPageDto
@QueryProjection
constructor(
  val orderId: Int,
  val orderNo: String?,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date,
  val totalPrice: Double?,
  val netPrice: Double?,
  val orderItem: List<OrderCustomDto>? = mutableListOf(),
)

data class OrderCustomDto
@QueryProjection
constructor(
  val customizeId: Long,
  val productName: String?,
  val modelName: String?,
  val price: Double,
  val width: Number,
  val height: Double,
  val length: Double?,
  val material: String,
  val gram: Int,
  val printing: String,
  val coating: String?,
  val specialTechnic: List<String>? = mutableListOf(),
  val modelSizeConfigDetail: ModelSizeConfigDetailDto?,
  val productPeriod: ProductPeriodDto?,
  val zipcode: String,
)

data class OrderIdDetailDto
@QueryProjection constructor(
  val id: Int,
  val orderNo: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date,
  val status: Int,
  val statusName: String?,
  val totalPrice: Double,
  val shippingCost: Double,
  val vat: Double,
  val vatCost: Double,
  val netPrice: Double,
  val shippingType : Int,
  val shippingTypeName: String,
  val shippingSubType: Int? = null,
  val shippingSubTypeName: String? = null,
  val taxAddress : AddressOrderDto? = null,
  val shippingAddress : AddressOrderDto? = null,
  val orderItem : List<CustomOrderDetailDto> = mutableListOf(),
)

data class CustomOrderDetailDto
@QueryProjection constructor(
  val customizeId : Long,
  val productName: String?,
  val modelName: String,
  val price: Double,
  val width: Double,
  val height: Double,
  val length: Double?,
  val unfolded: String,
  val material: String,
  val gram: Int,
  val printing: String,
  val coating: String?,
  val isArtwork: Boolean,
  val artworkEnum: String,
  val dieLine : String? = null,
  val specialTechnic : List<SpecialTechnicConfigDetailDto>? = mutableListOf(),
  val modelSizeConfigDetail: ModelSizeConfigDetailDto?,
  val sampleProductEnum : Int,
  val sampleProductEnumName: String,
  val sampleProductEnumPrice : Double,
  val productPeriod: ProductPeriodDto?,
  val shippingTypeEnum: Int,
  val shippingTypeEnumName: String,
  val shippingAddress: AddressOrderDto? = null
)
data class SpecialTechnicConfigDetailDto
@QueryProjection constructor(
  val specialTechnic : String,
  val areaSize : Int,
  val price : Double,
)

data class OrderStatusCountDto
@QueryProjection constructor(
  val id: Long,
  val status: String,
  val count: Long,
)

data class AddressOrderDto
@QueryProjection constructor(
  val name: String? = null,
  val phoneNumber: String? = null,
  val email: String? = null,
  val address: String? = null,
  val province: String? = null,
  val district: String? = null,
  val subDistrict: String? = null,
  val zipCode: String? = null,
  val taxId: String? = null,
  val isTax: Boolean,
)
