package com.lucablock.digiboxapi.order.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.cart.repository.CartRepository
import com.lucablock.digiboxapi.coupon.repository.CouponRepository
import com.lucablock.digiboxapi.customize.repository.CustomizeRepository
import com.lucablock.digiboxapi.discount.repository.DiscountRepository
import com.lucablock.digiboxapi.discount.repository.OrderDiscountRepository
import com.lucablock.digiboxapi.discount.repository.UserDiscountRepository
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.order.constant.OrderStatusEnum
import com.lucablock.digiboxapi.order.dto.*
import com.lucablock.digiboxapi.order.repository.*
import com.lucablock.digiboxapi.order.request.*
import com.lucablock.digiboxapi.order.service.OrderService
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.repository.AddressRepository
import com.lucablock.digiboxapi.user.repository.UserRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.stereotype.Service
import org.thymeleaf.spring6.SpringTemplateEngine
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Service
class OrderServiceImpl @Autowired constructor(
  private val userRepository: UserRepository,
  private val cartRepository: CartRepository,
  private val orderRepository: OrderRepository,
  private val orderItemRepository: OrderItemRepository,
  private val paymentTransactionRepository: PaymentTransactionRepository,
  private val orderLogsRepository: OrderLogsRepository,
  private val emailSender: JavaMailSender,
  private val templateEngine: SpringTemplateEngine,
  private val s3Service: S3Service,
  private val discountRepository: DiscountRepository,
  private val userDiscountRepository: UserDiscountRepository,
  private val orderDiscountRepository: OrderDiscountRepository,
  private val orderAddressRepository: OrderAddressRepository,
  private val addressRepository: AddressRepository,
  private val customizeRepository: CustomizeRepository,
  private val couponRepository: CouponRepository,
  private val orderStatusRepository: OrderStatusRepository,
) : OrderService {
  @Transactional
  override fun createOrderCheckout(
    userPrincipal: UserPrincipal,
    createCheckoutRequest: CreateCheckoutRequest,
  ): CheckoutOrderDto {
    val today = LocalDate.now()
    val date = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
    val lastOrderNo = orderRepository.findMaxOrderNoForDate(date)
    val orderNo = generateOrderNo(lastOrderNo)

    val order = orderRepository.save(
      Order(
        userId = userPrincipal.getUserId(),
        orderNo = "OD$orderNo",
        totalPrice = 0.0,
        vat = createCheckoutRequest.vat,
        status = OrderStatusEnum.WAITING_PAYMENT.value,
        shippingCost = 0.0,
        shippingType = createCheckoutRequest.shippingType,
        receiptType = createCheckoutRequest.receiptType,
        shippingSubType = createCheckoutRequest.shippingSubType,
        pickupName = createCheckoutRequest.pickupName,
        pickupTell = createCheckoutRequest.pickupTell,
        vatCost = 0.0,
      )
    )
    createCheckoutRequest.orderAddress.map {
      val address = addressRepository.findById(it).orElseThrow {
        NotFoundException("ไม่พบข้อมูลที่อยู่")
      }
      orderAddressRepository.save(
        OrderAddress(
          orderId = order.id,
          isTax = address.isTax,
          name = address.name,
          phoneNumber = address.phoneNumber,
          address = address.address,
          email = address.email,
          province = address.province,
          district = address.district,
          subDistrict = address.subDistrict,
          zipcode = address.zipCode,
          order = order,
          taxId = address.taxId,
          taxPayerType = address.taxPayerType,
        )
      )
    }

    createCheckoutRequest.checkoutItems.map {
      val itemAddress =
        if (it.addressId != null) {
          addressRepository.findById(it.addressId).orElseThrow {
            NotFoundException("ไม่พบข้อมูลที่อยู่")
          }
        } else {
          null
        }
      val customize = customizeRepository.findById(it.customizeId).orElseThrow{
        NotFoundException("ไม่พบข้อมูลปรับแต่งสินค้า")
      }
      val itemPrice = (customize.modelSizeConfigDetail.price * customize.modelSizeConfigDetail.amount)
      var specialTechnicCost = customize.customizeSpecialTechnic.sumOf { it.specialTechnicConfig.price }
      specialTechnicCost *= customize.modelSizeConfigDetail.amount
      val coatingCost = customize.customizeCoating.sumOf { it.modelSizeConfigCoating?.coating?.price ?: 0.0}
      val totalPrice = (itemPrice + customize.productPeriod.price) + specialTechnicCost + coatingCost
      orderItemRepository.save(
        OrderItem(
          orderId = order.id,
          customizeId = customize.id,
          customize = customize,
          shippingCost = it.shippingCostItem,
          deliveryDate = it.deliveryDate,
          periodDate = it.periodDate,
          status = OrderStatusEnum.WAITING_PAYMENT.value,
          itemPrice = itemPrice,
          totalPrice = totalPrice,
          addressName = itemAddress?.name,
          email = itemAddress?.email,
          phoneNumber = itemAddress?.phoneNumber,
          address = itemAddress?.address,
          zipcode = itemAddress?.zipCode,
          province = itemAddress?.province,
          district = itemAddress?.district,
          subDistrict = itemAddress?.subDistrict,
        )
      )

      cartRepository.deleteByUserIdAndCustomizeId(
        userPrincipal.getUserId().toInt(), customize.id
      )

      order.totalPrice += totalPrice
      order.totalPrice += it.shippingCostItem
    }
    val totalPrice = (order.totalPrice + order.shippingCost)
    val vatCost = totalPrice * (order.vat / 100.0)
    order.netPrice = totalPrice + vatCost
    order.vatCost = vatCost
    val updateOrder = orderRepository.save(order)

    return orderRepository.getCheckoutOrderById(updateOrder.id)
  }


  override fun summaryOrder(orderId: Long): SummaryOrderDto {
    return orderRepository.getSummaryOrderById(orderId)
  }

  override fun getOrderPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    status: Int?,
    userPrincipal: UserPrincipal
  ): Page<OrderPageDto> {
    return orderRepository.getOrderPage(
      pageable,
      ascending,
      search,
      status,
      userPrincipal
    )
  }

  override fun getOrderById(id: Long): OrderIdDetailDto {
    val order = orderRepository.findById(id).orElseThrow {
      NotFoundException("ไม่พบข้อมูลออเดอร์")
    }
    return orderRepository.getOrderDetailById(order.id)
  }

  override fun getOrderStatusCounts(): List<OrderStatusCountDto> {
    val status = orderRepository.getOrderStatusCounts()
    return status.map {
      OrderStatusCountDto(
        id = it.id,
        status = it.status,
        count = it.count,
      )
    }
  }

  override fun updateOrderStatus(id: Long): Boolean {
    val order = orderRepository.findById(id).orElseThrow{
      NotFoundException("ไม่พบข้อมูลออเดอร์")
    }
    val statusMap = orderStatusRepository.findAll()
      .associateBy { it.sort }

    val requiredSorts = listOf(1, 2, 3, 4, 5, 6)
    for (sort in requiredSorts) {
      if (!statusMap.containsKey(sort)) {
        throw BadRequestException("ไม่พบสถานะ")
      }
    }
    val currentStatus = statusMap.getValue(order.status)

    order.status = when (currentStatus.sort) {
      1 -> statusMap.getValue(2).id.toInt()
      2 -> statusMap.getValue(3).id.toInt()
      3 -> statusMap.getValue(4).id.toInt()
      4 -> statusMap.getValue(5).id.toInt()
      5 -> statusMap.getValue(6).id.toInt()
      6 -> throw BadRequestException("ไม่สามารถอัปเดตสถานะออเดอร์ได้ อยู่ในสถานะสำเร็จแล้ว")
      else -> throw BadRequestException("สถานะไม่ถูกต้อง")
    }
    orderRepository.save(order)
    return true
  }

  private fun generateOrderNo(lastOrderNo: Long?): String {
    val today = LocalDate.now()
    val date = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
    val newRunning = if (lastOrderNo != null) {
      lastOrderNo.toString().takeLast(5).toInt() + 1
    } else {
      1
    }
    return date + String.format("%05d", newRunning)
  }
}


