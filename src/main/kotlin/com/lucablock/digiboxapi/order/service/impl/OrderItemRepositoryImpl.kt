package com.lucablock.digiboxapi.order.service.impl

import com.lucablock.digiboxapi.entity.QOrderItem
import com.lucablock.digiboxapi.order.dto.*
import com.lucablock.digiboxapi.order.repository.OrderItemRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.stereotype.Service

@Service
class OrderItemRepositoryImpl: OrderItemRepositoryCustom {
}