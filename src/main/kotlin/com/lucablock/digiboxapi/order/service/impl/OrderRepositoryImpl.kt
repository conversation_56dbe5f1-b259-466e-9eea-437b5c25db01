package com.lucablock.digiboxapi.order.service.impl

import com.lucablock.digiboxapi.customize.constant.ArtWorkEnum
import com.lucablock.digiboxapi.customize.constant.SampleProductEnum
import com.lucablock.digiboxapi.customize.constant.ShippingTypeEnum
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.order.constant.ReceiptTypeEnum
import com.lucablock.digiboxapi.order.constant.ShippingSubType
import com.lucablock.digiboxapi.order.dto.*
import com.lucablock.digiboxapi.order.repository.OrderRepositoryCustom
import com.lucablock.digiboxapi.user.dto.AddressDto
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.querydsl.core.types.Projections
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.round

@Service
class OrderRepositoryImpl : OrderRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qOrder = QOrder.order
  private val qOrderItem = QOrderItem.orderItem
  private val qCouponUsageLog = QCouponUsageLog.couponUsageLog
  private val qCoupon = QCoupon.coupon
  override fun getOrderPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    status: Int?,
    userPrincipal: UserPrincipal
  ): Page<OrderPageDto> {
    var criteria = qOrder.isDeleted.eq(false).and(qOrder.userId.eq(userPrincipal.getUserId()))
    if (search != null) {
      criteria = criteria.and(qOrder.orderNo.containsIgnoreCase(search))
    }
    if (status != null) {
      criteria = criteria.and(qOrder.status.eq(status))
    }

    val sort = if (ascending) {
      qOrder.id.desc()
    } else {
      qOrder.id.asc()
    }

    val orders = queryFactory
      .select(qOrder)
      .from(qOrder)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()

    val orderIds = orders.map { it.id }

    // Fetch order items with proper joins to avoid lazy loading issues
    val orderItems = if (orderIds.isNotEmpty()) {
      val qCustomize = QCustomize.customize

      queryFactory
        .selectFrom(qOrderItem)
        .leftJoin(qOrderItem.customize, qCustomize).fetchJoin()
        .where(qOrderItem.orderId.`in`(orderIds))
        .fetch()
        .groupBy { it.orderId }
    } else {
      emptyMap()
    }

    val resultDto = orders.map { order ->
      OrderPageDto(
        orderId = order.id,
        orderNo = order.orderNo,
        createdDate = order.createdDate,
        totalPrice = order.totalPrice,
        netPrice = order.netPrice,
        orderItem = orderItems[order.id]?.map { orderItem ->
          OrderCustomDto(
            customizeId = orderItem.customizeId,
            productName = orderItem.customize.model.product.name,
            modelName = orderItem.customize.model.name,
            price = orderItem.itemPrice,
            width = orderItem.customize.modelSize.width,
            height = orderItem.customize.modelSize.height,
            length = orderItem.customize.modelSize.length,
            material = orderItem.customize.materialConfig.materials.name,
            gram = orderItem.customize.materialConfig.grams.gsm,
            printing = orderItem.customize.printing.name,
            coating = orderItem.customize.modelSizeConfigDetail.modelSizeConfigCoating.firstOrNull()?.coating?.name,
            specialTechnic = orderItem.customize.customizeSpecialTechnic.map { spec -> spec.specialTechnicConfig.specialTechnic.name },
            modelSizeConfigDetail = orderItem.customize.modelSizeConfigDetail.toModelSizeConfigDetailDto(),
            productPeriod = orderItem.customize.productPeriod.toProductPeriodDto(),
            zipcode = orderItem?.zipcode ?: "",
          )
        } ?: emptyList()
      )
    }
    val total = queryFactory
      .select(qOrder.count())
      .from(qOrder)
      .where(criteria)
      .fetchOne() ?: 0L

    return PageImpl(resultDto, pageable, total)
  }

  override fun getOrderStatusCounts(): List<OrderStatusCountDto> {
    val order = QOrder.order
    val orderStatus = QOrderStatus.orderStatus

    return queryFactory
      .select(
        Projections.constructor(
          OrderStatusCountDto::class.java,
          orderStatus.id,
          orderStatus.status,
          order.id.count().coalesce(0L)
        )
      )
      .from(orderStatus)
      .leftJoin(order).on(order.orderStatus.id.eq(orderStatus.id))
      .groupBy(orderStatus.id, orderStatus.status)
      .orderBy(orderStatus.id.asc())
      .fetch()
  }

  override fun getOrderDetailById(orderId: Int): OrderIdDetailDto {
    val qOrderAddress = QOrderAddress.orderAddress
    val qOrderStatus = QOrderStatus.orderStatus
    val qCustomize = QCustomize.customize
    val qModel = QModel.model
    val qProduct = QProduct.product
    val qUnfoldedSize = QUnfoldedSize.unfoldedSize
    val qMaterialConfig = QMaterialConfig.materialConfig
    val qMaterials = QMaterials.materials
    val qGram = QGram.gram
    val qPrinting = QPrinting.printing
    val qModelSizeConfigDetail = QModelSizeConfigDetail.modelSizeConfigDetail
    val qProductPeriod = QProductPeriod.productPeriod
    val qCustomizeSpecialTechnic = QCustomizeSpecialTechnic.customizeSpecialTechnic
    val qSpecialTechnicConfig = QSpecialTechnicConfig.specialTechnicConfig
    val qSpecialTechnic = QSpecialTechnic.specialTechnic
    val qAreaSizePercentage = QAreaSizePercentage.areaSizePercentage

    val order = queryFactory
      .selectFrom(qOrder)
      .leftJoin(qOrder.orderStatus, qOrderStatus).fetchJoin()
      .where(qOrder.id.eq(orderId))
      .fetchOne() ?: throw NotFoundException("ไม่พบข้อมูลออเดอร์")

    val addresses = queryFactory
      .select(qOrderAddress)
      .from(qOrderAddress)
      .where(qOrderAddress.orderId.eq(orderId))
      .fetch()

    val taxAddress = addresses.firstOrNull { it.isTax }?.toAddressOrderDto()
    val shippingAddress = addresses.firstOrNull { !it.isTax }?.toAddressOrderDto()

    val orderItems = queryFactory
      .selectFrom(qOrderItem)
      .leftJoin(qOrderItem.customize, qCustomize).fetchJoin()
      .leftJoin(qCustomize.model, qModel).fetchJoin()
      .leftJoin(qModel.product, qProduct).fetchJoin()
      .leftJoin(qCustomize.unfoldedSize, qUnfoldedSize).fetchJoin()
      .leftJoin(qCustomize.materialConfig, qMaterialConfig).fetchJoin()
      .leftJoin(qMaterialConfig.materials, qMaterials).fetchJoin()
      .leftJoin(qMaterialConfig.grams, qGram).fetchJoin()
      .leftJoin(qCustomize.printing, qPrinting).fetchJoin()
      .leftJoin(qCustomize.modelSizeConfigDetail, qModelSizeConfigDetail).fetchJoin()
      .leftJoin(qCustomize.productPeriod, qProductPeriod).fetchJoin()
      .where(qOrderItem.orderId.eq(orderId))
      .fetch()

    val customizeIds = orderItems.map { it.customizeId }
    val specialTechnicsMap = if (customizeIds.isNotEmpty()) {
      queryFactory
        .selectFrom(qCustomizeSpecialTechnic)
        .leftJoin(qCustomizeSpecialTechnic.specialTechnicConfig, qSpecialTechnicConfig).fetchJoin()
        .leftJoin(qSpecialTechnicConfig.specialTechnic, qSpecialTechnic).fetchJoin()
        .leftJoin(qSpecialTechnicConfig.areaSizePercentage, qAreaSizePercentage).fetchJoin()
        .where(qCustomizeSpecialTechnic.customizeId.`in`(customizeIds))
        .fetch()
        .groupBy { it.customizeId }
    } else {
      emptyMap()
    }

    val modelSizeConfigDetailIds = orderItems.mapNotNull { it.customize.modelSizeConfigDetail.id }
    val coatingsMap = if (modelSizeConfigDetailIds.isNotEmpty()) {
      val qModelSizeConfigCoating = QModelSizeConfigCoating.modelSizeConfigCoating
      val qCoating = QCoating.coating
      queryFactory
        .selectFrom(qModelSizeConfigCoating)
        .leftJoin(qModelSizeConfigCoating.coating, qCoating).fetchJoin()
        .where(qModelSizeConfigCoating.modelSizeConfigDetailId.`in`(modelSizeConfigDetailIds))
        .fetch()
        .groupBy { it.modelSizeConfigDetailId }
    } else {
      emptyMap()
    }

    val orderItemDto = orderItems.map { item ->
      val specialTechnics = specialTechnicsMap[item.customizeId] ?: emptyList()
      val coatings = item.customize.modelSizeConfigDetail.id.let { coatingsMap[it] } ?: emptyList()

      CustomOrderDetailDto(
        customizeId = item.customizeId,
        productName = item.customize.model.product.name,
        modelName = item.customize.model.name,
        price = item.itemPrice.roundTo2Decimal(),
        width = item.customize.width,
        height = item.customize.height,
        length = item.customize.length,
        unfolded = item.customize.unfoldedSize?.name ?: "",
        material = item.customize.materialConfig.materials.name,
        gram = item.customize.materialConfig.grams.gsm,
        printing = item.customize.printing.name,
        coating = coatings.firstOrNull()?.coating?.name,
        isArtwork = item.customize.isArtwork,
        artworkEnum = ArtWorkEnum.fromValue(item.customize.isArtwork).description,
        dieLine = item.customize.dieLine,
        specialTechnic = specialTechnics.map { special ->
          SpecialTechnicConfigDetailDto(
            specialTechnic = special.specialTechnicConfig.specialTechnic.name,
            areaSize = special.specialTechnicConfig.areaSizePercentage.percentage,
            price = special.specialTechnicConfig.price
          )
        },
        modelSizeConfigDetail = item.customize.modelSizeConfigDetail.toModelSizeConfigDetailDto(),
        sampleProductEnum = item.customize.sampleProduct,
        sampleProductEnumName = SampleProductEnum.fromValue(item.customize.sampleProduct).description,
        sampleProductEnumPrice = SampleProductEnum.fromValue(item.customize.sampleProduct).price,
        productPeriod = item.customize.productPeriod.toProductPeriodDto(),
        shippingTypeEnum = item.customize.shippingType,
        shippingTypeEnumName = ShippingTypeEnum.fromValue(item.customize.shippingType).description,
        shippingAddress = item.toAddressOrderDto()
      )
    }

    return OrderIdDetailDto(
      id = order.id,
      orderNo = order.orderNo,
      createdDate = order.createdDate,
      status = order.status,
      statusName = order.orderStatus?.status,
      totalPrice = order.totalPrice.roundTo2Decimal(),
      shippingCost = order.shippingCost.roundTo2Decimal(),
      vat = order.vat,
      vatCost = order.vatCost.roundTo2Decimal(),
      netPrice = order.netPrice.roundTo2Decimal(),
      shippingType = order.shippingType,
      shippingTypeName = ShippingTypeEnum.fromValue(order.shippingType).description,
      shippingSubType = order.shippingSubType,
      shippingSubTypeName = order.shippingSubType?.let { ShippingSubType.fromValue(it)?.description },
      taxAddress = taxAddress,
      shippingAddress = shippingAddress,
      orderItem = orderItemDto
    )
  }

  override fun getCheckoutOrderById(orderId: Int): CheckoutOrderDto {
    val qOrderAddress = QOrderAddress.orderAddress
    val qOrderStatus = QOrderStatus.orderStatus

    val order = queryFactory
      .selectFrom(qOrder)
      .leftJoin(qOrder.orderStatus, qOrderStatus).fetchJoin()
      .where(qOrder.id.eq(orderId))
      .fetchOne() ?: throw NotFoundException("ไม่พบข้อมูลออเดอร์")

    val orderAddress = queryFactory
      .select(qOrderAddress)
      .from(qOrderAddress)
      .where(qOrderAddress.orderId.eq(orderId))
      .fetch()

    val taxAddress = orderAddress
      .firstOrNull { it.isTax }

    val shippingAddress = orderAddress
      .firstOrNull { !it.isTax }

    val orderItems = queryFactory
      .selectFrom(qOrderItem)
      .where(qOrderItem.orderId.eq(orderId))
      .fetch()

    val checkoutItems = orderItems.map { item ->
      CheckoutItemDto(
        id = item.id,
        orderId = item.orderId,
        customizeId = item.customizeId,
        totalPrice = item.totalPrice.roundTo2Decimal(),
        shippingCostItem = item.shippingCost.roundTo2Decimal(),
        trackingNumber = item.trackingNumber
      )
    }
    return CheckoutOrderDto(
      id = order.id,
      orderNo = order.orderNo,
      userId = order.userId,
      status = order.status,
      statusName = order.orderStatus?.status,
      totalPrice = order.totalPrice.roundTo2Decimal(),
      vat = order.vat,
      vatCost = order.vatCost.roundTo2Decimal(),
      netPrice = order.netPrice.roundTo2Decimal(),
      shippingCost = order.shippingCost.roundTo2Decimal(),
      receiptType = order.receiptType,
      receiptTypeName = ReceiptTypeEnum.fromValue(order.receiptType).description,
      shippingType = order.shippingType,
      shippingName = ShippingTypeEnum.fromValue(order.shippingType).description,
      shippingSubType = order.shippingSubType,
      shippingSubTypeName = order.shippingSubType?.let { ShippingSubType.fromValue(it)?.description },
      pickupName = order.pickupName,
      pickupTell = order.pickupTell,
      isDeleted = order.isDeleted,
      taxAddress = taxAddress?.toAddressOrderDto(),
      shippingAddress = shippingAddress?.toAddressOrderDto(),
      checkoutItem = checkoutItems
    )
  }

  override fun getSummaryOrderById(orderId: Long): SummaryOrderDto {
    val qCustomize = QCustomize.customize
    val qModel = QModel.model
    val qProduct = QProduct.product

    val order = queryFactory
      .selectFrom(qOrder)
      .where(qOrder.id.eq(orderId.toInt()))
      .fetchOne() ?: throw NotFoundException("ไม่พบข้อมูลออเดอร์")

    val coupons = queryFactory
      .select(
        Projections.constructor(
          CouponListDto::class.java,
          qCouponUsageLog.couponId,
          qCoupon.name,
          qCoupon.percentage,
          qCoupon.maxPrice,
          qCoupon.minPrice
        )
      )
      .from(qCouponUsageLog)
      .leftJoin(qCouponUsageLog.coupon, qCoupon)
      .where(qCouponUsageLog.orderId.eq(orderId.toInt()))
      .fetch()

    val orderItems = queryFactory
      .select(
        Projections.constructor(
          SummaryOrderItemDto::class.java,
          qOrderItem.id,
          qOrderItem.orderId,
          qOrderItem.customizeId,
          qProduct.name,
          qModel.name,
          qOrderItem.shippingCost,
          qOrderItem.trackingNumber,
          qOrderItem.itemPrice,
          qOrderItem.addressName
        )
      )
      .from(qOrderItem)
      .leftJoin(qOrderItem.customize, qCustomize)
      .leftJoin(qCustomize.model, qModel)
      .leftJoin(qModel.product, qProduct)
      .where(qOrderItem.orderId.eq(orderId.toInt()))
      .fetch()

    return SummaryOrderDto(
      id = order.id,
      orderNo = order.orderNo,
      totalPrice = order.totalPrice.roundTo2Decimal(),
      shippingCost = order.shippingCost.roundTo2Decimal(),
      vat = order.vat,
      couponListDto = coupons,
      orderItem = orderItems.map { item ->
        item.copy(
          shippingCostItem = item.shippingCostItem.roundTo2Decimal(),
          itemPrice = item.itemPrice.roundTo2Decimal()
        )
      }
    )
  }

  private fun Double.roundTo2Decimal(): Double =
    BigDecimal(this).setScale(2, RoundingMode.HALF_UP).toDouble()
}