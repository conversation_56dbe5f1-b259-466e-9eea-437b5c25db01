package com.lucablock.digiboxapi.order.service

import com.lucablock.digiboxapi.order.dto.*
import com.lucablock.digiboxapi.order.request.CreateCheckoutRequest
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface OrderService {
  fun createOrderCheckout(
    userPrincipal: UserPrincipal,
    createCheckoutRequest: CreateCheckoutRequest,
  ):CheckoutOrderDto
  fun summaryOrder(orderId: Long): SummaryOrderDto
  fun getOrderPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    status: Int?,
    userPrincipal: UserPrincipal,
  ): Page<OrderPageDto>
  fun getOrderById(id: Long): OrderIdDetailDto
  fun getOrderStatusCounts(): List<OrderStatusCountDto>
  fun updateOrderStatus(id : Long):<PERSON><PERSON>an
}