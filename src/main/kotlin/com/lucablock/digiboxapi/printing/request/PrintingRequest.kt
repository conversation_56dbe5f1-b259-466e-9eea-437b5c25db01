package com.lucablock.digiboxapi.printing.request

import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive

class PrintingRequest {
  @NotBlank(message = "กรุณากรอกชื่อ")
  var name : String = ""

  @NotBlank(message = "กรุณาอัปโหลดรูปภาพระบบพิมพ์")
  var imageUrl : String = ""

  @NotNull(message = "กรุณากรอกจำนวนขั้นต่ำที่สามารถผลิตได้ของระบบพิมพ์นี้")
  @Positive(message = "กรุณากรอกจำนวนที่ถูกต้อง")
  var minPrint : Int = 0

  @NotNull(message = "กรุณากรอกจำนวนสูงสุดที่สามารถผลิตได้ของระบบพิมพ์นี้")
  @Positive(message = "กรุณากรอกจำนวนที่ถูกต้อง")
  var maxPrint : Int = 0
  @Valid
  var machine : List<MachineRequest> = mutableListOf()
}
class MachineRequest {
  @NotBlank(message = "กรุณากรอกชื่อเครื่องพิมพ์")
  var name : String = ""
}