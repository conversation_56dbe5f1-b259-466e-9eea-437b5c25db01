package com.lucablock.digiboxapi.printing.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.entity.Printing
import com.lucablock.digiboxapi.entity.PrintingConfig
import com.querydsl.core.annotations.QueryProjection
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

data class PrintingDto
@QueryProjection constructor(
  val id: Long,
  val name: String,
  val imageUrl: String,
  val minPrint: Int,
  val maxPrint: Int,
  val isActive: Boolean,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
  val machine : List<MachineListDto> = mutableListOf()
)
data class MachineDto
@QueryProjection constructor(
  val id: Long,
  val name: String,
  val printingId: Long,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
)
data class MachineListDto
@QueryProjection constructor(
  val id: Long,
  val name: String,
)
data class PrintingListDto
@QueryProjection constructor(
  val id: Long,
  val name: String,
  val imageUrl: String,
)
data class PrintingListPrintingConfigDto
@QueryProjection constructor(
  val printingConfigId: Long,
  val printing: PrintingDto?,
)