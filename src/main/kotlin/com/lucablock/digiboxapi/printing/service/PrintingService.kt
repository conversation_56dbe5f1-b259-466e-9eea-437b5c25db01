package com.lucablock.digiboxapi.printing.service

import com.lucablock.digiboxapi.printing.dto.PrintingDto
import com.lucablock.digiboxapi.printing.dto.PrintingListPrintingConfigDto
import com.lucablock.digiboxapi.printing.request.PrintingRequest

interface PrintingService {
  fun createPrinting(printingRequest: PrintingRequest):PrintingDto
  fun updatePrinting(id: Long, printingRequest: PrintingRequest):PrintingDto
  fun getAllPrinting():List<PrintingDto>
  fun getPrintingById(id: Long):PrintingDto
  fun deletePrinting(id: Long):Boolean
  fun updateStatus(id: Long):Boolean
  fun getPrintingByModelSuzeConfigId(modelSizeConfigId: Long):List<PrintingListPrintingConfigDto>
}