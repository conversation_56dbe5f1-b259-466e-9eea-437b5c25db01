package com.lucablock.digiboxapi.printing.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.Machine
import com.lucablock.digiboxapi.entity.Printing
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.modelsizeconfig.repository.ModelSizeConfigRepository
import com.lucablock.digiboxapi.printing.dto.PrintingDto
import com.lucablock.digiboxapi.printing.dto.PrintingListPrintingConfigDto
import com.lucablock.digiboxapi.printing.repository.MachineRepository
import com.lucablock.digiboxapi.printing.repository.PrintingRepository
import com.lucablock.digiboxapi.printing.request.PrintingRequest
import com.lucablock.digiboxapi.printing.service.PrintingService
import com.lucablock.digiboxapi.printingconfig.repository.PrintingConfigRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class PrintingServiceImpl @Autowired constructor(
  private val printingRepository: PrintingRepository,
  private val s3Service: S3Service,
  private val machineRepository: MachineRepository,
  private val modelSizeConfigRepository: ModelSizeConfigRepository,
  private val printingConfigRepository: PrintingConfigRepository
) : PrintingService {
  override fun createPrinting(printingRequest: PrintingRequest): PrintingDto {
    printingRepository.existsByNameIgnoreCase(printingRequest.name).let {
      if (it) {
        throw BadRequestException("ระบบพิมพ์นี้มีอยู่แล้ว")
      }
    }
    val printing = printingRepository.save(
      Printing(
        name = printingRequest.name,
        imageUrl = printingRequest.imageUrl,
        minPrint = printingRequest.minPrint,
        maxPrint = printingRequest.maxPrint,
      )
    )
    val machine = machineRepository.saveAll(
      printingRequest.machine.map {
        Machine(
          name = it.name,
          printingId = printing.id
        )
      }
    )
    printing.machine = machine
    val urlFile = s3Service.moveFile(printingRequest.imageUrl).url
    printing.imageUrl = urlFile
    val savedPrinting = printingRepository.save(printing)
    return savedPrinting.toPrintingDto()
  }

  override fun updatePrinting(id: Long, printingRequest: PrintingRequest): PrintingDto {
    val printing = printingRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์นี้")
    }
    printingRepository.existsByNameIgnoreCaseAndIdNot(printingRequest.name, id).let {
      if (it) {
        throw BadRequestException("ระบบพิมพ์นี้มีอยู่แล้ว")
      }
    }
    val machine = machineRepository.findAllByPrintingId(id)
    machineRepository.deleteAll(machine)

    printing.name = printingRequest.name
    printing.minPrint = printingRequest.minPrint
    printing.maxPrint = printingRequest.maxPrint

    val savedMachines = machineRepository.saveAll(
      printingRequest.machine.map {
        Machine(
          name = it.name,
          printingId = printing.id
        )
      }
    )

    if (printingRequest.imageUrl != printing.imageUrl) {
      s3Service.deleteFile(printing.imageUrl)
      val newFileUrl = s3Service.moveFile(printingRequest.imageUrl).url
      printing.imageUrl = newFileUrl
    }
    val updatePrinting = printingRepository.save(printing)
    return updatePrinting.toPrintingDto()
  }

  override fun getAllPrinting(): List<PrintingDto> {
    return printingRepository.findAllByIsActiveTrueOrderByIdAsc().map { it.toPrintingDto() }
  }

  override fun getPrintingById(id: Long): PrintingDto {
    val printing = printingRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์นี้")
    }
    return printing.toPrintingDto()
  }

  override fun deletePrinting(id: Long): Boolean {
    val printing = printingRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์นี้")
    }
    val printingConfig = printingConfigRepository.findAllByPrintingId(id)
    if(printingConfig.isNotEmpty()){
      throw BadRequestException("ไม่สามารถลบระบบพิมพ์นี้ได้ เนื่องจากกำลังใช้งานอยู่")
    }else{
      machineRepository.deleteAll(printing.machine)
      s3Service.deleteFile(printing.imageUrl)
      printingRepository.delete(printing)
    }
    return true
  }

  override fun updateStatus(id: Long): Boolean {
    val printing = printingRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์นี้")
    }
    printing.isActive = !printing.isActive
    printingRepository.save(printing)
    return true
  }

  override fun getPrintingByModelSuzeConfigId(modelSizeConfigId: Long):List<PrintingListPrintingConfigDto>  {
    val modelSizeConfig = modelSizeConfigRepository.findById(modelSizeConfigId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดลสินค้านี้")
    }
    val printingConfig = printingConfigRepository.findAllByModelSizeConfigId(modelSizeConfig.id)
    val printing = printingRepository.findAllByPrintingConfigIn(printingConfig)

    return printingConfig.map {
        PrintingListPrintingConfigDto(
          printingConfigId = it.id,
          printing = it.printing?.toPrintingDto()
        )
    }
  }
}