package com.lucablock.digiboxapi.printing.repository

import com.lucablock.digiboxapi.entity.Printing
import com.lucablock.digiboxapi.entity.PrintingConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface PrintingRepository : JpaRepository<Printing, Long> {
  fun existsByNameIgnoreCase(name:String):Boolean
  fun existsByNameIgnoreCaseAndIdNot(name:String,id:Long):Boolean
  fun findAllByOrderByIdAsc():List<Printing>
  fun findAllByPrintingConfigIn(printingConfigs: MutableList<PrintingConfig>): MutableList<Printing>
  fun findAllByIsActiveTrueOrderByIdAsc(): MutableList<Printing>
}