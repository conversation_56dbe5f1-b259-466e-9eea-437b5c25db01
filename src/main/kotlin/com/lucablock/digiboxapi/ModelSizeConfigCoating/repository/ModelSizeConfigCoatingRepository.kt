package com.lucablock.digiboxapi.ModelSizeConfigCoating.repository

import com.lucablock.digiboxapi.entity.ModelSizeConfigCoating
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ModelSizeConfigCoatingRepository : JpaRepository<ModelSizeConfigCoating, Long> {
  fun findAllModelSizeConfigCoatingById(id: Long): MutableList<ModelSizeConfigCoating>
  fun deleteAllByModelSizeConfigDetailIdIn(modelSizeConfigDetailIds: List<Long>)
  fun findAllByModelSizeConfigDetailIdIn(modelSizeConfigDetailIds: List<Long>): MutableList<ModelSizeConfigCoating>
//  fun deleteAllByModelSizeConfigIdIn(modelSizeConfigIds: List<Long>)
}