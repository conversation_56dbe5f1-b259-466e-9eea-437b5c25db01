package com.lucablock.digiboxapi.holiday.service.impl

import com.lucablock.digiboxapi.entity.QHoliday
import com.lucablock.digiboxapi.holiday.dto.HolidayDto
import com.lucablock.digiboxapi.holiday.repository.HolidayRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class HolidayRepositoryImpl : HolidayRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qHoliday = QHoliday.holiday

  override fun getPageHoliday(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    holidayTypeId: Long?,
    isActive: Boolean?
  ): Page<HolidayDto> {
    var criteria = qHoliday.isNotNull

    if (isActive != null) {
      criteria = criteria.and(qHoliday.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qHoliday.name.containsIgnoreCase(search))
    }
    if (holidayTypeId != null){
      criteria = criteria.and(qHoliday.holidayTypeId.eq(holidayTypeId))
    }

    val sort = if (ascending) {
      qHoliday.id.asc()
    } else {
      qHoliday.id.desc()
    }

    val query = queryFactory
      .select(qHoliday)
      .from(qHoliday)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toHolidayDto()
      }

    val total = queryFactory
      .select(qHoliday)
      .from(qHoliday)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}