package com.lucablock.digiboxapi.holiday.service

import com.lucablock.digiboxapi.holiday.dto.CalendarDataDto
import com.lucablock.digiboxapi.holiday.dto.HolidayDto
import com.lucablock.digiboxapi.holiday.request.CalendarRequest
import com.lucablock.digiboxapi.holiday.request.HolidayRequest
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface HolidayService {
  fun createHoliday(holidayRequest: HolidayRequest):HolidayDto
  fun updateHoliday(id:Long,holidayRequest: HolidayRequest):HolidayDto
  fun getAllHoliday():List<HolidayDto>
  fun getHolidayById(id:Long):HolidayDto
  fun deletedHoliday(id: Long):Boolean
  fun getCalendarDataNow(): CalendarDataDto
  fun getCalendarData(month : Int, year : Int): CalendarDataDto
  fun updateActive(id:Long):Boolean
  fun updateRecurring(id:Long):Boolean
  fun getPageHoliday(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    holidayTypeId : Long?,
    isActive : Boolean?
  ): Page<HolidayDto>
}