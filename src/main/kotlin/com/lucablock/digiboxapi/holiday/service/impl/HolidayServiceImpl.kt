package com.lucablock.digiboxapi.holiday.service.impl

import com.lucablock.digiboxapi.companyworking.repository.CompanyWorkingRepository
import com.lucablock.digiboxapi.companyworkingconfig.repository.CompanyWorkingConfigRepository
import com.lucablock.digiboxapi.entity.Holiday
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.holiday.dto.*
import com.lucablock.digiboxapi.holiday.repository.HolidayRepository
import com.lucablock.digiboxapi.holiday.request.HolidayRequest
import com.lucablock.digiboxapi.holiday.service.HolidayService
import com.lucablock.digiboxapi.holidaytype.repository.HolidayTypeRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime

@Service
class HolidayServiceImpl @Autowired constructor(
  private val holidayRepository: HolidayRepository,
  private val holidayTypeRepository: HolidayTypeRepository,
  private val companyWorkingConfigRepository: CompanyWorkingConfigRepository,
  private val companyWorkingRepository: CompanyWorkingRepository
):HolidayService{
  @Transactional
  override fun createHoliday(holidayRequest: HolidayRequest): HolidayDto {
    holidayRepository.existsHolidaysByHolidayDate(holidayRequest.holidayDate).let {
      if(it){
        throw BadRequestException("วันหยุดนี้มีอยู่แล้ว")
      }
    }
    val now = LocalDateTime.now()
    if(holidayRequest.holidayDate < now){
      throw BadRequestException("กรุณาระบุวันหยุดที่ไม่เป็นอดีต")
    }
    val type = holidayTypeRepository.findById(holidayRequest.holidayTypeId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลประเภทวันหยุด")
    }
    val holiday = holidayRepository.save(
        Holiday(
          name = holidayRequest.name,
          description = holidayRequest.description,
          holidayDate = holidayRequest.holidayDate,
          holidayTypeId = type.id,
          isActive = holidayRequest.isActive,
          isRecurring = holidayRequest.isRecurring,
          holidayType = type,
          companyId = 1
        )
    )
    return holiday.toHolidayDto()
  }

  override fun updateHoliday(id: Long, holidayRequest: HolidayRequest): HolidayDto {
    val holiday = holidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    val type = holidayTypeRepository.findById(holidayRequest.holidayTypeId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลประเภทวันหยุด")
    }
    holidayRepository.existsHolidaysByHolidayDateAndIdNot(holidayRequest.holidayDate,id).let {
      if(it){
        throw BadRequestException("วันหยุดนี้มีอยู่แล้ว")
      }
    }
    holiday.name = holidayRequest.name
    holiday.description = holidayRequest.description
    holiday.holidayDate = holidayRequest.holidayDate
    holiday.holidayTypeId = type.id
    holiday.isActive = holidayRequest.isActive
    holiday.isRecurring = holidayRequest.isRecurring
    holiday.holidayType = type
    val updateHoliday = holidayRepository.save(holiday)
    return updateHoliday.toHolidayDto()
  }

  override fun getAllHoliday(): List<HolidayDto> {
    val holiday = holidayRepository.findAllHolidayByIsActiveTrueOrderByHolidayDateAsc()
    return holiday.map { it.toHolidayDto() }
  }

  override fun getHolidayById(id: Long): HolidayDto {
    val holiday = holidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    return holiday.toHolidayDto()
  }

  override fun deletedHoliday(id: Long): Boolean {
    val holiday = holidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    holidayRepository.delete(holiday)
    return true
  }

  override fun getCalendarDataNow(): CalendarDataDto {
    val now = LocalDate.now()

    val inactiveWorkingDays = companyWorkingConfigRepository
      .findAllCompanyWorkingConfigByCompanyIdAndIsActiveFalse(1)
      .mapNotNull { NORMAL_HOLIDAY[it.companyWorking!!.sort] }

    val workPerWeek = companyWorkingConfigRepository.findAllCompanyWorkingConfigByCompanyIdAndIsActiveTrue(1)
    val holiday = holidayRepository.findAllHolidayByHolidayDateMonthAndYear(now.monthValue,now.year)
    val holidayDates = holiday.map { it.holidayDate.toLocalDate() }.toSet()
    val workingDays = getWorkingDaysInMonth(now.monthValue, now.year, holidayDates).map { it.dayOfMonth }
    val monthInfo = MonthInfoDto(
      year = now.year,
      month = now.month.value,
      totalDays = now.lengthOfMonth(),
      workingDaysCount = workingDays.size,
      holidaysCount = holiday.size,
      workingPerWeek = workPerWeek.size
    )
    val holidays = holiday.map {
      CalendarHoliday(
        id = it.id,
        name = it.name,
        description = it.description,
        holidayDate = it.holidayDate,
        holidayTypeId = it.holidayTypeId,
        holidayTypeName = it.holidayType.name,
        holidayTypeColor = it.holidayType.color,
        isRecurring = it.isRecurring
      )
    }

    return CalendarDataDto(
//      workingDays = workingDays,
      holidays = holidays,
      normalHoliday = inactiveWorkingDays.map {
        NormalHolidayDto(
          dayNo = it.value,
          day = it.name
        )
      },
      monthInfo = monthInfo
    )
  }

  @Transactional
  override fun getCalendarData(month : Int, year : Int): CalendarDataDto {
    if (month < 1 || month > 12){
      throw BadRequestException("กรุณาระบุเดือนระหว่าง 1-12")
    }
    val now = LocalDate.now()
    checkOldHoliday(now.year)
    checkRecurring(year)
//    createHolidaysFromInactiveConfig(1,year,month)

    val date = LocalDate.of(year, month, 1)
    val workPerWeek = companyWorkingConfigRepository.findAllCompanyWorkingConfigByCompanyIdAndIsActiveTrue(1)

    val inactiveWorkingDays = companyWorkingConfigRepository
      .findAllCompanyWorkingConfigByCompanyIdAndIsActiveFalse(1)
      .mapNotNull { NORMAL_HOLIDAY[it.companyWorking!!.sort] }

    val holiday = holidayRepository.findAllHolidayByHolidayDateMonthAndYear(date.monthValue,date.year)
    val holidayDates = holiday.map { it.holidayDate.toLocalDate() }.toSet()

    val workingDays = getWorkingDaysInMonth(date.monthValue, date.year, holidayDates).map { it.dayOfMonth }

    val monthInfo = MonthInfoDto(
      year = date.year,
      month = date.month.value,
      totalDays = date.lengthOfMonth(),
      workingDaysCount = workingDays.size,
      holidaysCount = holiday.size,
      workingPerWeek = workPerWeek.size
    )
    val holidays = holiday.map {
      CalendarHoliday(
        id = it.id,
        name = it.name,
        description = it.description,
        holidayDate = it.holidayDate,
        holidayTypeId = it.holidayTypeId,
        holidayTypeName = it.holidayType.name,
        holidayTypeColor = it.holidayType.color,
        isRecurring = it.isRecurring
      )
    }

    return CalendarDataDto(
//      workingDays = workingDays,
      holidays = holidays.sortedBy { it.holidayDate },
      normalHoliday = inactiveWorkingDays.map {
        NormalHolidayDto(
          dayNo = it.value,
          day = it.name
        )
      },
      monthInfo = monthInfo
    )
  }

  override fun updateActive(id: Long): Boolean {
    val holiday = holidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    holiday.isActive = !holiday.isActive
    holidayRepository.save(holiday)
    return true
  }

  override fun updateRecurring(id: Long): Boolean {
    val holiday = holidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    holiday.isRecurring = !holiday.isRecurring
    holidayRepository.save(holiday)
    return true
  }

  override fun getPageHoliday(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    holidayTypeId: Long?,
    isActive: Boolean?
  ): Page<HolidayDto> {
    return holidayRepository.getPageHoliday(
      pageable,
      ascending,
      search,
      holidayTypeId,
      isActive
    )
  }


  //--------------------------------------- add-on func --------------------------------------------------------

  fun getWorkingDaysInMonth(month: Int, year: Int, holidayDates: Set<LocalDate>): List<LocalDate> {
    val start = LocalDate.of(year, month, 1)
    val end = start.withDayOfMonth(start.lengthOfMonth())

    val workingDays = mutableListOf<LocalDate>()
    var date = start

    val inactiveWorkingDays = companyWorkingConfigRepository
      .findAllCompanyWorkingConfigByCompanyIdAndIsActiveFalse(1)
      .mapNotNull { NORMAL_HOLIDAY[it.companyWorking!!.sort] }

    while (!date.isAfter(end)) {
      val isWeekend = date.dayOfWeek in inactiveWorkingDays
      if (!holidayDates.contains(date) && !isWeekend) {
        workingDays.add(date)
      }
      date = date.plusDays(1)
    }

    return workingDays
  }

  @Transactional
  fun createHolidaysFromInactiveConfig(companyId: Long, year: Int, month: Int): List<Holiday> {
    val type = holidayTypeRepository.findById(1).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลประเภทวันหยุด")
    }

    val inactiveWorkingDays = companyWorkingConfigRepository
      .findAllCompanyWorkingConfigByCompanyIdAndIsActiveFalse(companyId)
      .mapNotNull { NORMAL_HOLIDAY[it.companyWorking!!.sort] }

    val start = LocalDate.of(year, month, 1)
    val end = start.withDayOfMonth(start.lengthOfMonth())
    val holidays = mutableListOf<Holiday>()
    var date = start

    while (!date.isAfter(end)) {
      if (date.dayOfWeek in inactiveWorkingDays) {
        holidays.add(
          Holiday(
            name = "วันหยุดธรรมดา",
            description = null,
            holidayDate = date.atStartOfDay(),
            holidayTypeId = 1,
            isActive = true,
            isRecurring = false,
            holidayType = type,
            companyId = 1
          )
        )
      }
      date = date.plusDays(1)
    }
    val normalHolidays =
      holidayRepository.existsHolidaysByHolidayDateIn(holidays.map { it.holidayDate }.toMutableList()).let {
      if(!it){
        holidayRepository.deleteAllHolidayByCompanyIdAndHolidayTypeIdAndHolidayDateMonthAndYear(companyId,1, month,year)
        holidayRepository.saveAll(holidays)
      }else{
        emptyList()
      }
    }
    return normalHolidays.toMutableList()
  }

  fun checkRecurring(year: Int): Boolean{
    val recurringHoliday = holidayRepository.findByIsRecurringTrue()
    val recurringDatesInYear = holidayRepository.findAllByIsRecurringTrueAndYear(year)
      .map { it.holidayDate.toLocalDate() }
    val holidays = mutableListOf<Holiday>()

    for (holiday in recurringHoliday) {
      val recurringDate = holiday.holidayDate
      val month = recurringDate.month
      val day = recurringDate.dayOfMonth

      val newDate = LocalDate.of(year, month, day).atStartOfDay()

      if (!recurringDatesInYear.contains(newDate.toLocalDate())) {
        holidays.add(
          Holiday(
            name = holiday.name,
            description = holiday.description,
            holidayDate = newDate,
            holidayTypeId = holiday.holidayTypeId,
            isActive = true,
            isRecurring = true,
            holidayType = holiday.holidayType,
            companyId = holiday.companyId
          )
        )
      }
    }
    if (holidays.isNotEmpty()) {
      holidayRepository.saveAll(holidays)
      return true
    }else{
      return false
    }
  }

  fun checkOldHoliday(year: Int): Boolean{
    val yearToDelete = year - 3
    val holidays = holidayRepository.findAll()
    val holidayToDelete =  mutableListOf<Holiday>()
    for (holiday in holidays) {
      if (holiday.holidayDate.year < yearToDelete){
        holidayToDelete.add(holiday)
      }
    }
    return if (holidayToDelete.isNotEmpty()) {
      holidayRepository.deleteAll(holidayToDelete)
      true
    }else{
      false
    }
  }

  companion object {
    val NORMAL_HOLIDAY: Map<Int, DayOfWeek> = mapOf(
      1 to DayOfWeek.SUNDAY,
      2 to DayOfWeek.MONDAY,
      3 to DayOfWeek.TUESDAY,
      4 to DayOfWeek.WEDNESDAY,
      5 to DayOfWeek.THURSDAY,
      6 to DayOfWeek.FRIDAY,
      7 to DayOfWeek.SATURDAY
    )
  }
}