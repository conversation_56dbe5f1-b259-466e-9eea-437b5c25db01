package com.lucablock.digiboxapi.holiday.repository

import com.lucablock.digiboxapi.entity.Holiday
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Repository
interface HolidayRepository : JpaRepository<Holiday,Long>, HolidayRepositoryCustom {
  fun existsHolidaysByHolidayDate(holidayDate: LocalDateTime): Boolean
  fun existsHolidaysByHolidayDateAndIdNot(holidayDate: LocalDateTime, id: Long): Boolean
  fun findAllHolidayByIsActiveTrueOrderByHolidayDateAsc(): List<Holiday>
  fun findAllHolidayByHolidayTypeId(holidayTypeId: Long): MutableList<Holiday>

  @Query("SELECT h FROM Holiday h WHERE MONTH(h.holidayDate) = :month AND YEAR(h.holidayDate) = :year")
  fun findAllHolidayByHolidayDateMonthAndYear(@Param("month") month: Int, @Param("year") year: Int): List<Holiday>

  fun existsHolidaysByHolidayDateIn(holidayDates: MutableList<LocalDateTime>): Boolean

  @Modifying
  @Query("DELETE FROM Holiday h WHERE h.companyId = :companyId AND h.holidayTypeId = :holidayTypeId " +
      "AND MONTH(h.holidayDate) = :month AND YEAR(h.holidayDate) = :year")
  fun deleteAllHolidayByCompanyIdAndHolidayTypeIdAndHolidayDateMonthAndYear(
    @Param("companyId")companyId: Long,
    @Param("holidayTypeId")holidayTypeId: Long,
    @Param("month")month:Int,
    @Param("year")year:Int,
  )
  @Query("SELECT h FROM Holiday h WHERE h.isRecurring = true AND YEAR(h.holidayDate) = :year")
  fun findAllByIsRecurringTrueAndYear(@Param("year")year: Int):List<Holiday>
  @Query("SELECT h FROM Holiday h WHERE h.isRecurring = true")
  fun findByIsRecurringTrue():List<Holiday>

}