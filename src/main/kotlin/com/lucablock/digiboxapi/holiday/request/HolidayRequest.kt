package com.lucablock.digiboxapi.holiday.request

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Date

class HolidayRequest {
  @NotBlank(message = "กรุณากรอกชื่อวันหยุด")
  val name : String =""
  val description : String? = null
  @NotNull(message = "กรุณาระบุวันหยุด")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
  val holidayDate : LocalDateTime = LocalDateTime.now()
  @NotNull(message = "กรุณาระบุประเภทวันหยุด")
  val holidayTypeId : Long = 0
  @JsonProperty("isRecurring")
  val isRecurring : Boolean = false
  @JsonProperty("isActive")
  val isActive : Boolean = true
}

class CalendarRequest{
  @NotNull(message = "กรุณาระบุปี")
  val year : Int = 0
  @NotNull(message = "กรุณาระบุเดือน")
  val month : Int = 0
  val companyId : Long = 0
}