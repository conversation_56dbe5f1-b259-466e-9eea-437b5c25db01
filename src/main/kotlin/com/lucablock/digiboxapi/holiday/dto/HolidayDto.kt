package com.lucablock.digiboxapi.holiday.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

data class HolidayDto
@QueryProjection constructor(
  val id : Long,
  val name : String,
  val description : String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
  val holidayDate : LocalDateTime,
  val holidayTypeId : Long,
  val holidayTypeName : String,
  val holidayTypeColor: String,
  val isRecurring : Boolean,
  val isActive : Boolean
)

data class MonthInfoDto
@QueryProjection constructor(
  val year : Int,
  val month : Int,
  val totalDays : Int,
  val holidaysCount : Int,
  val workingDaysCount : Int,
  val workingPerWeek : Int
)

data class CalendarHoliday
@QueryProjection constructor(
  val id : Long,
  val name : String,
  val description: String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
  val holidayDate : LocalDateTime,
  val holidayTypeId : Long,
  val holidayTypeName : String,
  val holidayTypeColor: String,
  val isRecurring : Boolean,
)

data class CalendarDataDto
@QueryProjection constructor(
//  val workingDays : List<Int>,
  val monthInfo : MonthInfoDto,
  val normalHoliday : List<NormalHolidayDto>,
  val holidays : List<CalendarHoliday>,
)

data class NormalHolidayDto
@QueryProjection constructor(
  val dayNo : Int,
  val day : String,
)