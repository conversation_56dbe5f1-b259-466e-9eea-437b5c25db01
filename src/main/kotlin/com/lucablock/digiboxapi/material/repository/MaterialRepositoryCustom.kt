package com.lucablock.digiboxapi.material.repository

import com.lucablock.digiboxapi.material.dto.MaterialsDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository

@Repository
interface MaterialRepositoryCustom {
  fun findMaterialPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?
  ): Page<MaterialsDto>
}