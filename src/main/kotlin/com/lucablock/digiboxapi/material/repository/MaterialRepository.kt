package com.lucablock.digiboxapi.material.repository

import com.lucablock.digiboxapi.entity.MaterialConfig
import com.lucablock.digiboxapi.entity.Materials
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository


@Repository
interface MaterialRepository : JpaRepository<Materials, Int> ,MaterialRepositoryCustom{
  fun findAllByNameContainingIgnoreCase(name: String, pageable: Pageable): Page<Materials>
  fun existsByNameIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  fun existsByNameIgnoreCaseAndIdNot(name: String, id: Int): <PERSON>olean

  @Query(
    value = "SELECT * FROM materials m " +
        "JOIN product_category_material pcm ON m.id = pcm.material_id " +
        "WHERE pcm.product_category_id = :categoryId",
    nativeQuery = true
  )
  fun findAllByCategoryId(categoryId: Long): List<Materials>
  fun existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String, id: Int): Boolean
  fun findAllByIsDeletedFalse(): List<Materials>
  fun findAllByNameIgnoreCase(name: String):Materials
  fun findAllByIsDeletedFalseAndIsActiveTrue(): List<Materials>
}