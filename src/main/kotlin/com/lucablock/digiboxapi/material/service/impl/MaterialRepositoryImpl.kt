package com.lucablock.digiboxapi.material.service.impl

import com.lucablock.digiboxapi.entity.QMaterials
import com.lucablock.digiboxapi.material.dto.MaterialsDto
import com.lucablock.digiboxapi.material.repository.MaterialRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service


@Service
class MaterialRepositoryImpl : MaterialRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qMaterials = QMaterials.materials

  override fun findMaterialPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<MaterialsDto> {
    var criteria = qMaterials.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qMaterials.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qMaterials.name.containsIgnoreCase(search))
    }

    val sort = if (ascending) {
      qMaterials.id.asc()
    } else {
      qMaterials.id.desc()
    }

    val query = queryFactory
      .select(qMaterials)
      .from(qMaterials)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toMaterialDto()
      }

    val total = queryFactory
      .select(qMaterials)
      .from(qMaterials)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)

  }
}
