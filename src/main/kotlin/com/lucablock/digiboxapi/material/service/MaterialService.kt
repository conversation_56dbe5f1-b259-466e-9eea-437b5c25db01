package com.lucablock.digiboxapi.material.service

import com.lucablock.digiboxapi.material.dto.MaterialsDto
import com.lucablock.digiboxapi.material.request.MaterialRequest
import com.lucablock.digiboxapi.material.request.UpdateMaterialRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile

interface MaterialService {
  fun createMaterial(materialRequest: MaterialRequest): MaterialsDto
  fun findAllMaterials(category: Long?): List<MaterialsDto>
  fun searchMaterials(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<MaterialsDto>

  fun findMaterialPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?,
  ): Page<MaterialsDto>

  fun findMaterialById(id: Int): MaterialsDto
  fun updateMaterial(id: Int, updateMaterialRequest: UpdateMaterialRequest): MaterialsDto
  fun deleteMaterial(id: Int): Boolean
  fun updateStatus(id:Int):<PERSON>olean
}