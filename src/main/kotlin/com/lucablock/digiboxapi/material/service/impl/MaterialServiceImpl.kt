package com.lucablock.digiboxapi.material.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.MaterialConfig
import com.lucablock.digiboxapi.entity.Materials
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.gram.repository.GramRepository
import com.lucablock.digiboxapi.item.repository.ItemRepository
import com.lucablock.digiboxapi.material.dto.MaterialsDto
import com.lucablock.digiboxapi.material.repository.MaterialRepository
import com.lucablock.digiboxapi.material.request.MaterialRequest
import com.lucablock.digiboxapi.material.request.UpdateMaterialRequest
import com.lucablock.digiboxapi.material.service.MaterialService
import com.lucablock.digiboxapi.materialconfig.repository.MaterialConfigRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryMaterialRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class MaterialServiceImpl @Autowired constructor(
  private val materialRepository: MaterialRepository,
  private val productCategoryRepository: ProductCategoryRepository,
  private val productCategoryMaterialRepository: ProductCategoryMaterialRepository,
  private val itemRepository: ItemRepository,
  private val gramRepository: GramRepository,
  private val s3Service: S3Service,
  private val materialConfigRepository: MaterialConfigRepository
) : MaterialService {

  @Transactional
  override fun createMaterial(materialRequest: MaterialRequest): MaterialsDto {
//    materialRepository.existsByNameIgnoreCaseAndIsDeletedFalse(materialRequest.name)
//      .let { if (it) throw BadRequestException("วัสดุนี้มีอยู่แล้ว") }

//     val gram = gramRepository.findById(materialRequest.gramId).orElseThrow{
//       throw NotFoundException("ไม่พบข้อมูลแกรม")
//     }

    val existsConfigs = materialConfigRepository
      .findAllByMaterialsNameIgnoreCaseAndGramIdInAndIsDeletedFalse(materialRequest.name, materialRequest.grams)

    if (existsConfigs.isNotEmpty()) {
      val existingGramIds = existsConfigs.map { it.gramId }
      throw BadRequestException("วัสดุมีแกรมนี้อยู่แล้ว: ${existingGramIds.joinToString(", ")}")
    }

    val savedMaterial = materialRepository.save(
      Materials(
        name = materialRequest.name,
        imageUrl = materialRequest.imageUrl,
        isActive = true
      )
    )

    val materialConfig = materialConfigRepository.saveAll(
      materialRequest.grams.map { gramId ->
      val gram = gramRepository.findById(gramId).orElseThrow{
        NotFoundException("ไม่พบข้อมูลแกรม")
      }
      MaterialConfig(
          materialsId = savedMaterial.id,
          gramId = gramId,
          materials = savedMaterial,
          grams = gram
        )
      }
    )
    val newFileUrl = s3Service.moveFile(materialRequest.imageUrl).url
    savedMaterial.imageUrl = newFileUrl
    val updateMaterial = materialRepository.save(savedMaterial)

    val materialConfigs = materialConfigRepository.findAllByMaterialsId(updateMaterial.id)
    val grams = gramRepository.findAllById(materialConfigs.map { it.gramId })

    return MaterialsDto(
      id = updateMaterial.id,
      name = updateMaterial.name,
      imageUrl = updateMaterial.imageUrl,
      isActive = updateMaterial.isActive,
      isDeleted = updateMaterial.isDeleted,
      grams = grams.map { it.toGramDto() },
      createdDate = updateMaterial.createdDate,
      updatedDate = updateMaterial.updatedDate
    )
  }

  override fun findAllMaterials(category: Long?): List<MaterialsDto> {
    if (category != null) {
      return materialRepository.findAllByCategoryId(category).map { it.toMaterialDto() }
    }
    return materialRepository.findAllByIsDeletedFalseAndIsActiveTrue().map { it.toMaterialDto() }
  }

  override fun searchMaterials(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<MaterialsDto> {
    var term = ""
    if (searchTerm != null) {
      term = searchTerm
    }
    return materialRepository.findAllByNameContainingIgnoreCase(term, pageable)
      .map { it.toMaterialDto() }
  }

  override fun findMaterialPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<MaterialsDto> {
    return materialRepository.findMaterialPage(pageable, ascending, search, isActive)
  }

  override fun findMaterialById(id: Int): MaterialsDto {
    val updatedMaterial = materialRepository.findById(id).orElseThrow {
        throw NotFoundException("ไม่พบข้อมูลวัสดุ")
      }
    val materialConfigs = materialConfigRepository.findAllByMaterialsIdAndIsDeletedFalse(updatedMaterial.id)
    val grams = gramRepository.findAllById(materialConfigs.map { it.gramId })
    return MaterialsDto(
      id = updatedMaterial.id,
      name = updatedMaterial.name,
      imageUrl = updatedMaterial.imageUrl,
      isActive = updatedMaterial.isActive,
      isDeleted = updatedMaterial.isDeleted,
      grams = grams.map { it.toGramDto() },
      createdDate = updatedMaterial.createdDate,
      updatedDate = updatedMaterial.updatedDate
    )
  }

  @Transactional
  override fun updateMaterial(id: Int, updateMaterialRequest: UpdateMaterialRequest): MaterialsDto {
    val material =
      materialRepository.findById(id).orElseThrow { NotFoundException("ไม่มีข้อมูลวัสดุ") }

    materialRepository.existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(
      updateMaterialRequest.name, id
    )
      .let { if (it) throw BadRequestException("วัสดุนี้มีอยู่แล้ว") }

    material.name = updateMaterialRequest.name
    material.isActive = updateMaterialRequest.isActive

    if (updateMaterialRequest.imageUrl != material.imageUrl) {
      s3Service.deleteFile(material.imageUrl)
      val newFileUrl = s3Service.moveFile(updateMaterialRequest.imageUrl).url
      material.imageUrl = newFileUrl
    }

    val materialConfig = materialConfigRepository.findAllByMaterialsId(material.id)
    val updatedGramIds = updateMaterialRequest.grams.toSet()
    val toDeleteConfigs = materialConfig.filterNot { it.gramId in updatedGramIds && !it.isDeleted }
    toDeleteConfigs.map {
      it.isDeleted = true
      it.isActive = false
    }
    materialConfigRepository.saveAll(toDeleteConfigs)

    val existingGramIds = materialConfig.map { it.gramId }.toSet()
    val newGramIds = updatedGramIds.minus(existingGramIds)

    val revivedConfigs = materialConfig.filter {
      it.gramId in updatedGramIds && it.isDeleted
    }.onEach {
      it.isDeleted = false
      it.isActive = true
    }

    val gramsId = newGramIds.minus(revivedConfigs.map { it.gramId }.toSet())

    val newConfigs = gramsId.map { gramId ->
      val gram = gramRepository.findById(gramId).orElseThrow{
        NotFoundException("ไม่พบข้อมูลแกรม")
      }
      MaterialConfig(
        materialsId = material.id,
        gramId = gramId,
        materials = material,
        grams = gram
      )
    }
    materialConfigRepository.saveAll(newConfigs)
    val updatedMaterial = materialRepository.save(material)

    val materialConfigs = materialConfigRepository.findAllByMaterialsIdAndIsDeletedFalse(updatedMaterial.id)
    val grams = gramRepository.findAllById(materialConfigs.map { it.gramId })
    return MaterialsDto(
      id = updatedMaterial.id,
      name = updatedMaterial.name,
      imageUrl = updatedMaterial.imageUrl,
      isActive = updatedMaterial.isActive,
      isDeleted = updatedMaterial.isDeleted,
      grams = grams.map { it.toGramDto() },
      createdDate = updatedMaterial.createdDate,
      updatedDate = updatedMaterial.updatedDate
    )
  }

  override fun deleteMaterial(id: Int): Boolean {
    val material =
      materialRepository.findById(id).orElseThrow { NotFoundException("ไม่พบข้อมูลวัสดุ") }
//    val items = itemRepository.findAllByMaterialId(id)
//    if (items.isNotEmpty()) throw NotFoundException("วัสดุนี้ถูกใช้งานอยู่ ไม่สามารถลบได้")
//
//    s3Service.deleteFile(material.imageUrl)
//    materialRepository.deleteById(id)
    material.isDeleted = true
    materialRepository.save(material)
    return true
  }

  override fun updateStatus(id: Int): Boolean {
    val material = materialRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวัสดุ")
    }
    material.isActive = !material.isActive
    materialRepository.save(material)
    return true
  }

}