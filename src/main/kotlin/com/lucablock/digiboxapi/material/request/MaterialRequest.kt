package com.lucablock.digiboxapi.material.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

class MaterialRequest {
  @NotBlank(message = "กรุณากรอกชื่อวัสดุ")
  var name: String = ""

  @NotBlank(message = "ต้องระบุ url รูปภาพ")
  var imageUrl: String = ""

  @JsonProperty("isActive")
  val isActive : Boolean = true

  @NotEmpty(message = "กรุณาเลือกขนาดแกรม")
  val grams : List<Long> = mutableListOf()

//  @NotEmpty(message = "ต้องระบุหมวดหมู่สินค้า")
//  var categories: List<Long> = mutableListOf()
}

class UpdateMaterialRequest {
  @NotBlank(message = "กรุณากรอกชื่อวัสดุ")
  var name: String = ""

  @NotBlank(message = "ต้องระบุ url รูปภาพ")
  var imageUrl: String = ""

  @JsonProperty("isActive")
  val isActive : Boolean = true

  @NotEmpty(message = "กรุณาเลือกขนาดแกรม")
  var grams : List<Long> = mutableListOf()
//  @NotEmpty(message = "ต้องระบุหมวดหมู่สินค้า")
//  var categories: List<Long> = mutableListOf()
}


