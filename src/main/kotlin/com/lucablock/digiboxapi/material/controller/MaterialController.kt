package com.lucablock.digiboxapi.material.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.material.request.MaterialRequest
import com.lucablock.digiboxapi.material.request.UpdateMaterialRequest
import com.lucablock.digiboxapi.material.service.MaterialService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("api")
class MaterialController @Autowired internal constructor(
  private val materialService: MaterialService
) {
  private val logger: Logger = LoggerFactory.getLogger(MaterialController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/material")
  fun createMaterial(
    @Valid @RequestBody materialRequest: MaterialRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลวัสดุสำเร็จ",
          materialService.createMaterial(materialRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create material error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message?: "วัสดุนี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error("create material error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลวัสดุนี้"))
    } catch (e: Exception) {
      logger.error("create material error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถสร้างข้อมูลวัสดุ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/material")
  fun findAllMaterial(
    @RequestParam("category", required = false) category: Long?,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          materialService.findAllMaterials(category)
        )
      )
    } catch (e: Exception) {
      logger.error("find all material error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/material/search")
  fun searchMaterial(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("searchTerm", required = false) searchTerm: String?,
    @RequestParam("sortField", required = false) sortField: String?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหาวัสดุสำเร็จ",
          materialService.searchMaterials(pageable, ascending, searchTerm, sortField)
        )
      )
    } catch (e: Exception) {
      logger.error("search material error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/material/{id}")
  fun findMaterialById(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          materialService.findMaterialById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find material by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลวัสดุ"))
    } catch (e: Exception) {
      logger.error("find material by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/material/{id}")
  fun updateMaterial(
    @PathVariable id: Int,
    @Valid @RequestBody updateMaterialRequest: UpdateMaterialRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลวัสดุสำเร็จ",
          materialService.updateMaterial(id, updateMaterialRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update material error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลวัสดุ"))
    } catch (e: BadRequestException) {
      logger.error("update material error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "วัสดุนี้มีอยู่แล้ว"))
    } catch (e: Exception) {
      logger.error("update material error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลได้ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @GetMapping("/web/material/page")
  fun findMaterialPage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = materialService.findMaterialPage(
            pageable,
            ascending,
            search,
            isActive,
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find material error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }


  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/material/{id}")
  fun deleteMaterial(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลวัสดุสำเร็จ",
          materialService.deleteMaterial(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete material error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลวัสดุ"))
    } catch (e: BadRequestException) {
      logger.error("delete material error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "วัสดุนี้ถูกใช้งานอยู่ ไม่สามารถลบได้"))
    } catch (e: Exception) {
      logger.error("delete material error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/material/active")
  fun updateModelActive(
    @RequestParam("id" , required = true) id: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกสถานะวัสดุสำเร็จ",
          materialService.updateStatus(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update model error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลวัสดุ"))
    } catch (e: Exception) {
      logger.error("update model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลวัสดุ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

}