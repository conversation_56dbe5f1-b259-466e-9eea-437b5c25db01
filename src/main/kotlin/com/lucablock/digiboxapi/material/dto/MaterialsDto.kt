package com.lucablock.digiboxapi.material.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.gram.dto.GramDto
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class MaterialsDto
@QueryProjection
constructor(
  val id: Int = 0,
  val name: String = "",
  val imageUrl: String = "",
  val isActive: Boolean,
  val isDeleted: Boolean,
  val grams: List<GramDto> = mutableListOf(),
//  val categories: List<ProductCategoryDto> = mutableListOf(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date()
)
