package com.lucablock.digiboxapi.config

import org.hibernate.dialect.PostgreSQLInetJdbcType
import org.springframework.aot.hint.MemberCategory
import org.springframework.aot.hint.RuntimeHints
import org.springframework.aot.hint.RuntimeHintsRegistrar
import org.springframework.aot.hint.TypeHint
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.ImportRuntimeHints

@Configuration
@ImportRuntimeHints(NativeImageRuntimeHintsConfiguration.HibernateRegistrar::class)
class NativeImageRuntimeHintsConfiguration {
  internal class HibernateRegistrar : RuntimeHintsRegistrar {
    override fun registerHints(hints: RuntimeHints, classLoader: ClassLoader?) {
      try {
        // Temporary hint, should be included into the official spring boot project
        hints.reflection().registerTypeIfPresent(
          classLoader, "org.postgresql.util.PGobject"
        ) { hint: TypeHint.Builder ->
          hint.withMembers(
            MemberCategory.INVOKE_PUBLIC_CONSTRUCTORS,
            MemberCategory.INTROSPECT_PUBLIC_METHODS
          )
            .onReachableType(PostgreSQLInetJdbcType::class.java)
        }
      } catch (e: Exception) {
        throw RuntimeException(e)
      }
    }
  }
}