package com.lucablock.digiboxapi.config

import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.servers.Server
import io.swagger.v3.oas.models.Components
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.security.SecurityRequirement
import io.swagger.v3.oas.models.security.SecurityScheme
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration


@Configuration
@OpenAPIDefinition(
    servers = [Server(url = "/", description = "Default Server URL")]
)
//@SecurityScheme(name = "bearerAuth", type = SecuritySchemeType.HTTP, bearerFormat = "JWT", scheme = "bearer")
class OpenApiConfig {
  @Bean
  fun customOpenAPI(): OpenAPI {
    return OpenAPI()
        .info(
            Info().title("Digibox API").description("Digibox API").version("1.0")
        ) // Components section defines Security Scheme "mySecretHeader"
        .components(Components().addSecuritySchemes("Authorization", securityScheme()))
        .addSecurityItem(SecurityRequirement().addList("Authorization"))
  }

  fun securityScheme(): SecurityScheme {
    return SecurityScheme().name("Authorization")
        .type(SecurityScheme.Type.HTTP)
        .`in`(SecurityScheme.In.HEADER)
        .bearerFormat("JWT")
        .scheme("bearer")

  }
}