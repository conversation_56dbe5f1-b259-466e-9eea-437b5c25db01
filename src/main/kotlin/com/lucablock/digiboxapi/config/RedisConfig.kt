package com.lucablock.digiboxapi.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.CacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.cache.RedisCacheConfiguration
import org.springframework.data.redis.cache.RedisCacheManager
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.connection.RedisStandaloneConfiguration
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer
import org.springframework.data.redis.serializer.RedisSerializationContext
import org.springframework.data.redis.serializer.StringRedisSerializer
import java.time.Duration


@Configuration
@EnableRedisRepositories
class RedisConfig {
  @Value("\${spring.jpa.data.redis.host}")
  private lateinit var redisHost: String

  @Value("\${spring.jpa.data.redis.port}")
  private val redisPort: Int = 0

  @Bean
  fun jedisConnectionFactory(): JedisConnectionFactory {
    val configuration = RedisStandaloneConfiguration(redisHost, redisPort)
    return JedisConnectionFactory(configuration)
  }

  @Bean
  fun redisTemplate(): RedisTemplate<String, Any>? {
    val template = RedisTemplate<String, Any>()
    template.connectionFactory = jedisConnectionFactory()
    return template
  }
  
  @Bean
  fun cacheManager(redisConnectionFactory: RedisConnectionFactory): CacheManager {
    val cacheConfig = RedisCacheConfiguration.defaultCacheConfig()
      .entryTtl(Duration.ofMinutes(5))
      .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(StringRedisSerializer()))
      .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(GenericJackson2JsonRedisSerializer()))
      .disableCachingNullValues()
    
    val webReviewSummaryConfig = RedisCacheConfiguration.defaultCacheConfig()
      .entryTtl(Duration.ofMinutes(5))
      .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(StringRedisSerializer()))
      .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(GenericJackson2JsonRedisSerializer()))
      .disableCachingNullValues()
    
    return RedisCacheManager.builder(redisConnectionFactory)
      .cacheDefaults(cacheConfig)
      .withCacheConfiguration("webReviewSummary", webReviewSummaryConfig)
      .build()
  }
}