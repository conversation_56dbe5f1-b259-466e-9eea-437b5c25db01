package com.lucablock.digiboxapi.config

import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.JavaMailSenderImpl
import java.util.*


@Configuration
@EnableConfigurationProperties(MailSenderProperties::class)
class MailSenderConfig(private val mailSenderProperties: MailSenderProperties) {
  @Bean
  fun javaMailSender(): JavaMailSender {
    val mailSender = JavaMailSenderImpl()
    mailSender.host = mailSenderProperties.host
    mailSender.port = mailSenderProperties.port
    mailSender.username = mailSenderProperties.username
    mailSender.password = mailSenderProperties.password
    configureJavaMailProperties(mailSender.javaMailProperties)
    return mailSender
  }

  private fun configureJavaMailProperties(properties: Properties) {
    properties["mail.smtp.auth"] = mailSenderProperties.auth
    properties["mail.smtp.starttls.enable"] = mailSenderProperties.starttlsEnable
    properties["mail.debug"] = mailSenderProperties.debug
    properties["mail.from.email"] = mailSenderProperties.username
    properties["mail.transport.protocol"] = mailSenderProperties.protocol
  }
}