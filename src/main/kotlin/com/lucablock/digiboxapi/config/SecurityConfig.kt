package com.lucablock.digiboxapi.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.lucablock.digiboxapi.oauth2.*
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.servlet.ServletException
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.DisabledException
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.oauth2.client.oidc.authentication.OidcIdTokenDecoderFactory
import org.springframework.security.oauth2.client.registration.ClientRegistration
import org.springframework.security.oauth2.jose.jws.MacAlgorithm
import org.springframework.security.oauth2.jwt.JwtDecoderFactory
import org.springframework.security.web.AuthenticationEntryPoint
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.stereotype.Component
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource
import java.io.IOException
import java.io.OutputStream

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
class SecurityConfig @Autowired internal constructor(
  private val customOAuth2UserService: CustomOAuth2UserService,
  private val customOidcUserService: CustomOidcUserService,
) {

  @Qualifier("userDetailServiceImpl")
  @Autowired
  lateinit var userDetailsService: UserDetailsService

  @Autowired
  lateinit var oAuth2AuthenticationSuccessHandler: OAuth2AuthenticationSuccessHandler

  @Autowired
  lateinit var oAuth2AuthenticationFailureHandler: OAuth2AuthenticationFailureHandler

  @Autowired
  @Qualifier("customAuthenticationEntryPoint")
  var authEntryPoint: AuthenticationEntryPoint? = null

  @Component("customAuthenticationEntryPoint")
  class CustomAuthenticationEntryPoint : AuthenticationEntryPoint {
    @Throws(IOException::class, ServletException::class)
    override fun commence(
      request: HttpServletRequest?,
      response: HttpServletResponse,
      authException: AuthenticationException?,
    ) {
      response.contentType = MediaType.APPLICATION_JSON_VALUE
      response.status = HttpServletResponse.SC_FORBIDDEN
      val responseStream: OutputStream = response.outputStream
      val mapper = ObjectMapper()
      mapper.writeValue(
        responseStream,
        HttpResponse(false, "Forbidden, You are not authorized to access this resource")
      )
      responseStream.flush()
    }
  }

  @Bean
  @Throws(Exception::class)
  fun filterChain(http: HttpSecurity): SecurityFilterChain {
    http
      .cors {
        it.configurationSource(corsConfigurationSource())
      }
      .csrf {
        it.disable()
      }
      .formLogin {
        it.disable()
      }
      .httpBasic {
        it.disable()
      }
      .exceptionHandling {
        it.authenticationEntryPoint(authEntryPoint)
      }
      .authorizeHttpRequests { requests ->
        requests
          .requestMatchers(HttpMethod.OPTIONS).permitAll()
          .requestMatchers("/actuator/**").permitAll()
          .requestMatchers("/swagger-resources/**").permitAll()
          .requestMatchers("/swagger-ui/**").permitAll()
          .requestMatchers("/swagger-ui/index.html").permitAll()
          .requestMatchers("/api-docs/**").permitAll()
          .requestMatchers("/webjars/springfox-swagger-ui/**").permitAll()
          .requestMatchers("/v2/api-docs/**").permitAll()
          .requestMatchers("/v2/profile/**").permitAll()
          .requestMatchers("/oauth2/**").permitAll()
          .requestMatchers("/api/user/login").permitAll()
          .requestMatchers("/api/user/register").permitAll()
          .requestMatchers("/api/user/verifybyemail/**").permitAll()
          .requestMatchers("/api/user/sendEmailForgotPassword/**").permitAll()
          .requestMatchers("/api/user/forgotPassword/**").permitAll()
          .requestMatchers("/api/product-category").permitAll()
          .requestMatchers("/api/product/**").permitAll()
          .requestMatchers("/api/discount-category").permitAll()
          .requestMatchers("/api/discount").permitAll()
          .requestMatchers("/api/model/populars").permitAll()
          .requestMatchers("/api/blog/slug/**").permitAll()
          .requestMatchers("/api/blog/published/**").permitAll()
          .requestMatchers("/api/web/cart/**").permitAll()
          .requestMatchers("/api/web/area-size/**").permitAll()
          .requestMatchers("/api/web/special-technic/**").permitAll()
          .requestMatchers("/api/web/unfolded-size/**").permitAll()
          .requestMatchers("/api/web/special-technic-config/**").permitAll()
          .requestMatchers("/api/web/product-category/**").permitAll()
          .requestMatchers("/api/web/product-tag/**").permitAll()
          .requestMatchers("/api/web/product-type/**").permitAll()
          .requestMatchers("/api/web/coating/**").permitAll()
          .requestMatchers("/api/web/material/**").permitAll()
          .requestMatchers("/api/web/material-config/**").permitAll()
          .requestMatchers("/api/web/gram/**").permitAll()
          .requestMatchers("/api/web/model-size/**").permitAll()
          .requestMatchers("/api/web/model-size-config/**").permitAll()
          .requestMatchers("/api/web/printing-config/**").permitAll()
          .requestMatchers("/api/web/model/**").permitAll()
          .requestMatchers("/api/web/company/**").permitAll()
          .requestMatchers("/api/company-holiday/**").permitAll()
          .requestMatchers("/api/company-working/**").permitAll()
          .requestMatchers("/api/company-working-config/**").permitAll()
          .requestMatchers("/api/web/printing/**").permitAll()
          .requestMatchers("/api/web/customize/**").permitAll()
          .requestMatchers("/api/web/product-period/**").permitAll()
          .requestMatchers("/api/web/faq/list").permitAll()
          .requestMatchers("/").permitAll()
          .anyRequest().authenticated()
      }
      .oauth2Login { oAuth2LoginConfigurer ->
        oAuth2LoginConfigurer.authorizationEndpoint {
          it.baseUri("/oauth2/authorize")
          it.authorizationRequestRepository(cookieAuthorizationRequestRepository())
        }
        oAuth2LoginConfigurer.redirectionEndpoint {
          it.baseUri("/oauth2/callback/*")
        }
        oAuth2LoginConfigurer.userInfoEndpoint {
          it.oidcUserService(customOidcUserService)
          it.userService(customOAuth2UserService)
        }
        oAuth2LoginConfigurer.successHandler(oAuth2AuthenticationSuccessHandler)
        oAuth2LoginConfigurer.failureHandler(oAuth2AuthenticationFailureHandler)
      }
      .sessionManagement {
        it.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
      }
      .headers { headersConfigurer ->
        headersConfigurer.frameOptions {
          it.disable()
        }
      }

    http.addFilterBefore(
      jwtAuthorizationFilter(),
      UsernamePasswordAuthenticationFilter::class.java
    )

    return http.build()
  }

  fun corsConfigurationSource(): CorsConfigurationSource {
    // Very permissive CORS config...
    val configuration = CorsConfiguration()
    configuration.allowedOrigins = listOf("*")
    configuration.allowedMethods = listOf("*")
    configuration.allowedHeaders = listOf("*")
    configuration.exposedHeaders = listOf("*")

    // Limited to API routes (neither actuator nor Swagger-UI)
    val source = UrlBasedCorsConfigurationSource()
    source.registerCorsConfiguration("/swagger-ui/index.html", configuration)
    source.registerCorsConfiguration("/*/**", configuration)
    source.registerCorsConfiguration("/oauth2/**", configuration)
    source.registerCorsConfiguration("/auth/**", configuration)
    source.registerCorsConfiguration("/api/redirect/**", configuration)
    source.registerCorsConfiguration("/h2/**", configuration)
    return source
  }

  @Bean
  fun customAuthenticationManager(
    userDetailsService: UserDetailsService,
    encoder: PasswordEncoder,
  ): AuthenticationManager {
    return AuthenticationManager { authentication: Authentication ->
      val username = authentication.principal.toString()
      val password = authentication.credentials.toString()
      val user = userDetailsService.loadUserByUsername(username)
      if (!encoder.matches(password, user.password)) {
        throw BadCredentialsException("ไม่สามารถเข้าสู่ระบบได้ รหัสผ่านไม่ถูกต้อง")
      }
      if (!user.isEnabled) {
        throw DisabledException("User account is not active")
      }
      UsernamePasswordAuthenticationToken(user, null, user.authorities)
    }
  }

  @Bean
  fun userDetailsService(bCryptPasswordEncoder: BCryptPasswordEncoder): UserDetailsService {
    return userDetailsService
  }

  @Bean
  fun idTokenDecoderFactory(): JwtDecoderFactory<ClientRegistration?> {
    val idTokenDecoderFactory = OidcIdTokenDecoderFactory()
    idTokenDecoderFactory.setJwsAlgorithmResolver { MacAlgorithm.HS256 }
    return idTokenDecoderFactory
  }

  @Bean
  fun bCryptPasswordEncoder(): BCryptPasswordEncoder {
    return BCryptPasswordEncoder(12)
  }

  @Bean
  fun jwtAuthorizationFilter(): JWTAuthorizationFilter {
    return JWTAuthorizationFilter()
  }

  @Bean
  fun cookieAuthorizationRequestRepository(): HttpCookieOAuth2AuthorizationRequestRepository {
    return HttpCookieOAuth2AuthorizationRequestRepository()
  }
}