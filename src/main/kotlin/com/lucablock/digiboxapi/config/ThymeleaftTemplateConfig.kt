package com.lucablock.digiboxapi.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.EnableWebMvc
import org.thymeleaf.spring6.SpringTemplateEngine
import org.thymeleaf.templatemode.TemplateMode
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver
import java.nio.charset.StandardCharsets

@Configuration
@EnableWebMvc
class ThymeleafTemplateConfig {
  @Bean
  fun springTemplateEngine(): SpringTemplateEngine {
    val springTemplateEngine = SpringTemplateEngine()
    springTemplateEngine.addTemplateResolver(emailTemplateResolver())
    return springTemplateEngine
  }

  fun emailTemplateResolver(): ClassLoaderTemplateResolver {
    val emailTemplateResolver = ClassLoaderTemplateResolver()
    emailTemplateResolver.prefix = "/templates/"
    emailTemplateResolver.suffix = ".html"
    emailTemplateResolver.templateMode = TemplateMode.HTML
    emailTemplateResolver.characterEncoding = StandardCharsets.UTF_8.name()
    emailTemplateResolver.isCacheable = false
    return emailTemplateResolver
  }
}