package com.lucablock.digiboxapi.config

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.lucablock.digiboxapi.slipok.dto.ErrorResponseDto
import feign.FeignException
import feign.codec.ErrorDecoder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class SlipOkClientConfig {
  @Bean
  fun errorDecoder(): ErrorDecoder {
    return CustomErrorDecoder()
  }
}

class CustomErrorDecoder : ErrorDecoder {
  override fun decode(methodKey: String, response: feign.Response): Exception {
    val body = response.body()?.asInputStream()?.bufferedReader()?.use { it.readText() }
    return try {
      val error = jacksonObjectMapper().readValue(body, ErrorResponseDto::class.java)
      FeignCustomException(error.code, error.message)
    } catch (ex: Exception) {
      FeignException.errorStatus(methodKey, response)
    }
  }
}

class FeignCustomException(
  val errorCode: String,
  override val message: String
) : RuntimeException(message)