package com.lucablock.digiboxapi.blog.extensions

import com.lucablock.digiboxapi.blog.dto.BlogByIdDto
import com.lucablock.digiboxapi.blog.dto.BlogBySlugDto
import com.lucablock.digiboxapi.blog.response.BlogResponse
import com.lucablock.digiboxapi.blog.response.BlogTypeResponse
import com.lucablock.digiboxapi.blog.response.BlogTypeResponseDto
import com.lucablock.digiboxapi.blog.response.CategoriesResponse
import com.lucablock.digiboxapi.entity.Blog
import com.lucablock.digiboxapi.entity.BlogCategory
import com.lucablock.digiboxapi.entity.BlogType

/**
 * Extension functions for Blog entity mapping to DTOs and Response objects
 */

fun Blog.toBlogResponse(): BlogResponse {
  return BlogResponse(
    id = this.id,
    title = this.title,
    content = this.content,
    keyword = this.keyword,
    thumbnailUrl = this.thumbnailUrl,
    isPublished = this.isPublished ?: false,
    isDeleted = this.isDeleted ?: false,
    description = this.description,
    urlSlug = this.urlSlug,
    createdDate = this.createdDate,
    updatedDate = this.updatedDate,
    author = this.author,
    categories = this.categories?.map { it.toCategoriesResponse() } ?: emptyList(),
    blogType = this.blogType?.toBlogTypeResponseDto() ?: BlogTypeResponseDto(0, "Unknown")
  )
}

fun Blog.toBlogByIdDto(): BlogByIdDto {
  return BlogByIdDto(
    id = this.id,
    title = this.title,
    content = this.content,
    thumbnailUrl = this.thumbnailUrl,
    keyword = this.keyword,
    isPublished = this.isPublished ?: false,
    urlSlug = this.urlSlug,
    description = this.description,
    isDeleted = this.isDeleted ?: false,
    blogTypeId = this.blogTypeId,
    author = this.author,
    categories = this.categories?.map { it.toCategoriesResponse() } ?: emptyList(),
    blogType = this.blogType?.toBlogTypeResponseDto() ?: BlogTypeResponseDto(0, "Unknown"),
    createdDate = this.createdDate,
    updatedDate = this.updatedDate
  )
}

fun Blog.toBlogBySlugDto(): BlogBySlugDto {
  return BlogBySlugDto(
    id = this.id,
    title = this.title,
    content = this.content,
    thumbnailUrl = this.thumbnailUrl,
    keyword = this.keyword,
    isPublished = this.isPublished ?: false,
    urlSlug = this.urlSlug,
    description = this.description,
    isDeleted = this.isDeleted ?: false,
    blogTypeUrlSlug = this.blogType?.urlSlug ?: "",
    author = this.author,
    categories = this.categories?.map { it.toCategoriesResponse() } ?: emptyList(),
    blogType = this.blogType?.toBlogTypeResponseDto() ?: BlogTypeResponseDto(0, "Unknown"),
    createdDate = this.createdDate,
    updatedDate = this.updatedDate
  )
}

fun BlogCategory.toCategoriesResponse(): CategoriesResponse {
  return CategoriesResponse(
    id = this.id,
    name = this.name,
    urlSlug = this.urlSlug
  )
}

fun BlogType.toBlogTypeResponseDto(): BlogTypeResponseDto {
  return BlogTypeResponseDto(
    id = this.id,
    name = this.name
  )
}

fun BlogType.toBlogTypeById(): BlogTypeResponse {
  return BlogTypeResponse(
    id = this.id,
    name = this.name,
    urlSlug = this.urlSlug
  )
}
