package com.lucablock.digiboxapi.blog.response

import java.io.Serializable
import java.util.*

data class BlogResponse(
  val id: Int = 0,
  var title: String,
  var content: String,
  var keyword: String,
  var thumbnailUrl: String,
  var isPublished: Boolean,
  var isDeleted: Boolean? = false,
  var description: String,
  var urlSlug: String,
  val createdDate: Date = Date(),
  val updatedDate: Date = Date(),
  val author: String,
  val categories: List<CategoriesResponse>,
  val blogType: BlogTypeResponseDto
) : Serializable

data class BlogTypeResponseDto(
  val id: Int,
  val name: String,
) : Serializable
