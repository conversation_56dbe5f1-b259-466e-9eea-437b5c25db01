package com.lucablock.digiboxapi.blog.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.blog.dto.BlogByIdDto
import com.lucablock.digiboxapi.blog.dto.BlogBySlugDto
import com.lucablock.digiboxapi.blog.extensions.toBlogTypeById
import com.lucablock.digiboxapi.blog.extensions.toCategoriesResponse
import com.lucablock.digiboxapi.blog.repository.*
import com.lucablock.digiboxapi.blog.request.BlogRequest
import com.lucablock.digiboxapi.blog.request.BlogTypeRequest
import com.lucablock.digiboxapi.blog.request.CategoriesRequest
import com.lucablock.digiboxapi.blog.response.BlogGalleryResponse
import com.lucablock.digiboxapi.blog.response.BlogResponse
import com.lucablock.digiboxapi.blog.response.BlogTypeResponse
import com.lucablock.digiboxapi.blog.response.CategoriesResponse
import com.lucablock.digiboxapi.blog.service.BlogService
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class BlogServiceImpl @Autowired constructor(
  private val blogRepository: BlogRepository,
  private val categoriesMappingRepository: BlogCategoryMappingRepository,
  private val categoriesRepository: BlogCategoryRepository,
  private val galleryRepository: GalleryRepository,
  private val s3Service: S3Service,
  private val blogTypeRepository: BlogTypeRepository,
  private val blogCategoryRepository: BlogCategoryRepository,
) : BlogService {

  //  Blog Type Methods
  override fun createBlogType(createBlogType: BlogTypeRequest) {
    validateBlogTypeRequest(createBlogType)

    blogTypeRepository.save(
      BlogType(
        name = createBlogType.name,
        urlSlug = createBlogType.urlSlug
      )
    )
  }

  override fun updateBlogType(id: Int, updateBlogType: BlogTypeRequest) {
    validatePositiveId(id, "ประเภทบทความ")

    val blogType = blogTypeRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่มีข้อมูลประเภทบทความนี้") }

    validateBlogTypeRequest(updateBlogType, excludeId = id)

    blogType.name = updateBlogType.name
    blogType.urlSlug = updateBlogType.urlSlug
    blogTypeRepository.save(blogType)
  }

  override fun getBlogTypes(): List<BlogTypeResponse> {
    return blogRepository.findAllBlogType()
  }

  override fun getBlogTypeById(id: Int): BlogTypeResponse {
    validatePositiveId(id, "ประเภทบทความ")

    val blogType = blogTypeRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่มีข้อมูลประเภทบทความนี้") }

    if (blogType.isDeleted == true) {
      throw NotFoundException("ไม่พบประเภทบทความที่ต้องการ")
    }

    return blogType.toBlogTypeById()
  }

  override fun deleteBlogType(id: Int) {
    validatePositiveId(id, "ประเภทบทความ")

    val blogType = blogTypeRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบประเภทบทความ") }
    if (blogType.isDeleted == true) {
      throw NotFoundException("ไม่พบประเภทบทความ")
    }

    blogType.isDeleted = true
    blogTypeRepository.save(blogType)
  }

  //  Blog Category Methods
  override fun createCategory(createCategory: CategoriesRequest) {
    validateCategoryRequest(createCategory)

    categoriesRepository.save(
      BlogCategory(
        name = createCategory.name,
        urlSlug = createCategory.urlSlug
      )
    )
  }

  override fun updateCategory(id: Int, createCategory: CategoriesRequest) {
    validatePositiveId(id, "หมวดหมู่บทความ")

    val category = categoriesRepository.findByIdAndIsDelete(id)
      .orElseThrow { NotFoundException("ไม่พบหมวดหมู่บทความ") }

    validateCategoryRequest(createCategory, excludeId = id)

    category.name = createCategory.name
    category.urlSlug = createCategory.urlSlug
    categoriesRepository.save(category)
  }

  override fun getCategoryById(id: Int): CategoriesResponse {
    validatePositiveId(id, "หมวดหมู่บทความ")

    val blogCategory = categoriesRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบข้อมูลหมวดหมู่บทความ") }

    if (blogCategory.isDeleted == true) {
      throw NotFoundException("ไม่พบข้อมูลหมวดหมู่บทความ")
    }

    return blogCategory.toCategoriesResponse()
  }

  override fun getBlogCategories(): List<CategoriesResponse> {
    return blogRepository.findAllCategories()
  }

  override fun deleteCategory(id: Int) {
    validatePositiveId(id, "หมวดหมู่บทความ")

    val category = blogCategoryRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบหมวดหมู่บทความ")
    }

    if (category.isDeleted == true) {
      throw NotFoundException("ไม่พบหมวดหมู่บทความ")
    }

    category.isDeleted = true
    blogCategoryRepository.save(category)
  }

  // Blog Gallery Methods
  override fun createGallery(file: MultipartFile): Gallery {
    validateFileUpload(file)

    val url = s3Service.uploadFile(file)

    return galleryRepository.save(
      Gallery(
        url = url.url
      )
    )
  }

  override fun getGalleries(pageable: Pageable): Page<BlogGalleryResponse> {
    return blogRepository.getGalleries(pageable)
  }

  override fun deleteGallery(id: Int) {
    validatePositiveId(id, "แกลเลอรี่")

    val gallery = galleryRepository.findById(id).orElseThrow {
      NotFoundException("ไม่พบข้อมูลแกลเลอรี่")
    }

    gallery.isDeleted = true
    galleryRepository.save(gallery)
  }


  //  Blog Methods
  @Transactional
  override fun createBlog(blogRequest: BlogRequest) {
    validateBlogRequest(blogRequest)
    val blog = createBlogFromRequest(blogRequest)
    blogRepository.save(blog)
  }

  @Transactional
  override fun updateBlog(id: Int, blogRequest: BlogRequest) {
    validatePositiveId(id, "บทความ")

    val existingBlog = blogRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบบทความ") }

    if (existingBlog.isDeleted == true) {
      throw NotFoundException("ไม่พบบทความ")
    }

    validateBlogRequest(blogRequest, excludeId = id)

    // Update blog properties
    existingBlog.title = blogRequest.title
    existingBlog.content = blogRequest.content
    existingBlog.blogTypeId = blogRequest.blogTypeId
    existingBlog.keyword = blogRequest.keyword
    existingBlog.isPublished = blogRequest.isPublished
    existingBlog.thumbnailUrl = blogRequest.thumbNailUrl
    existingBlog.urlSlug = blogRequest.urlSlug
    existingBlog.description = blogRequest.description
    existingBlog.author = blogRequest.author

    blogRepository.save(existingBlog)

    // Update categories using helper method
    updateBlogCategories(existingBlog.id, blogRequest.categories)
  }

  override fun getBlogs(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String
  ): Page<BlogResponse> {
    return blogRepository.findAllBlog(
      pageable, type, category, ascending, sortField
    )
  }

  override fun getBlogAdmin(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String
  ): Page<BlogResponse> {
    return blogRepository.getBlogAdmin(pageable, type, category, ascending, sortField)
  }

  override fun getBlogByAdminId(id: Int): BlogByIdDto {
    validatePositiveId(id, "บทความ")

    val blog = blogRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบบทความ") }

    if (blog.isDeleted == true) {
      throw NotFoundException("ไม่พบบทความ")
    }

    return blogRepository.getBlogAdminById(id)
  }

  override fun getBlogBySlug(urlSlug: String): BlogBySlugDto {
    if (urlSlug.isBlank()) {
      throw BadRequestException("ไม่สามารถแสดงข้อมูลได้ URL Slug ไม่ถูกต้อง")
    }

    blogRepository.findBlogBySlug(urlSlug)
      .orElseThrow { NotFoundException("ไม่พบบทความ") }

    return blogRepository.getBlogBySlug(urlSlug)
  }

  override fun deleteBlog(id: Int) {
    validatePositiveId(id, "บทความ")

    val blog = blogRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบบทความ") }

    if (blog.isDeleted == true) {
      throw NotFoundException("ไม่พบบทความ")
    }

    blog.isDeleted = true
    blogRepository.save(blog)
  }


  /**
   * Validation helper methods
   */
  private fun validateBlogRequest(request: BlogRequest, excludeId: Int? = null) {
    // Check for duplicate title
    val duplicateByTitle = if (excludeId != null) {
      blogRepository.findBlogDuplicatedByTitleAndId(request.title, excludeId)
    } else {
      blogRepository.findBlogDuplicatedByTitle(request.title)
    }

    if (duplicateByTitle.isPresent) {
      throw BadRequestException("ไม่สามารถสร้างบทความได้ ชื่อบทความซ้ำ")
    }

    // Check for duplicate slug
    val duplicateBySlug = if (excludeId != null) {
      blogRepository.findBlogDuplicatedBySlugAndId(request.urlSlug, excludeId)
    } else {
      blogRepository.findBlogDuplicatedBySlug(request.urlSlug)
    }

    if (duplicateBySlug.isPresent) {
      throw BadRequestException("ไม่สามารถสร้างบทความได้ URL Slug ซ้ำ")
    }

    // Validate blog type exists
    val blogType = blogTypeRepository.findById(request.blogTypeId)
      .orElseThrow { NotFoundException("ไม่พบประเภทบทความ") }

    if (blogType.isDeleted == true) {
      throw BadRequestException("ไม่สามารถสร้างบทความได้ ประเภทบทความถูกลบแล้ว")
    }

    // Validate categories exist
    request.categories.forEach { categoryId ->
      val category = blogCategoryRepository.findById(categoryId)
        .orElseThrow { NotFoundException("ไม่พบหมวดหมู่บทความ") }

      if (category.isDeleted == true) {
        throw BadRequestException("ไม่สามารถสร้างบทความได้ หมวดหมู่ถูกลบแล้ว")
      }
    }
  }

  private fun createBlogFromRequest(request: BlogRequest): Blog {
    val categories = request.categories.mapNotNull { categoryId ->
      blogCategoryRepository.findById(categoryId).orElse(null)
    }.toSet()

    return Blog(
      title = request.title,
      content = request.content,
      keyword = request.keyword,
      thumbnailUrl = request.thumbNailUrl,
      isPublished = request.isPublished,
      blogTypeId = request.blogTypeId,
      description = request.description,
      urlSlug = request.urlSlug,
      author = request.author,
      categories = categories
    )
  }

  private fun validateCategoryRequest(request: CategoriesRequest, excludeId: Int? = null) {
    val duplicateByName = if (excludeId != null) {
      categoriesRepository.findByCategoryByNameAndId(request.name, excludeId)
    } else {
      categoriesRepository.findByCategoryByName(request.name)
    }

    if (duplicateByName.isPresent) {
      throw BadRequestException("ไม่สามารถบันทึกหมวดหมู่บทความได้ ชื่อหมวดหมู่ซ้ำ")
    }

    val duplicateBySlug = if (excludeId != null) {
      categoriesRepository.findByCategoryByUrlSlugAndId(request.urlSlug, excludeId)
    } else {
      categoriesRepository.findByCategoryByUrlSlug(request.urlSlug)
    }

    if (duplicateBySlug.isPresent) {
      throw BadRequestException("ไม่สามารถบันทึกหมวดหมู่บทความได้ URL Slug ซ้ำ")
    }
  }

  private fun validateBlogTypeRequest(request: BlogTypeRequest, excludeId: Int? = null) {
    val duplicateByName = if (excludeId != null) {
      blogTypeRepository.findBlogTypeByNameAndId(request.name, excludeId)
    } else {
      blogTypeRepository.findBlogTypeByName(request.name)
    }

    if (duplicateByName.isPresent) {
      throw BadRequestException("ไม่สามารถบันทึกประเภทบทความได้ ชื่อประเภทซ้ำ")
    }

    val duplicateBySlug = if (excludeId != null) {
      blogTypeRepository.findBlogTypeByUrlSlugAndId(request.urlSlug, excludeId)
    } else {
      blogTypeRepository.findBlogTypeByUrlSlug(request.urlSlug)
    }

    if (duplicateBySlug.isPresent) {
      throw BadRequestException("ไม่สามารถบันทึกประเภทบทความได้ URL Slug ซ้ำ")
    }
  }

  /**
   * Helper method to update blog categories
   */
  private fun updateBlogCategories(blogId: Int, categoryIds: List<Int>) {
    // Remove existing mappings
    categoriesMappingRepository.deleteAllByBlogId(blogId)

    // Create new mappings in batch
    if (categoryIds.isNotEmpty()) {
      val categoryMappings = categoryIds.map { categoryId ->
        CategoriesMapping(
          blogId = blogId,
          categoriesId = categoryId
        )
      }
      categoriesMappingRepository.saveAll(categoryMappings)
    }
  }

  /**
   * Helper method to validate file upload
   */
  private fun validateFileUpload(file: MultipartFile) {
    if (file.isEmpty) {
      throw BadRequestException("ไม่สามารถอัพโหลดไฟล์ได้ กรุณาเลือกไฟล์")
    }

    // Additional file validation can be added here
    // e.g., file size, file type, etc.
  }

  /**
   * Helper method to validate positive ID
   */
  private fun validatePositiveId(id: Int, entityName: String) {
    if (id <= 0) {
      throw BadRequestException("ไม่สามารถแสดงข้อมูลได้ รหัส$entityName ไม่ถูกต้อง")
    }
  }
}