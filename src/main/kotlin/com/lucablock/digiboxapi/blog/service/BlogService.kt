package com.lucablock.digiboxapi.blog.service

import com.lucablock.digiboxapi.blog.dto.BlogByIdDto
import com.lucablock.digiboxapi.blog.dto.BlogBySlugDto
import com.lucablock.digiboxapi.blog.request.BlogRequest
import com.lucablock.digiboxapi.blog.request.BlogTypeRequest
import com.lucablock.digiboxapi.blog.request.CategoriesRequest
import com.lucablock.digiboxapi.blog.response.BlogGalleryResponse
import com.lucablock.digiboxapi.blog.response.BlogResponse
import com.lucablock.digiboxapi.blog.response.BlogTypeResponse
import com.lucablock.digiboxapi.blog.response.CategoriesResponse
import com.lucablock.digiboxapi.entity.Gallery
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile

interface BlogService {
  fun createBlog(blogRequest: BlogRequest)
  fun updateBlog(id: Int, blogRequest: BlogRequest)
  fun getBlogs(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String,
  ): Page<BlogResponse>

  fun deleteBlog(id: Int)
  fun getBlogAdmin(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String,
  ): Page<BlogResponse>

  fun getBlogByAdminId(id: Int): BlogByIdDto
  fun getBlogBySlug(urlSlug: String): BlogBySlugDto
  fun createCategory(createCategory: CategoriesRequest)
  fun updateCategory(id: Int, createCategory: CategoriesRequest)
  fun getCategoryById(id: Int): CategoriesResponse
  fun getBlogCategories(): List<CategoriesResponse>
  fun createGallery(file: MultipartFile): Gallery
  fun getGalleries(pageable: Pageable): Page<BlogGalleryResponse>
  fun deleteGallery(id: Int)
  fun createBlogType(createBlogType: BlogTypeRequest)
  fun updateBlogType(id: Int, updateBlogType: BlogTypeRequest)
  fun getBlogTypes(): List<BlogTypeResponse>
  fun getBlogTypeById(id: Int): BlogTypeResponse
  fun deleteBlogType(id: Int)
  fun deleteCategory(id: Int)
}