package com.lucablock.digiboxapi.blog.service.impl

import com.lucablock.digiboxapi.blog.dto.BlogByIdDto
import com.lucablock.digiboxapi.blog.dto.BlogBySlugDto
import com.lucablock.digiboxapi.blog.extensions.toBlogByIdDto
import com.lucablock.digiboxapi.blog.extensions.toBlogBySlugDto
import com.lucablock.digiboxapi.blog.extensions.toBlogResponse
import com.lucablock.digiboxapi.blog.extensions.toCategoriesResponse
import com.lucablock.digiboxapi.blog.repository.BlogRepositoryCustom
import com.lucablock.digiboxapi.blog.response.BlogGalleryResponse
import com.lucablock.digiboxapi.blog.response.BlogResponse
import com.lucablock.digiboxapi.blog.response.BlogTypeResponse
import com.lucablock.digiboxapi.blog.response.CategoriesResponse
import com.lucablock.digiboxapi.entity.QBlog
import com.lucablock.digiboxapi.entity.QBlogCategory
import com.lucablock.digiboxapi.entity.QBlogType
import com.lucablock.digiboxapi.entity.QGallery
import com.lucablock.digiboxapi.exception.NotFoundException
import com.querydsl.core.types.OrderSpecifier
import com.querydsl.core.types.dsl.BooleanExpression
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class BlogRepositoryImpl : BlogRepositoryCustom {

  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private var qBlog = QBlog.blog
  private var qBlogCategories = QBlogCategory.blogCategory
  private var qBlogType = QBlogType.blogType
  private var qGallery = QGallery.gallery

  override fun findAllBlog(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String,
  ): Page<BlogResponse> {
    val criteria = buildBlogCriteria(
      type = type,
      category = category,
      includeUnpublished = true,
      includeDeleted = false
    )

    return executeBlogQuery(pageable, criteria, sortField, ascending)
  }

  override fun getBlogAdmin(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String,
  ): Page<BlogResponse> {
    val criteria = buildBlogCriteria(
      type = type,
      category = category,
      includeDeleted = false
    )

    return executeBlogQuery(pageable, criteria, sortField, ascending)
  }

  override fun getBlogAdminById(id: Int): BlogByIdDto {
    require(id > 0) { "ไม่สามารถแสดงข้อมูลได้ รหัสบทความไม่ถูกต้อง" }

    val blog = queryFactory
      .selectFrom(qBlog)
      .where(qBlog.id.eq(id))
      .fetchOne()
      ?: throw NotFoundException("ไม่พบบทความ")

    return blog.toBlogByIdDto()
  }

  override fun getBlogBySlug(slug: String): BlogBySlugDto {
    require(slug.isNotBlank()) { "ไม่สามารถแสดงข้อมูลได้ URL Slug ไม่ถูกต้อง" }

    val blog = queryFactory
      .selectFrom(qBlog)
      .where(
        qBlog.urlSlug.toLowerCase().eq(slug.lowercase())
          .and(qBlog.isDeleted.isFalse)
          .and(qBlog.isPublished.isTrue)
      )
      .fetchOne()
      ?: throw NotFoundException("ไม่พบบทความ")

    return blog.toBlogBySlugDto()
  }

  override fun getBlogTypeById(id: Int): BlogTypeResponse {
//    require(id > 0) { "ไม่สามารถแสดงข้อมูลได้ รหัสประเภทบทความไม่ถูกต้อง" }

    val blogType = queryFactory
      .selectFrom(qBlogType)
      .where(qBlogType.id.eq(id).and(qBlogType.isDeleted.isFalse))
      .fetchOne()
      ?: throw NotFoundException("ไม่พบประเภทบทความ")

    return BlogTypeResponse(
      blogType.id,
      blogType.name,
      blogType.urlSlug
    )
  }

  override fun getCategoryById(id: Int): CategoriesResponse {
    require(id > 0) { "ไม่สามารถแสดงข้อมูลได้ รหัสหมวดหมู่บทความไม่ถูกต้อง" }

    val category = queryFactory
      .selectFrom(qBlogCategories)
      .where(qBlogCategories.id.eq(id).and(qBlogCategories.isDeleted.isFalse))
      .fetchOne()
      ?: throw NotFoundException("ไม่พบหมวดหมู่บทความ")

    return category.toCategoriesResponse()
  }

  override fun findAllBlogType(): List<BlogTypeResponse> {
    return queryFactory
      .selectFrom(qBlogType)
      .where(qBlogType.isDeleted.isFalse)
      .orderBy(qBlogType.name.asc())
      .fetch()
      .map { blogType ->
        BlogTypeResponse(
          blogType.id,
          blogType.name,
          blogType.urlSlug
        )
      }
  }

  override fun findAllCategories(): List<CategoriesResponse> {
    return queryFactory
      .selectFrom(qBlogCategories)
      .where(qBlogCategories.isDeleted.isFalse)
      .orderBy(qBlogCategories.name.asc())
      .fetch()
      .map { it.toCategoriesResponse() }
  }

  override fun findAllGallery(): List<BlogGalleryResponse> {
    return queryFactory
      .selectFrom(qGallery)
      .where(qGallery.isDeleted.isFalse)
      .orderBy(qGallery.id.desc())
      .fetch()
      .map { gallery ->
        BlogGalleryResponse(
          gallery.id,
          gallery.url
        )
      }
  }

  override fun findGalleryById(id: Int): BlogGalleryResponse {
    require(id > 0) { "ไม่สามารถแสดงข้อมูลได้ รหัสแกลเลอรี่ไม่ถูกต้อง" }

    val gallery = queryFactory
      .selectFrom(qGallery)
      .where(qGallery.id.eq(id).and(qGallery.isDeleted.isFalse))
      .fetchOne()
      ?: throw NotFoundException("ไม่พบแกลเลอรี่")

    return BlogGalleryResponse(
      gallery.id,
      gallery.url
    )
  }

  override fun getGalleries(pageable: Pageable): Page<BlogGalleryResponse> {
    val queryTotal = queryFactory
      .selectFrom(qGallery)
      .where(qGallery.isDeleted.isFalse)
      .orderBy(qGallery.id.desc())
    val total = queryTotal.fetch().size.toLong()


    val queryContent = queryFactory
      .selectFrom(qGallery)
      .where(qGallery.isDeleted.isFalse)
      .orderBy(qGallery.id.desc())
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())

    val results = queryContent.fetch().map {
      BlogGalleryResponse(
        it.id,
        it.url
      )
    }

    return PageImpl(results, pageable, total)
  }

  /**
   * Build criteria for blog queries with common filters
   */
  private fun buildBlogCriteria(
    type: Int? = null,
    category: List<Int>? = null,
    includeUnpublished: Boolean? = null,
    includeDeleted: Boolean = false
  ): BooleanExpression {
    var criteria = qBlog.id.isNotNull

    if (!includeDeleted) {
      criteria = criteria.and(qBlog.isDeleted.isFalse)
    }

    includeUnpublished?.let {
      if (!it) {
        criteria = criteria.and(qBlog.isPublished.isTrue)
      }
    }

    type?.let { criteria = criteria.and(qBlog.blogTypeId.eq(it)) }

    if (!category.isNullOrEmpty()) {
      criteria = criteria.and(qBlog.categories.any().id.`in`(category))
    }

    return criteria
  }

  /**
   * Execute paginated blog query with common logic
   */
  private fun executeBlogQuery(
    pageable: Pageable,
    criteria: BooleanExpression,
    sortField: String,
    ascending: Boolean
  ): Page<BlogResponse> {
    val queryTotal = queryFactory
      .selectFrom(qBlog)
      .where(criteria)
    val total = queryTotal.fetch().size.toLong()


    val query = queryFactory
      .selectFrom(qBlog)
      .leftJoin(qBlogType).on(qBlogType.id.eq(qBlog.blogTypeId))
      .where(criteria)
      .orderBy(getSortedColumn(sortField, ascending))
      .distinct()
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())

    val results = query.fetch().map { it.toBlogResponse() }

    return PageImpl(results, pageable, total)
  }

  private fun getSortedColumn(sortField: String, ascending: Boolean): OrderSpecifier<*> {
    val column = when (sortField) {
      "title" -> qBlog.title
      "createdDate" -> qBlog.createdDate
      "updatedDate" -> qBlog.updatedDate
      else -> qBlog.id
    }
    return if (ascending) column.asc() else column.desc()
  }
}