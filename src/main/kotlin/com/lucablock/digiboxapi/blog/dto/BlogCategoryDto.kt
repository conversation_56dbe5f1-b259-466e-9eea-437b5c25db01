package com.lucablock.digiboxapi.blog.dto

import com.lucablock.digiboxapi.blog.response.BlogTypeResponseDto
import com.lucablock.digiboxapi.blog.response.CategoriesResponse
import java.io.Serializable
import java.util.*

data class BlogBySlugDto(
  val id: Int = 0,
  var title: String,
  var content: String,
  var thumbnailUrl: String,
  var keyword: String,
  var isPublished: Boolean,
  var urlSlug: String,
  var description: String,
  var isDeleted: Boolean,
  var blogTypeUrlSlug: String,
  var author: String,
  var categories: List<CategoriesResponse>,
  var blogType: BlogTypeResponseDto,
  var createdDate: Date,
  var updatedDate: Date
) : Serializable

data class BlogByIdDto(
  val id: Int = 0,
  var title: String,
  var content: String,
  var thumbnailUrl: String,
  var keyword: String,
  var isPublished: Boolean,
  var urlSlug: String,
  var description: String,
  var isDeleted: Boolean,
  var blogTypeId: Int,
  var author: String,
  var categories: List<CategoriesResponse>,
  var blogType: BlogTypeResponseDto,
  var createdDate: Date,
  var updatedDate: Date
) : Serializable
