package com.lucablock.digiboxapi.blog.repository

import com.lucablock.digiboxapi.entity.Blog
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface BlogRepository : JpaRepository<Blog, Int>, BlogRepositoryCustom {

  @Query("select * from blog where blog_type_id = :id limit 1", nativeQuery = true)
  fun findByTypeId(id: Int): List<Blog>

  @Query(
    "select * from blog where lower(url_slug) = lower(:urlSlug) and is_deleted = false and is_published = true",
    nativeQuery = true
  )
  fun findBlogBySlug(urlSlug: String): Optional<Blog>

  @Query(
    "select * from blog where lower(title) = lower(:title) and is_deleted = false",
    nativeQuery = true
  )
  fun findBlogDuplicatedByTitle(title: String): Optional<Blog>

  @Query(
    "select * from blog where lower(title) = lower(:title) and id != :id and is_deleted = false",
    nativeQuery = true
  )
  fun findBlogDuplicatedByTitleAndId(title: String, id: Int): Optional<Blog>

  @Query(
    "select * from blog where lower(url_slug) = lower(:urlSlug) and is_deleted = false",
    nativeQuery = true
  )
  fun findBlogDuplicatedBySlug(urlSlug: String): Optional<Blog>

  @Query(
    "select * from blog where lower(url_slug) = lower(:urlSlug) and id != :id and is_deleted = false",
    nativeQuery = true
  )
  fun findBlogDuplicatedBySlugAndId(urlSlug: String, id: Int): Optional<Blog>
}
