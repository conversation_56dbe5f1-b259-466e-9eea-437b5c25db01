package com.lucablock.digiboxapi.blog.repository

import com.lucablock.digiboxapi.entity.BlogType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface BlogTypeRepository : JpaRepository<BlogType, Int> {

  @Query(
    "select * from blog_type where lower(name) = lower(:name) and is_deleted = false",
    nativeQuery = true
  )
  fun findBlogTypeByName(name: String): Optional<BlogType>

  @Query(
    "select * from blog_type where lower(url_slug) = lower(:urlSlug) and is_deleted = false",
    nativeQuery = true
  )
  fun findBlogTypeByUrlSlug(urlSlug: String): Optional<BlogType>

  @Query("select * from blog_type where id = :id and is_deleted = false", nativeQuery = true)
  fun findByIdAndIsDelete(id: Int): Optional<BlogType>

  @Query(
    "select * from blog_type where lower(name) = lower(:name) and is_deleted = false and id != :id",
    nativeQuery = true
  )
  fun findBlogTypeByNameAndId(name: String, id: Int): Optional<BlogType>

  @Query(
    "select * from blog_type where lower(url_slug) = lower(:urlSlug) and is_deleted = false and id != :id",
    nativeQuery = true
  )
  fun findBlogTypeByUrlSlugAndId(urlSlug: String, id: Int): Optional<BlogType>
}