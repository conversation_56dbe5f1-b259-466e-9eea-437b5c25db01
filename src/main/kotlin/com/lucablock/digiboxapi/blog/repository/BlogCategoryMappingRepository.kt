package com.lucablock.digiboxapi.blog.repository

import com.lucablock.digiboxapi.entity.CategoriesMapping
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query

interface BlogCategoryMappingRepository : JpaRepository<CategoriesMapping, Int> {

  @Transactional
  @Modifying
  @Query("DELETE from blog_category_mapping where blog_id = :blogId", nativeQuery = true)
  fun deleteAllByBlogId(blogId: Int)

  @Query(
    "select count(blog_category_id) > 0 from blog_category_mapping where blog_category_id = :categoryId",
    nativeQuery = true
  )
  fun existsByCategoryId(categoryId: Int): <PERSON><PERSON><PERSON>
}