package com.lucablock.digiboxapi.blog.repository

import com.lucablock.digiboxapi.entity.BlogCategory
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface BlogCategoryRepository : JpaRepository<BlogCategory, Int> {

  @Query(
    "select * from blog_category where lower(name) = lower(:categoryName) and is_deleted = false",
    nativeQuery = true
  )
  fun findByCategoryByName(categoryName: String): Optional<BlogCategory>

  @Query(
    "select * from blog_category where lower(url_slug) = lower(:urlSlug) and is_deleted = false",
    nativeQuery = true
  )
  fun findByCategoryByUrlSlug(urlSlug: String): Optional<BlogCategory>

  @Query(
    "select * from blog_category where lower(name) = lower(:categoryName) and is_deleted = false and id != :id",
    nativeQuery = true
  )
  fun findByCategoryByNameAndId(categoryName: String, id: Int): Optional<BlogCategory>

  @Query(
    "select * from blog_category where lower(url_slug) = lower(:urlSlug) and is_deleted = false and id != :id",
    nativeQuery = true
  )
  fun findByCategoryByUrlSlugAndId(urlSlug: String, id: Int): Optional<BlogCategory>

  @Query("select * from blog_category where id = :id and is_deleted = false", nativeQuery = true)
  fun findByIdAndIsDelete(id: Int): Optional<BlogCategory>
}