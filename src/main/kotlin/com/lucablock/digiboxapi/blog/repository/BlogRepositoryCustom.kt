package com.lucablock.digiboxapi.blog.repository

import com.lucablock.digiboxapi.blog.dto.BlogByIdDto
import com.lucablock.digiboxapi.blog.dto.BlogBySlugDto
import com.lucablock.digiboxapi.blog.response.BlogGalleryResponse
import com.lucablock.digiboxapi.blog.response.BlogResponse
import com.lucablock.digiboxapi.blog.response.BlogTypeResponse
import com.lucablock.digiboxapi.blog.response.CategoriesResponse
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface BlogRepositoryCustom {
  fun findAllBlog(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String,
  ): Page<BlogResponse>

  fun getBlogAdmin(
    pageable: Pageable,
    type: Int?,
    category: List<Int>?,
    ascending: Boolean,
    sortField: String,
  ): Page<BlogResponse>

  fun getBlogAdminById(id: Int): BlogByIdDto

  fun getBlogBySlug(slug: String): BlogBySlugDto

  fun getBlogTypeById(id: Int): BlogTypeResponse

  fun getCategoryById(id: Int): CategoriesResponse

  fun findAllBlogType(): List<BlogTypeResponse>

  fun findAllCategories(): List<CategoriesResponse>

  fun findAllGallery(): List<BlogGalleryResponse>

  fun findGalleryById(id: Int): BlogGalleryResponse

  fun getGalleries(pageable: Pageable): Page<BlogGalleryResponse>

}