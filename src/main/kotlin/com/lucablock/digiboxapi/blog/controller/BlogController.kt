package com.lucablock.digiboxapi.blog.controller

import com.lucablock.digiboxapi.blog.request.BlogRequest
import com.lucablock.digiboxapi.blog.request.BlogTypeRequest
import com.lucablock.digiboxapi.blog.request.CategoriesRequest
import com.lucablock.digiboxapi.blog.service.BlogService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import io.swagger.v3.oas.annotations.Operation
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.FieldError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("api")
class BlogController @Autowired internal constructor(
  private var blogService: BlogService,
) {
  private val logger: Logger = LoggerFactory.getLogger(BlogController::class.java)

  @ExceptionHandler(MethodArgumentNotValidException::class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  fun handleValidationExceptions(ex: MethodArgumentNotValidException): Map<String, Any?> {
    val errors = mutableMapOf<String, Any?>()
    errors["status"] = false
    logger.error("Validation failed: $errors")

    // Combine all error messages into a single message
    val errorMessage = ex.bindingResult.allErrors
      .joinToString(" และ ") { (it as FieldError).defaultMessage ?: "Invalid value" }

    errors["message"] = errorMessage
    errors["data"] = null

    logger.error("Validation failed: $errorMessage")

    return errors
  }


  //  Blog Type Endpoints
  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Create Type")
  @PostMapping("/admin/blog/type")
  fun createBlogType(
    @Valid @RequestBody createBlogType: BlogTypeRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "สร้างประเภทบทความสำเร็จ",
            data = blogService.createBlogType(createBlogType)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This name or url slug is already exist : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างประเภทบทความ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Failed to create type : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @Operation(summary = "Get all Type")
  @GetMapping("/web/blog/type")
  fun getBlogTypes(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "แสดงข้อมูลสำเร็จ",
            data = blogService.getBlogTypes()
          )
        )
    } catch (e: NotFoundException) {
      logger.error("Not found blog type list: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: Exception) {
      logger.error("Failed to get blog type list: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @Operation(summary = "Get Type with id")
  @GetMapping("/web/blog/type/{id}")
  fun getBlogTypeById(
    @PathVariable id: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "แสดงข้อมูลสำเร็จ",
            data = blogService.getBlogTypeById(id)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This type id is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: NotFoundException) {
      logger.error("Not found this type id $id : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Failed to get type: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Edit Type")
  @PutMapping("/admin/blog/type/{id}")
  fun updateBlogType(
    @PathVariable id: Int,
    @Valid @RequestBody updateBlogType: BlogTypeRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "บันทึกข้อมูลประเภทบทความสำเร็จ",
            data = blogService.updateBlogType(id, updateBlogType)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This type is already exist : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลประเภทบทความ กรุณาลองอีกครั้ง"
          )
        )
    } catch (e: NotFoundException) {
      logger.error("Not found this type id $id : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลประเภทบทความ กรุณาลองอีกครั้ง"
          )
        )
    } catch (e: Exception) {
      logger.error("Failed to update type: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Delete type")
  @DeleteMapping("/admin/blog/type/{id}")
  fun deleteBlogType(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "ลบข้อมูลสำเร็จ",
            data = blogService.deleteBlogType(id)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This type id is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: NotFoundException) {
      logger.error("Not found this id : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Failed to delete type: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }


  //  Blog Category Endpoints
  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Create Blog category")
  @PostMapping("/admin/blog/category")
  fun createCategory(
    @Valid @RequestBody createCategory: CategoriesRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "สร้างหมวดหมู่บทความสำเร็จ",
            data = blogService.createCategory(createCategory)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This Category is already exist: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างหมวดหมู่บทความ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Create failed: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @Operation(summary = "Get all blog category")
  @GetMapping("/web/blog/category")
  fun getAllCategories(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "แสดงข้อมูลสำเร็จ",
            data = blogService.getBlogCategories()
          )
        )
    } catch (e: NotFoundException) {
      logger.error("Not found blog categories: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: Exception) {
      logger.error("Failed to get blog categories: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(status = false, message = e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @Operation(summary = "Get blog category")
  @GetMapping("/web/blog/category/{id}")
  fun getCategoryById(
    @PathVariable id: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "แสดงข้อมูลสำเร็จ",
            data = blogService.getCategoryById(id)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This category id is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: NotFoundException) {
      logger.error("Not found this id $id: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Failed to get Category: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "update blog category")
  @PutMapping("/admin/blog//category/{id}")
  fun updateCategory(
    @PathVariable id: Int,
    @Valid @RequestBody updateCategory: CategoriesRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "บันทึกข้อมูลหมวดหมู่บทความสำเร็จ",
            data = blogService.updateCategory(id, updateCategory)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This Category is already exist: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลหมวดหมู่บทความ กรุณาลองอีกครั้ง"
          )
        )
    } catch (e: NotFoundException) {
      logger.error("Not found this id $id: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลหมวดหมู่บทความ กรุณาลองอีกครั้ง"
          )
        )
    } catch (e: Exception) {
      logger.error("Failed to update category: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Delete Category")
  @DeleteMapping("/admin/blog/category/{id}")
  fun deleteCategory(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "ลบข้อมูลสำเร็จ",
            data = blogService.deleteCategory(id)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This category id is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: NotFoundException) {
      logger.error("Not found this category : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Failed to delete category")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }


  //  Blog Gallery Endpoints
  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Create gallery")
  @PostMapping("/admin/blog/gallery", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
  fun createGallery(
    @Valid @RequestParam("file", required = true) file: MultipartFile,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "สร้างแกลเลอรี่สำเร็จ",
            data = blogService.createGallery(file)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This file is not valid or already exist : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างแกลเลอรี่ได้ กรุณาลองอีกครั้ง"))

    } catch (e: NotFoundException) {
      logger.error("Not found platform : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มสามารถสร้างแกลเลอรี่ได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Create gallery error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @Operation(summary = "Get all gallery")
  @GetMapping("/web/blog/gallery")
  fun getAllBlogGallery(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
  ): ResponseEntity<Any> {
    val pageable: Pageable = PageRequest.of(page, size).withSort(Sort.by("id").descending())

    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "แสดงข้อมูลสำเร็จ",
            data = blogService.getGalleries(pageable)
          )
        )
    } catch (e: NotFoundException) {
      logger.error("Not found gallery list: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: Exception) {
      logger.error("Failed to get gallery list: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Delete gallery")
  @DeleteMapping("/web/blog/gallery/{id}")
  fun deleteGallery(
    @PathVariable id: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "ลบข้อมูลสำเร็จ",
            data = blogService.deleteGallery(id)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This gallery id is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: NotFoundException) {
      logger.error("Not found this gallery: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Failed to delete gallery: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }


  //  Blog Endpoints
  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Create Blog")
  @PostMapping("/admin/blog")
  fun createBlog(
    @Valid @RequestBody createBlog: BlogRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "สร้างบทความสำเร็จ",
            data = blogService.createBlog(createBlog)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("Bad request err: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างบทความ กรุณาลองอีกครั้ง"))
    } catch (e: NotFoundException) {
      logger.error("Not found this id : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างบทความ กรุณาลองอีกครั้ง"))

    } catch (e: Exception) {
      logger.error("Failed to create the new Blog, Try again later: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @Operation(summary = "Get all blog")
  @GetMapping("/web/blog/published")
  fun getAllBlog(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("type", required = false) type: Int?,
    @RequestParam("category", required = false) category: List<Int>?,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("sortField", required = false) sortField: String?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size).withSort(Sort.by("id").descending())

      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "แสดงรายการบทความสำเร็จ",
            data = blogService.getBlogs(pageable, type, category, ascending, sortField ?: "id")
          )
        )
    } catch (e: Exception) {
      logger.error("Failed to get all Blog list: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Get blog admin")
  @GetMapping("/admin/blog")
  fun getBlogAdmin(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("type", required = false) type: Int?,
    @RequestParam("category", required = false) category: List<Int>?,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("sortField", required = false) sortField: String?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size).withSort(Sort.by("id").descending())

      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "แสดงรายการบทความสำเร็จ",
          data = blogService.getBlogAdmin(pageable, type, category, ascending, sortField ?: "id")
        )
      )
    } catch (e: Exception) {
      logger.error("Failed to get all Blog list: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Get blog admin by id")
  @GetMapping("/admin/blog/{id}")
  fun getBlogAdminById(
    @PathVariable id: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          blogService.getBlogByAdminId(id)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("This blog id is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง")
      )

    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์")
      )
    }
  }

  @Operation(summary = "Get blog slug by id")
  @GetMapping("/web/blog/slug/{urlSlug}")
  fun getBlogBySlug(
    @PathVariable urlSlug: String,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = blogService.getBlogBySlug(urlSlug)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("This Url Slug is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: NotFoundException) {
      logger.error("Not found Url Slug: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์")
      )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Update blog")
  @PutMapping("/admin/blog/{id}")
  fun updateBlog(
    @PathVariable id: Int,
    @Valid @RequestBody updateBlog: BlogRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "บันทึกข้อมูลบทความสำเร็จ",
            data = blogService.updateBlog(id, updateBlog)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This Blog title is already exist: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลบทความ กรุณาลองอีกครั้ง"))
    } catch (e: NotFoundException) {
      logger.error("Not found this id $id: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลบทความ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Failed to get blog with id $id : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @Operation(summary = "Delete blog")
  @DeleteMapping("/admin/blog/{id}")
  fun deleteBlog(
    @PathVariable id: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok()
        .body(
          HttpResponse(
            status = true,
            message = "ลบข้อมูลสำเร็จ",
            data = blogService.deleteBlog(id)
          )
        )
    } catch (e: BadRequestException) {
      logger.error("This blog id is not valid: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))

    } catch (e: NotFoundException) {
      logger.error("Not found this id $id: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    } catch (e: Exception) {
      logger.error("Create blog type error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"))
    }
  }
}