package com.lucablock.digiboxapi.blog.request

import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

data class BlogRequest(
  @field:NotBlank(message = "กรุณากรอกชื่อบทความ")
  val title: String,
  @field:NotBlank(message = "กรุณาระบุเนื้อหาบทความ")
  val content: String,
  @field:NotEmpty(message = "กรุณาเลือกหมวดหมู่บทความ")
  val categories: List<Int>,
  @field:Min(1, message = "กรุณาเลือกประเภทบทความ")
  val blogTypeId: Int,
  @field:NotBlank(message = "กรุณากรอกคีย์เวิร์ดสำหรับ SEO")
  val keyword: String,
  val isPublished: <PERSON>olean,
  @field:NotBlank(message = "กรุณากรอก URL รูปปก")
  val thumbNailUrl: String,
  @field:NotBlank(message = "กรุณากรอก URL Slug")
  val urlSlug: String,
  val description: String,
  @field:NotBlank(message = "กรุณากรอกชื่อผู้เขียน")
  val author: String,
)
