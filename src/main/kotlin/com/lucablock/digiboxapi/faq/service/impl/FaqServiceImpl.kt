package com.lucablock.digiboxapi.faq.service.impl

import com.lucablock.digiboxapi.entity.Faq
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.faq.dto.FaqDto
import com.lucablock.digiboxapi.faq.repository.FaqRepository
import com.lucablock.digiboxapi.faq.request.CreateFaqRequest
import com.lucablock.digiboxapi.faq.service.FaqService
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class FaqServiceImpl @Autowired internal constructor(
  private val faqRepository: FaqRepository
): FaqService {
  override fun createFaq(
    userPrincipal: UserPrincipal,
    request: CreateFaqRequest
  ): Faq {
    val faq = Faq(
      question = request.question,
      answer = request.answer,
      isActive = request.isActive,
      userId = userPrincipal.getUserId()
    )
    return faqRepository.save(faq)
  }

  override fun updateFaq(
    id: Long,
    userPrincipal: UserPrincipal,
    request: CreateFaqRequest
  ): Faq {
    val faq = faqRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบ FAQ ที่มี ID: $id")
    }
    faq.question = request.question
    faq.answer = request.answer
    faq.isActive = request.isActive
    return faqRepository.save(faq)
  }

  override fun getAllFaqsForPublic(): List<FaqDto> {
    return faqRepository.findAllByIsActiveTrue()
      .sortedByDescending { it.createdDate }
      .sortedByDescending { it.updatedDate }
      .map { it.toDto() }
  }

  override fun getAllFaqsForAdmin(): List<FaqDto> {
    return faqRepository.findAll()
      .sortedByDescending { it.createdDate }
      .sortedByDescending { it.isActive }
      .sortedByDescending { it.updatedDate }
      .map { it.toDto() }
  }

  override fun getFaqById(id: Long): FaqDto {
    val faq = faqRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบ FAQ ที่มี ID: $id")
    }
    return faq.toDto()
  }

  override fun getAllFaqsForAdminPaginated(pageable: Pageable, ascending: Boolean): Page<FaqDto> {
    val direction = if (ascending) Sort.Direction.ASC else Sort.Direction.DESC
    val sort = Sort.by(direction, "updatedDate")
      .and(Sort.by(direction, "isActive"))
      .and(Sort.by(direction, "createdDate"))
    
    val pageableWithSort = PageRequest.of(pageable.pageNumber, pageable.pageSize, sort)
    return faqRepository.findAll(pageableWithSort).map { it.toDto() }
  }

  override fun deleteFaq(
    id: Long,
    userPrincipal: UserPrincipal
  ) {
    val faq = faqRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบ FAQ ที่มี ID: $id")
    }
    faqRepository.delete(faq)
  }
}