package com.lucablock.digiboxapi.faq.service

import com.lucablock.digiboxapi.entity.Faq
import com.lucablock.digiboxapi.faq.dto.FaqDto
import com.lucablock.digiboxapi.faq.request.CreateFaqRequest
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface FaqService {
  /**
   * Create a new FAQ entry.
   *
   * @param userPrincipal The user making the request.
   * @param request The request containing FAQ details.
   * @return The created FAQ entry.
   */
  fun createFaq(userPrincipal: UserPrincipal, request: CreateFaqRequest): Faq
  /**
   * Update an existing FAQ entry.
   *
   * @param id The ID of the FAQ to update.
   * @param userPrincipal The user making the request.
   * @param request The request containing updated FAQ details.
   * @return The updated FAQ entry.
   */
  fun updateFaq(id: Long, userPrincipal: UserPrincipal, request: CreateFaqRequest): Faq
  /**
   * Delete an FAQ entry.
   *
   * @param id The ID of the FAQ to delete.
   * @param userPrincipal The user making the request.
   */
  fun deleteFaq(id: Long, userPrincipal: UserPrincipal)

  /**
   * Get FAQ by ID.
   *
   * @param id The ID of the FAQ.
   * @return The FAQ with the given ID.
   */
  fun getFaqById(id: Long): FaqDto

  /**
   * Retrieve all FAQs.
   *
   * @return A list of all FAQs.
   */
  fun getAllFaqsForAdmin(): List<FaqDto>

  /**
   * Retrieve all FAQs with pagination for admin.
   *
   * @param pageable The pagination information.
   * @param ascending Whether to sort in ascending order.
   * @return A page of FAQs.
   */
  fun getAllFaqsForAdminPaginated(pageable: Pageable, ascending: Boolean): Page<FaqDto>

  /**
   * Retrieve all FAQs for public view.
   *
   * @return A list of all FAQs.
   */
  fun getAllFaqsForPublic(): List<FaqDto>
}