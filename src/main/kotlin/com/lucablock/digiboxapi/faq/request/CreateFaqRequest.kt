package com.lucablock.digiboxapi.faq.request

import jakarta.persistence.Column
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

data class CreateFaqRequest(
  @field:NotNull(message = "กรุณาระบุคำถาม")
  @field:NotBlank(message = "กรุณาระบุคำถาม")
  val question: String,
  @field:NotNull(message = "กรุณาระบุตอบคำตอบ")
  @field:NotBlank(message = "กรุณาระบุตอบคำตอบ")
  val answer: String,

  @field:Column
  val isActive: Boolean = false
)
