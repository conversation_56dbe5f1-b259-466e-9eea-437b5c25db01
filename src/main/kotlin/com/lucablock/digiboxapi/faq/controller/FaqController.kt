package com.lucablock.digiboxapi.faq.controller

import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.faq.request.CreateFaqRequest
import com.lucablock.digiboxapi.faq.service.FaqService
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
class FaqController @Autowired internal constructor(
  private val faqService: FaqService,
) {
  private val logger: Logger = LoggerFactory.getLogger(FaqController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/faq")
  fun createFaq(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody request: CreateFaqRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้าง FAQ สำเร็จ",
          faqService.createFaq(userPrincipal, request)
        )
      )
    } catch (e: Exception) {
      logger.error("Error creating FAQ: ${e.message}")
      ResponseEntity.status(500).body("Error creating FAQ")
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/faq/{id}")
  fun updateFaq(
    @PathVariable id: Long,
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody request: CreateFaqRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "อัพเดต FAQ สำเร็จ",
          faqService.updateFaq(id, userPrincipal, request)
        )
      )

    } catch (e: NotFoundException) {
      logger.error("Error updating FAQ: ${e.message}")
      ResponseEntity.status(404).body(e.message ?: "FAQ not found")
    } catch (e: IllegalArgumentException) {
      logger.error("Error updating FAQ: ${e.message}")
      ResponseEntity.badRequest().body(e.message ?: "Invalid request")
    } catch (e: Exception) {
      logger.error("Error updating FAQ: ${e.message}")
      ResponseEntity.status(500).body("Error updating FAQ")
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @GetMapping("/admin/faq/page")
  fun getAllFaqs(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "10") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean
  ): ResponseEntity<Any> {
    return try {
      val pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ดึงข้อมูล FAQ สำเร็จ",
          faqService.getAllFaqsForAdminPaginated(pageable, ascending)
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching FAQs: ${e.message}")
      ResponseEntity.status(500).body("Error fetching FAQs")
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @GetMapping("/admin/faq/{id}")
  fun getFaqById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ดึงข้อมูล FAQ สำเร็จ",
          faqService.getFaqById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Error fetching FAQ: ${e.message}")
      ResponseEntity.status(404).body(e.message ?: "FAQ not found")
    } catch (e: Exception) {
      logger.error("Error fetching FAQ: ${e.message}")
      ResponseEntity.status(500).body("Error fetching FAQ")
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/faq/{id}")
  fun deleteFaq(
    @PathVariable id: Long,
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal
  ): ResponseEntity<Any> {
    return try {
      faqService.deleteFaq(id, userPrincipal)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบ FAQ สำเร็จ",
          null
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Error deleting FAQ: ${e.message}")
      ResponseEntity.status(404).body(e.message ?: "FAQ not found")
    } catch (e: Exception) {
      logger.error("Error deleting FAQ: ${e.message}")
      ResponseEntity.status(500).body("Error deleting FAQ")
    }
  }

  @GetMapping("/web/faq/list")
  fun getAllFaqsForUser(): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ดึงข้อมูล FAQ สำเร็จ",
          faqService.getAllFaqsForPublic()
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching FAQs for user: ${e.message}")
      ResponseEntity.status(500).body("Error fetching FAQs")
    }
  }
}