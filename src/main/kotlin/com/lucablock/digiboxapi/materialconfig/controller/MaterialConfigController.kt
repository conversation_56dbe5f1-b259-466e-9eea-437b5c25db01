package com.lucablock.digiboxapi.materialconfig.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.gram.request.GramRequest
import com.lucablock.digiboxapi.materialconfig.request.MaterialConfigRequest
import com.lucablock.digiboxapi.materialconfig.service.MaterialConfigService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class MaterialConfigController @Autowired internal constructor(
  private val materialConfigService: MaterialConfigService
){
  private val logger: Logger = LoggerFactory.getLogger(MaterialConfigController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/material-config")
  fun createMaterialConfig(
    @Valid @RequestBody materialConfigRequest: MaterialConfigRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างข้อมูลวัสดุต่อขนาดแกรมสำเร็จ",
          data = materialConfigService.createMaterialConfig(materialConfigRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลวัสดุต่อขนาดแกรมซ้ำ"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/material-config")
  fun findAllMaterialConfig(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลวัสดุต่อขนาดแกรมสำเร็จ",
          data = materialConfigService.getAllMaterialConfig()
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวัสดุต่อขนาดแกรม"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/material-config/{id}")
  fun findMaterialConfigById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลวัสดุต่อขนาดแกรมสำเร็จ",
          data = materialConfigService.getMaterialConfigById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวัสดุต่อขนาดแกรม"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/material-config/{id}")
  fun deleteMaterialConfig(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบข้อมูลวัสดุต่อขนาดแกรมสำเร็จ",
          data = materialConfigService.deleteMaterialConfig(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวัสดุต่อขนาดแกรม"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลวัสดุต่อขนาดแกรมได้ มีการใช้งานอยู่"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลวัสดุต่อขนาดแกรม กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/material-config/page")
  fun getPageMaterialConfig(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("materialId", required = false) materialId: Long?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = materialConfigService.getPageMaterialConfig(
            pageable,
            ascending,
            search,
            materialId,
            isActive
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }


}