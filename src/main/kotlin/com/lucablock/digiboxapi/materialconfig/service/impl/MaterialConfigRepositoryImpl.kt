package com.lucablock.digiboxapi.materialconfig.service.impl

import com.lucablock.digiboxapi.entity.QGram
import com.lucablock.digiboxapi.entity.QMaterialConfig
import com.lucablock.digiboxapi.entity.QMaterials
import com.lucablock.digiboxapi.entity.QProductTag
import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigDto
import com.lucablock.digiboxapi.materialconfig.repository.MaterialConfigRepositoryCustom
import com.querydsl.core.BooleanBuilder
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class MaterialConfigRepositoryImpl : MaterialConfigRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }
  private val qMaterialConfig = QMaterialConfig.materialConfig
  private val qMaterials = QMaterials.materials
  private val qGram = qMaterialConfig.grams

  override fun getPageMaterialConfig(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    materialId: Long?,
    isActive: Boolean?
  ): Page<MaterialConfigDto> {
    var criteria = qMaterialConfig.isDeleted.eq(false)
      .and(qMaterials.isDeleted.eq(false)
    )
    if (isActive != null) {
      criteria = criteria.and(qMaterialConfig.isActive.eq(isActive))
    }
    if (search != null) {
      val keywordBuilder = BooleanBuilder()
      val keywords = search.trim().split("\\s+".toRegex())

      for (keyword in keywords) {
        val gsm = keyword.toIntOrNull()
        val keywordCondition = BooleanBuilder()

        if (gsm != null) {
          keywordCondition.or(qGram.gsm.eq(gsm))
        }
        keywordCondition.or(qMaterials.name.containsIgnoreCase(keyword))

        keywordBuilder.and(keywordCondition)
      }

      criteria = criteria.and(keywordBuilder)
    }
    if (materialId != null) {
      criteria = criteria.and(qMaterialConfig.materialsId.eq(qMaterials.id))
    }
    var sortGram = qGram.gsm.desc()
    var sortMaterial = qMaterials.name.desc()
    if (ascending) {
      sortMaterial = qMaterials.name.asc()
      sortGram = qGram.gsm.asc()
    }

    val query = queryFactory
      .select(qMaterialConfig)
      .from(qMaterialConfig)
      .where(criteria)
      .orderBy(sortMaterial,sortGram)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toMaterialConfigDto()
      }

    val total = queryFactory
      .select(qMaterialConfig)
      .from(qMaterialConfig)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}