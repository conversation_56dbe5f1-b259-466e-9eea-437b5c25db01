package com.lucablock.digiboxapi.materialconfig.service

import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigDto
import com.lucablock.digiboxapi.materialconfig.request.MaterialConfigRequest
import com.lucablock.digiboxapi.order.dto.AdminOrderDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface MaterialConfigService {
  fun createMaterialConfig(materialConfigRequest: MaterialConfigRequest):MaterialConfigDto
  fun getAllMaterialConfig():List<MaterialConfigDto>
  fun getMaterialConfigById(id:Long):MaterialConfigDto
  fun deleteMaterialConfig(id:Long):Boolean
  fun getPageMaterialConfig(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    materialId : Long?,
    isActive : Boolean?,
  ): Page<MaterialConfigDto>
}