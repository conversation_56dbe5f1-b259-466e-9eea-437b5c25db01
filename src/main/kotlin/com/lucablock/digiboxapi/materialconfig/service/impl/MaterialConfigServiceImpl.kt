package com.lucablock.digiboxapi.materialconfig.service.impl

import com.lucablock.digiboxapi.entity.MaterialConfig
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.gram.repository.GramRepository
import com.lucablock.digiboxapi.material.repository.MaterialRepository
import com.lucablock.digiboxapi.materialconfig.dto.GramListDto
import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigDto
import com.lucablock.digiboxapi.materialconfig.dto.MaterialListDto
import com.lucablock.digiboxapi.materialconfig.repository.MaterialConfigRepository
import com.lucablock.digiboxapi.materialconfig.request.MaterialConfigRequest
import com.lucablock.digiboxapi.materialconfig.service.MaterialConfigService
import com.lucablock.digiboxapi.modelsizeconfig.repository.ModelSizeConfigRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class MaterialConfigServiceImpl @Autowired constructor(
  private val materialConfigRepository: MaterialConfigRepository,
  private val materialRepository: MaterialRepository,
  private val gramRepository: GramRepository,
  private val modelSizeConfigRepository: ModelSizeConfigRepository
): MaterialConfigService {
  @Transactional
  override fun createMaterialConfig(materialConfigRequest: MaterialConfigRequest): MaterialConfigDto {
    materialConfigRepository.existsMaterialConfigByMaterialsIdAndGramIdAndIsDeletedFalse(
      materialConfigRequest.materialId,materialConfigRequest.gramId
    ).let {
      if (it){
        throw BadRequestException("วัสดุนี้มีขนาดแกรมนี้แล้ว")
      }
    }
    val material = materialRepository.findById(materialConfigRequest.materialId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวัสดุ")
    }
    if(!material.isActive){
      throw BadRequestException("วัสดุนี้ไม่พร้อมใช้งาน")
    }
    val gram = gramRepository.findById(materialConfigRequest.gramId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลแกรม")
    }
    val savedConfig = materialConfigRepository.save(
      MaterialConfig(
        materialsId = material.id,
        gramId = gram.id,
        materials = material,
        grams = gram,
      )
    )
    return MaterialConfigDto(
      id = savedConfig.id,
      isActive = savedConfig.isActive,
      isDeleted = savedConfig.isDeleted,
      materials = MaterialListDto(
        id = material.id,
        name = material.name,
        imageUrl = material.imageUrl
      ),
      grams = GramListDto(
        id = gram.id,
        gsm = gram.gsm,
        mm = gram.mm
      )
    )
  }

  override fun getAllMaterialConfig(): List<MaterialConfigDto> {
    return materialConfigRepository.findAllMaterialConfigByIsDeletedFalseAndIsActiveTrueOrderByMaterials_NameAscGrams_GsmAsc().map{
      it.toMaterialConfigDto()
    }
  }

  override fun getMaterialConfigById(id: Long): MaterialConfigDto {
    val matConfig = materialConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวัสดุต่อขนาดแกรม")
    }
    return matConfig.toMaterialConfigDto()
  }

  override fun deleteMaterialConfig(id: Long): Boolean {
    val matConfig = materialConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวัสดุต่อขนาดแกรม")
    }
    materialConfigRepository.delete(matConfig)
    return true
  }

  override fun getPageMaterialConfig(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    materialId: Long?,
    isActive: Boolean?
  ): Page<MaterialConfigDto> {
    return materialConfigRepository.getPageMaterialConfig(
      pageable, ascending, search, materialId, isActive
    )
  }
}