package com.lucablock.digiboxapi.materialconfig.repository

import com.lucablock.digiboxapi.entity.MaterialConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface MaterialConfigRepository : JpaRepository<MaterialConfig, Long>, MaterialConfigRepositoryCustom {
  fun existsMaterialConfigByMaterialsIdAndGramIdAndIsDeletedFalse(materialsId: Int, gramId: Long): Boolean
  fun findAllMaterialConfigByIsDeletedFalseAndIsActiveTrueOrderByMaterials_NameAscGrams_GsmAsc(): List<MaterialConfig>
  fun existsMaterialConfigByMaterialsNameIgnoreCaseAndGramIdAndIsDeletedFalse(name:String,gramId: Long):<PERSON><PERSON><PERSON>
  @Query("SELECT m FROM MaterialConfig m JOIN m.materials mat ORDER BY mat.name ASC")
  fun findAllMaterialConfigOrderByMaterialsNameAsc():List<MaterialConfig>
  fun findAllByMaterialsNameIgnoreCaseAndGramIdInAndIsDeletedFalse(name: String, gramId: List<Long>):List<MaterialConfig>
  fun findAllByMaterialsId(materialsId: Int): List<MaterialConfig>
  fun findAllByMaterialsIdAndIsDeletedFalse(materialsId: Int): List<MaterialConfig>
  fun findAllByGramId(gramId: Long): MutableList<MaterialConfig>
}