package com.lucablock.digiboxapi.materialconfig.dto

import com.querydsl.core.annotations.QueryProjection

data class MaterialConfigDto
@QueryProjection
constructor(
  val id: Long = 0,
  val isActive : Boolean,
  val isDeleted : Boolean,
  val materials: MaterialListDto,
  val grams: GramListDto,
)

data class MaterialListDto
@QueryProjection
constructor(
  val id: Int = 0,
  val name: String = "",
  val imageUrl: String = "",
)

data class GramListDto
@QueryProjection
constructor(
  val id: Long = 0,
  val gsm: Int,
  val mm: Double,
)
data class MaterialConfigListDto
@QueryProjection
constructor(
  val id: Long = 0,
  val materialsId: Int,
  val materialsName : String,
  val gramsId: Long,
  val gsm: Int,
  val mm: Double,
)
