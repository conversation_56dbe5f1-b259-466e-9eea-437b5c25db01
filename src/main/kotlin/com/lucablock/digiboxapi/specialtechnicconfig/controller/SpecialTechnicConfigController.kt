package com.lucablock.digiboxapi.specialtechnicconfig.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.specialtechnicconfig.request.SpecialTechnicConfigRequest
import com.lucablock.digiboxapi.specialtechnicconfig.service.SpecialTechnicConfigService
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class SpecialTechnicConfigController {
  private val logger: Logger = LoggerFactory.getLogger(SpecialTechnicConfigController::class.java)

  @Autowired
  lateinit var specialTechnicConfigService: SpecialTechnicConfigService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/special-technic-config")
  fun createSpecialTechnicConfig(
    @Valid @RequestBody specialTechnicConfigRequest: SpecialTechnicConfigRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลพื้นที่เทคนิคพิเศษสำเร็จ",
          specialTechnicConfigService.createSpecialTechnicConfig(specialTechnicConfigRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, "ข้อมูลพื้นที่เทคนิคพิเศษนี้มีอยู่แล้ว"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/special-technic-config/{id}")
  fun updateSpecialTechnicConfig(
    @PathVariable id: Long,
    @Valid @RequestBody specialTechnicConfigRequest: SpecialTechnicConfigRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลพื้นที่เทคนิคพิเศษสำเร็จ",
          specialTechnicConfigService.updateSpecialTechnicConfig(id, specialTechnicConfigRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลพื้นที่เทคนิคพิเศษ"))
    } catch (e: Exception) {
      logger.error("update coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @GetMapping("/web/special-technic-config")
  fun findAllSpecialTechnicConfig(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลพื้นที่เทคนิคพิเศษทั้งหมดสำเร็จ",
          specialTechnicConfigService.getSpecialTechnicConfig()
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/special-technic-config/{id}")
  fun findSpecialTechnicConfigById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลพื้นที่เทคนิคพิเศษสำเร็จ",
          specialTechnicConfigService.getSpecialTechnicConfigById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลพื้นที่เทคนิคพิเศษ"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/special-technic-config/specialTechnicId")
  fun findSpecialTechnicConfigBySpecialTechnicId(
    @RequestParam("id", required = true) id: Int
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลพื้นที่เทคนิคพิเศษสำเร็จ",
          specialTechnicConfigService.getSpecialTechnicConfigBySpecialTechnicId(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลพื้นที่เทคนิคพิเศษ"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/special-technic-config/{id}")
  fun deleteSpecialTechnicConfig(@PathVariable id: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลพื้นที่เทคนิคพิเศษสำเร็จ",
          specialTechnicConfigService.deleteSpecialTechnicConfig(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลพื้นที่เทคนิคพิเศษ"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "พื้นที่เทคนิคพิเศษนี้ถูกใช้งานอยู่ ไม่สามารถลบได้"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

}