package com.lucablock.digiboxapi.specialtechnicconfig.service

import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigDto
import com.lucablock.digiboxapi.specialtechnicconfig.request.SpecialTechnicConfigRequest

interface SpecialTechnicConfigService {
  fun createSpecialTechnicConfig(
    specialTechnicConfigRequest: SpecialTechnicConfigRequest
  ):SpecialTechnicConfigDto
  fun updateSpecialTechnicConfig(
    id: Long,
    specialTechnicConfigRequest: SpecialTechnicConfigRequest
  ):SpecialTechnicConfigDto
  fun getSpecialTechnicConfigById(id: Long): SpecialTechnicConfigDto
  fun getSpecialTechnicConfig(): List<SpecialTechnicConfigDto>
  fun getSpecialTechnicConfigBySpecialTechnicId(
    specialTechnicId: Int
  ): List<SpecialTechnicConfigDto>
  fun deleteSpecialTechnicConfig(id: Long):Bo<PERSON>an
}