package com.lucablock.digiboxapi.specialtechnicconfig.service.impl

import com.lucablock.digiboxapi.areasizepercentage.repository.AreaSizePercentageRepository
import com.lucablock.digiboxapi.entity.SpecialTechnicConfig
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.specialTechnic.repository.SpecialTechnicRepository
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigDto
import com.lucablock.digiboxapi.specialtechnicconfig.repository.SpecialTechnicConfigRepository
import com.lucablock.digiboxapi.specialtechnicconfig.request.SpecialTechnicConfigRequest
import com.lucablock.digiboxapi.specialtechnicconfig.service.SpecialTechnicConfigService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class SpecialTechnicConfigServiceImpl @Autowired constructor(
  private val specialTechnicConfigRepository: SpecialTechnicConfigRepository,
  private val specialTechnicRepository: SpecialTechnicRepository,
  private val areaSizePercentageRepository: AreaSizePercentageRepository
): SpecialTechnicConfigService {
  override fun createSpecialTechnicConfig(specialTechnicConfigRequest: SpecialTechnicConfigRequest): SpecialTechnicConfigDto {
    val specialTechnic = specialTechnicRepository.findById(specialTechnicConfigRequest.specialTechnicId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลเทคนิคพิเศษ")
    }
    val areaSize = areaSizePercentageRepository.findById(specialTechnicConfigRequest.areaSizePercentageId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลพื้นที่")
    }
    specialTechnicConfigRepository.existsSpecialTechnicConfigBySpecialTechnicIdAndAreaSizePercentageId(
      specialTechnicConfigRequest.specialTechnicId,
      specialTechnicConfigRequest.areaSizePercentageId
    ).let {
      if (it){
        throw Exception("ข้อมูลเทคนิคพิเศษและพื้นที่ซ้ำ")
      }
    }
    val specialTechnicConfig = specialTechnicConfigRepository.save(
      SpecialTechnicConfig(
        price = specialTechnicConfigRequest.price,
        period = specialTechnicConfigRequest.period,
        specialTechnicId = specialTechnic.id,
        areaSizePercentageId = areaSize.id.toLong(),
        specialTechnic = specialTechnic,
        areaSizePercentage = areaSize
      )
    )
    return specialTechnicConfig.toSpecialTechnicConfig()
  }

  override fun updateSpecialTechnicConfig(
    id: Long,
    specialTechnicConfigRequest: SpecialTechnicConfigRequest
  ): SpecialTechnicConfigDto {
    val specialTechnicConfig = specialTechnicConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลพื้นที่เทคนิคพิเศษ")
    }
    specialTechnicConfig.price = specialTechnicConfigRequest.price
    specialTechnicConfigRepository.save(specialTechnicConfig)
    return specialTechnicConfig.toSpecialTechnicConfig()
  }

  override fun getSpecialTechnicConfigById(id: Long): SpecialTechnicConfigDto {
    val specialTechnicConfig = specialTechnicConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลพื้นที่เทคนิคพิเศษ")
    }
    return specialTechnicConfig.toSpecialTechnicConfig()
  }

  override fun getSpecialTechnicConfig(): List<SpecialTechnicConfigDto> {
    return specialTechnicConfigRepository.findAllSpecialTechnicConfigByIsDeletedFalse().map { it.toSpecialTechnicConfig() }
  }

  override fun getSpecialTechnicConfigBySpecialTechnicId(specialTechnicId: Int): List<SpecialTechnicConfigDto> {
    val specialTechnic = specialTechnicRepository.findById(specialTechnicId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลเทคนิคพิเศษ")
    }
    val specialTechnicConfig = specialTechnicConfigRepository.findSpecialTechnicConfigBySpecialTechnicIdAndIsDeletedFalse(specialTechnic.id)
    return specialTechnicConfig.map { it.toSpecialTechnicConfig() }
  }

  override fun deleteSpecialTechnicConfig(id: Long): Boolean {
    val specialTechnicConfig = specialTechnicConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลพื้นที่เทคนิคพิเศษ")
    }
    specialTechnicConfigRepository.delete(specialTechnicConfig)
    return true
  }

}