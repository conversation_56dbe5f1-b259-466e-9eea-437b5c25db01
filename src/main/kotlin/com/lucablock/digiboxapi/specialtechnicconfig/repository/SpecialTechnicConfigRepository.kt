package com.lucablock.digiboxapi.specialtechnicconfig.repository

import com.lucablock.digiboxapi.entity.ItemSpecialTechnic
import com.lucablock.digiboxapi.entity.SpecialTechnicConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SpecialTechnicConfigRepository : JpaRepository<SpecialTechnicConfig, Long> {
  fun existsSpecialTechnicConfigBySpecialTechnicIdAndAreaSizePercentageId(
    specialTechnicId: Int,
    areaSizeId: Long
  ):Boolean

  fun findSpecialTechnicConfigBySpecialTechnicIdAndIsDeletedFalse(specialTechnicId: Int): MutableList<SpecialTechnicConfig>
  fun deleteAllBySpecialTechnicId(specialTechnicId: Int)
  fun findByAreaSizePercentageId(areaSizePercentageId: Long): MutableList<SpecialTechnicConfig>
  fun findAllSpecialTechnicConfigById(id: Long): List<SpecialTechnicConfig>
  fun findAllBySpecialTechnicId(specialTechnicId: Int): MutableList<SpecialTechnicConfig>
  fun deleteAllByIdInAndIsDeletedFalse(ids: Set<Long>)
  fun deleteAllByIdIn(ids: List<Long>)
  fun deleteAllBySpecialTechnicIdAndIsDeletedFalse(specialTechnicId: Int)
  fun findAllByIdIn(ids: MutableCollection<Long>): MutableList<SpecialTechnicConfig>
  fun findAllSpecialTechnicConfigByIsDeletedFalse(): MutableList<SpecialTechnicConfig>
  fun findAllBySpecialTechnicIdAndIsDeletedFalse(id:Int): MutableList<SpecialTechnicConfig>
}