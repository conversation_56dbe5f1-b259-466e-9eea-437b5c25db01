package com.lucablock.digiboxapi.specialtechnicconfig.request

import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive

class SpecialTechnicConfigRequest {
  @NotNull(message = "กรุณาระบุรหัสเทคนิคพิเศษ")
  var specialTechnicId: Int = 0
  @Positive(message = "กรุณาระบุรหัสพื้นที่")
  @NotNull(message = "กรุณาระบุรหัสพื้นที่")
  var areaSizePercentageId: Long = 0
  @Positive(message = "กรุณาระบุราคา")
  @NotNull(message = "กรุณาระบุราคา")
  var price: Double = 0.0
  @NotNull(message = "กรุณาระบุระยะเวลาการผลิต")
  @Positive(message = "กรุณาระบุระยะเวลาการผลิต")
  var period : Int = 0
}