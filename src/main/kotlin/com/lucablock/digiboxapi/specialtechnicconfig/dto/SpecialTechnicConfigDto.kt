package com.lucablock.digiboxapi.specialtechnicconfig.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class SpecialTechnicConfigDto
@QueryProjection
constructor(
  val id: Long,
  val price: Double,
  val specialTechnic: SpecialTechnicListDto,
  val areaSize : AreaSizePercentageListDto,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
)

data class SpecialTechnicListDto
@QueryProjection
constructor(
  val id: Int,
  val name: String,
  val description: String?,
  val imageUrl: String,
)

data class AreaSizePercentageListDto
@QueryProjection
constructor(
  val id: Int,
  val name: String,
  val percentage: Int,
  val imageUrl: String,
)

data class SpecialTechnicConfigListDto
@QueryProjection
constructor(
  val id: Long,
  val price: Double,
  val specialTechnic: SpecialTechnicListDto,
  val areaSize : AreaSizePercentageListDto,
)
data class SpecialTechnicConfigCustomDto
@QueryProjection
constructor(
  val id: Long,
  val customId : Long,
  val price: Double,
  val specialTechnicId: Int,
  val specialTechnicName : String,
  val areaSizePercentageId : Int,
  val areaSizePercentageName : String,
  val areaSizePercentage: Int
)