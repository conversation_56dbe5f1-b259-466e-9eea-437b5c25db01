package com.lucablock.digiboxapi.oauth2

import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.entity.UserRoleEnum
import com.lucablock.digiboxapi.exception.InternalServerException
import com.lucablock.digiboxapi.exception.OAuth2AuthenticationProcessingException
import com.lucablock.digiboxapi.oauth2.dto.AuthProvider
import com.lucablock.digiboxapi.oauth2.user.OAuth2UserInfo
import com.lucablock.digiboxapi.oauth2.user.OAuth2UserInfoFactory
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.repository.UserRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.authentication.InternalAuthenticationServiceException
import org.springframework.security.core.AuthenticationException
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest
import org.springframework.security.oauth2.core.OAuth2AuthenticationException
import org.springframework.security.oauth2.core.user.OAuth2User
import org.springframework.stereotype.Service
import java.util.*

@Service
class CustomOAuth2UserService @Autowired internal constructor(
  private val userRepository: UserRepository,
) : DefaultOAuth2UserService() {

  @Throws(OAuth2AuthenticationException::class)
  override fun loadUser(oAuth2UserRequest: OAuth2UserRequest): OAuth2User {
    val oAuth2User = super.loadUser(oAuth2UserRequest)
    return try {
      processOAuth2User(oAuth2UserRequest, oAuth2User, null)
    } catch (ex: AuthenticationException) {
      throw InternalServerException(ex.message)
    } catch (ex: Exception) {
      // Throwing an instance of AuthenticationException will trigger the OAuth2AuthenticationFailureHandler
      throw InternalAuthenticationServiceException(ex.message, ex.cause)
    }
  }

  @Throws(OAuth2AuthenticationException::class)
  fun loadOidcUser(oAuth2UserRequest: OAuth2UserRequest, email: String?): OAuth2User {
    val oAuth2User = super.loadUser(oAuth2UserRequest)
    return try {
      processOAuth2User(oAuth2UserRequest, oAuth2User, email)
    } catch (ex: AuthenticationException) {
      throw InternalServerException(ex.message)
    } catch (ex: Exception) {
      // Throwing an instance of AuthenticationException will trigger the OAuth2AuthenticationFailureHandler
      throw InternalAuthenticationServiceException(ex.message, ex.cause)
    }
  }

  private fun processOAuth2User(
    oAuth2UserRequest: OAuth2UserRequest,
    oAuth2User: OAuth2User,
    email: String?
  ): UserPrincipal {
    val oAuth2UserInfo = OAuth2UserInfoFactory.getOAuth2UserInfo(
      oAuth2UserRequest.clientRegistration.registrationId,
      oAuth2User.attributes,
      email
    )

    if (oAuth2UserInfo.email.isNullOrEmpty()) {
      throw OAuth2AuthenticationProcessingException("Email not found from OAuth2 provider")
    }

    var user: User
    var userOptional = Optional.empty<User>()
    if (oAuth2UserRequest.clientRegistration.registrationId.equals(
        AuthProvider.line.name,
        ignoreCase = true
      )
    ) {
      userOptional =
        userRepository.findByEmailContainsIgnoreCase(oAuth2UserInfo.id.toString())
      // case user already registered with line but don't have email
      if (userOptional.isPresent) {
        user = userOptional.get()
        user.email = oAuth2UserInfo.email.toString()
        user.providerId = oAuth2UserInfo.id
        userRepository.save(user)
      } else {
        // case user already registered but don't have line_provider_id
        userOptional = userRepository.findByEmail(oAuth2UserInfo.email.toString())
        if (userOptional.isPresent) {
          user = userOptional.get()
          user.providerId = oAuth2UserInfo.id
          userRepository.save(user)
        }
      }
    }
    userOptional = userRepository.findByEmailContainsIgnoreCase(oAuth2UserInfo.email.toString())
    if (userOptional.isPresent) {
      user = userOptional.get()
      user.provider =
        AuthProvider.valueOf(oAuth2UserRequest.clientRegistration.registrationId).toString()
//      throw OAuth2AuthenticationProcessingException("Looks like you're signed up with " +
//          user.provider + " account. Please use your " + user.provider +
//          " account to login.")
    } else {
      user = registerNewUser(oAuth2UserRequest, oAuth2UserInfo)
    }
    val userPrincipal = UserPrincipal()
    userPrincipal.setUserId(user.id)
    userPrincipal.setUsername(user.email)
    userPrincipal.setPassword(user.password)
    userPrincipal.setUserTypeId(user.userTypeId)
    userPrincipal.email = user.email
    userPrincipal.attributes = oAuth2User.attributes

    val userType = when {
      user.userType == null -> {
        UserRoleEnum.ROLE_USER.name
      }

      user.userType!!.isAdmin -> {
        UserRoleEnum.ROLE_ADMIN.name
      }

      else -> {
        UserRoleEnum.ROLE_USER.name
      }
    }
    userPrincipal.setUserType(userType)
    return userPrincipal
  }

  private fun registerNewUser(
    oAuth2UserRequest: OAuth2UserRequest,
    oAuth2UserInfo: OAuth2UserInfo,
  ): User {

    var firstName: String = ""
    var lastName: String? = null

    firstName = if (oAuth2UserRequest.clientRegistration.registrationId.equals(
        AuthProvider.line.name,
        ignoreCase = true
      )
    ) {
      oAuth2UserInfo.name.toString()
    } else {
      oAuth2UserInfo.name?.split(" ")?.get(0).toString()
    }

    lastName = if (oAuth2UserRequest.clientRegistration.registrationId.equals(
        AuthProvider.line.name,
        ignoreCase = true
      )
    ) {
      null
    } else {
      oAuth2UserInfo.name?.split(" ")?.get(1)
    }

    var user = User(
      email = oAuth2UserInfo.email.toString(),
      fullName = oAuth2UserInfo.name,
      imageUrl = oAuth2UserInfo.imageUrl,
      provider = AuthProvider.valueOf(oAuth2UserRequest.clientRegistration.registrationId)
        .toString(),
      providerId = oAuth2UserInfo.id,
      emailVerified = true,
      userTypeId = UserRoleEnum.ROLE_USER.value,
      firstName = firstName,
      lastName = lastName,
    )
    user = userRepository.save(user)
    return user
  }

  private fun updateExistingUser(existingUser: User, oAuth2UserInfo: OAuth2UserInfo): User {
    existingUser.fullName = oAuth2UserInfo.name
    existingUser.imageUrl = oAuth2UserInfo.imageUrl
    return userRepository.save(existingUser)
  }
}