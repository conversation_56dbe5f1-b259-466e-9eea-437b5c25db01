package com.lucablock.digiboxapi.oauth2

import com.lucablock.digiboxapi.exception.InternalServerException
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.authentication.InternalAuthenticationServiceException
import org.springframework.security.core.AuthenticationException
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest
import org.springframework.security.oauth2.core.OAuth2AuthenticationException
import org.springframework.security.oauth2.core.oidc.user.OidcUser
import org.springframework.stereotype.Service
import java.util.*

@Service
class CustomOidcUserService @Autowired internal constructor(
  private val customOAuth2UserService: CustomOAuth2UserService
) : OidcUserService() {

  @Throws(OAuth2AuthenticationException::class)
  override fun loadUser(userRequest: OidcUserRequest?): OidcUser? {
    val oAuth2UserRequest = OAuth2UserRequest(
      userRequest!!.clientRegistration,
      userRequest.accessToken,
    )
    val email = userRequest.idToken!!.claims["email"] as String
    val oauth2User = this.customOAuth2UserService.loadOidcUser(oAuth2UserRequest, email)
    return try {
      oauth2User as UserPrincipal
    } catch (ex: AuthenticationException) {
      throw InternalServerException(ex.message)
    } catch (ex: Exception) {
      // Throwing an instance of AuthenticationException will trigger the OAuth2AuthenticationFailureHandler
      throw InternalAuthenticationServiceException(ex.message, ex.cause)
    }
  }
}