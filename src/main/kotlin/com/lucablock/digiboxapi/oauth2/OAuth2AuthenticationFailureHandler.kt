package com.lucablock.digiboxapi.oauth2

import com.lucablock.digiboxapi.oauth2.HttpCookieOAuth2AuthorizationRequestRepository.Companion.REDIRECT_URI_PARAM_COOKIE_NAME
import jakarta.servlet.ServletException
import jakarta.servlet.http.Cookie
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler
import org.springframework.stereotype.Component
import org.springframework.web.util.UriComponentsBuilder
import java.io.IOException

@Component
class OAuth2AuthenticationFailureHandler : SimpleUrlAuthenticationFailureHandler() {
  @Autowired
  lateinit var httpCookieOAuth2AuthorizationRequestRepository: HttpCookieOAuth2AuthorizationRequestRepository

  @Throws(IOException::class, ServletException::class)
  override fun onAuthenticationFailure(
      request: HttpServletRequest?,
      response: HttpServletResponse?,
      exception: AuthenticationException
  ) {
    var targetUrl: Any? = CookieUtils.getCookie(request, REDIRECT_URI_PARAM_COOKIE_NAME)
        .map<Any>(Cookie::getValue)
        .orElse("/")
    targetUrl = targetUrl?.let {
      UriComponentsBuilder.fromUriString(it.toString())
          .queryParam("error", exception.localizedMessage)
          .build().toUriString()
    }
    httpCookieOAuth2AuthorizationRequestRepository.removeAuthorizationRequestCookies(
        request,
        response
    )
    redirectStrategy.sendRedirect(request, response, targetUrl)

  }
}