package com.lucablock.digiboxapi.oauth2

import com.nimbusds.oauth2.sdk.util.StringUtils
import jakarta.servlet.http.Cookie
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.oauth2.client.web.AuthorizationRequestRepository
import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest
import org.springframework.stereotype.Component
import java.util.function.Function

@Component
class HttpCookieOAuth2AuthorizationRequestRepository :
  AuthorizationRequestRepository<OAuth2AuthorizationRequest?> {
  override fun loadAuthorizationRequest(request: HttpServletRequest?): OAuth2AuthorizationRequest {
    return CookieUtils.getCookie(request, OAUTH2_AUTHORIZATION_REQUEST_COOKIE_NAME)
      .map<OAuth2AuthorizationRequest>(Function<Cookie, OAuth2AuthorizationRequest> { cookie: Cookie? ->
        CookieUtils.deserialize<OAuth2AuthorizationRequest>(
          cookie!!,
          OAuth2AuthorizationRequest::class.java
        )
      })
      .orElse(null)
  }

  override fun saveAuthorizationRequest(
    authorizationRequest: OAuth2AuthorizationRequest?,
    request: HttpServletRequest,
    response: HttpServletResponse?,
  ) {
    if (authorizationRequest == null) {
      CookieUtils.deleteCookie(request, response, OAUTH2_AUTHORIZATION_REQUEST_COOKIE_NAME)
      CookieUtils.deleteCookie(request, response, REDIRECT_URI_PARAM_COOKIE_NAME)
      CookieUtils.deleteCookie(request, response, AFFILIATE_PARAM_COOKIE_NAME)
      return
    }
    CookieUtils.addCookie(
      response,
      OAUTH2_AUTHORIZATION_REQUEST_COOKIE_NAME,
      CookieUtils.serialize(authorizationRequest),
      cookieExpireSeconds,
    )
    val redirectUriAfterLogin: String? = request.getParameter(REDIRECT_URI_PARAM_COOKIE_NAME)
    if (StringUtils.isNotBlank(redirectUriAfterLogin)) {
      CookieUtils.addCookie(
        response,
        REDIRECT_URI_PARAM_COOKIE_NAME,
        redirectUriAfterLogin,
        cookieExpireSeconds
      )
    }
    val affiliateCode: String? = request.getParameter(AFFILIATE_PARAM_COOKIE_NAME)
    if (StringUtils.isNotBlank(affiliateCode)) {
      CookieUtils.addCookie(
        response,
        AFFILIATE_PARAM_COOKIE_NAME,
        affiliateCode,
        cookieExpireSeconds
      )
    }
  }

  override fun removeAuthorizationRequest(
    request: HttpServletRequest?,
    response: HttpServletResponse?,
  ): OAuth2AuthorizationRequest? {
    return loadAuthorizationRequest(request)
  }

  fun removeAuthorizationRequestCookies(
    request: HttpServletRequest?,
    response: HttpServletResponse?,
  ) {
    CookieUtils.deleteCookie(request, response, OAUTH2_AUTHORIZATION_REQUEST_COOKIE_NAME)
    CookieUtils.deleteCookie(request, response, REDIRECT_URI_PARAM_COOKIE_NAME)
  }

  companion object {
    const val OAUTH2_AUTHORIZATION_REQUEST_COOKIE_NAME = "oauth2_auth_request"
    const val REDIRECT_URI_PARAM_COOKIE_NAME = "redirect_uri"
    private const val cookieExpireSeconds = 180
    const val AFFILIATE_PARAM_COOKIE_NAME = "affId"
  }
}