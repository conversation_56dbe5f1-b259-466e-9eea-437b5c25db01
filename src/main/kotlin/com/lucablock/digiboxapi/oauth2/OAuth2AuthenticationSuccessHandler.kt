package com.lucablock.digiboxapi.oauth2


import com.lucablock.digiboxapi.config.AppProperties
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.oauth2.HttpCookieOAuth2AuthorizationRequestRepository.Companion.REDIRECT_URI_PARAM_COOKIE_NAME
import com.lucablock.digiboxapi.token.JWTTokenProvider
import com.lucablock.digiboxapi.token.dto.TokenDto
import com.lucablock.digiboxapi.token.service.TokenService
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import jakarta.servlet.ServletException
import jakarta.servlet.http.Cookie
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.Authentication
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler
import org.springframework.stereotype.Component
import org.springframework.web.util.UriComponentsBuilder
import java.io.IOException
import java.net.URI
import java.util.*

@Component
class OAuth2AuthenticationSuccessHandler @Autowired internal constructor(
  private val tokenProvider: JWTTokenProvider,
  private val tokenService: TokenService,
  private val appProperties: AppProperties,
  private val httpCookieOAuth2AuthorizationRequestRepository: HttpCookieOAuth2AuthorizationRequestRepository,
) : SimpleUrlAuthenticationSuccessHandler() {

  @Throws(IOException::class, ServletException::class)
  override fun onAuthenticationSuccess(
    request: HttpServletRequest?,
    response: HttpServletResponse,
    authentication: Authentication?,
  ) {
    val targetUrl = determineTargetUrl(request, response, authentication)
    if (response.isCommitted) {
      logger.debug("Response has already been committed. Unable to redirect to $targetUrl")
      return
    }
    clearAuthenticationAttributes(request, response)
    redirectStrategy.sendRedirect(request, response, targetUrl)
  }

  override fun determineTargetUrl(
    request: HttpServletRequest?,
    response: HttpServletResponse?,
    authentication: Authentication?,
  ): String {
    val redirectUri: Optional<String> =
      CookieUtils.getCookie(request, REDIRECT_URI_PARAM_COOKIE_NAME)
        .map(Cookie::getValue)
    if (redirectUri.isPresent && !isAuthorizedRedirectUri(redirectUri.get())) {
      throw BadRequestException("Sorry! We've got an Unauthorized Redirect URI and can't proceed with the authentication")
    }

    val targetUrl = redirectUri.orElse(defaultTargetUrl)

//    val targetUrl = redirectUri.orElse(defaultTargetUrl)
    val token: String? = authentication?.let { tokenProvider.createToken(it) }
    val userPrincipal = authentication?.principal as UserPrincipal

    if (token != null) {
      tokenService.cacheToken(
        TokenDto(
          userId = userPrincipal.getUserId().toString(),
          token = token,
          multipleLogin = true //login ซ้อนพร้อมกัน
        )
      )
    }
    return UriComponentsBuilder.fromUriString(targetUrl)
      .queryParam("token", token)
      .build().toUriString()
  }

  protected fun clearAuthenticationAttributes(
    request: HttpServletRequest?,
    response: HttpServletResponse?,
  ) {
    super.clearAuthenticationAttributes(request)
    httpCookieOAuth2AuthorizationRequestRepository.removeAuthorizationRequestCookies(
      request,
      response
    )
  }

  private fun isAuthorizedRedirectUri(uri: String): Boolean {
    val clientRedirectUri = URI.create(uri)
    return appProperties.oauth2.authorizedRedirectUris
      .stream()
      .anyMatch { authorizedRedirectUri ->
        // Only validate host and port. Let the clients use different paths if they want to
        val authorizedURI = URI.create(authorizedRedirectUri)
        if (authorizedURI.host.equals(clientRedirectUri.host, ignoreCase = true)
          && authorizedURI.port == clientRedirectUri.port
        ) {
          return@anyMatch true
        }
        false
      }
  }
}