package com.lucablock.digiboxapi.oauth2

import jakarta.servlet.http.Cookie
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.util.SerializationUtils
import java.math.BigInteger
import java.security.MessageDigest
import java.util.*

object CookieUtils {
    fun getCookie(request: HttpServletRequest?, name: String?): Optional<Cookie> {
        val cookies: Array<Cookie> = request?.cookies ?: return Optional.empty<Cookie>()
        if (cookies.isNotEmpty()) {
            for (cookie in cookies) {
                if (cookie.name.equals(name)) {
                    return Optional.of<Cookie>(cookie)
                }
            }
        }
        return Optional.empty<Cookie>()
    }

    fun addCookie(response: HttpServletResponse?, name: String?, value: String?, maxAge: Int) {
        val cookie = Cookie(name, value)
        cookie.path = "/"
        cookie.isHttpOnly = true
        cookie.maxAge = maxAge
        response?.addCookie(cookie)
    }

    fun deleteCookie(request: HttpServletRequest?, response: HttpServletResponse?, name: String?) {
        val cookies: Array<Cookie> = request?.cookies ?: return
        if (cookies.isNotEmpty()) {
            for (cookie in cookies) {
                if (cookie.name.equals(name)) {
                    cookie.value = ""
                    cookie.path = "/"
                    cookie.maxAge = 0
                    response?.addCookie(cookie)
                }
            }
        }
    }

    fun serialize(`object`: Any?): String {
        return Base64.getUrlEncoder()
            .encodeToString(SerializationUtils.serialize(`object`))
    }

    fun <T> deserialize(cookie: Cookie, cls: Class<T>): T {
        return cls.cast(
            SerializationUtils.deserialize(
                Base64.getUrlDecoder().decode(cookie.value)
            )
        )
    }
}