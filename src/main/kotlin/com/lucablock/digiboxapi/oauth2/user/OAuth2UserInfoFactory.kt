package com.lucablock.digiboxapi.oauth2.user

import com.lucablock.digiboxapi.exception.OAuth2AuthenticationProcessingException
import com.lucablock.digiboxapi.oauth2.dto.AuthProvider

object OAuth2UserInfoFactory {
  fun getOAuth2UserInfo(
    registrationId: String,
    attributes: Map<String?, Any?>?,
    email: String?
  ): OAuth2UserInfo {
    return if (registrationId.equals(AuthProvider.line.toString(), ignoreCase = true)) {
      LineOAuth2UserInfo(attributes, email)
    } else {
      throw OAuth2AuthenticationProcessingException("Sorry! Login with $registrationId is not supported yet.")
    }
  }
}