package com.lucablock.digiboxapi.oauth2.user

class LineOAuth2UserInfo(attributes: Map<String?, Any?>?, email: String?) :
  OAuth2UserInfo(attributes) {
  override val id: String?
    get() = attributes?.get("userId") as String?
  override val name: String?
    get() = attributes?.get("displayName") as String?
  override val sub: String?
    get() = attributes?.get("sub") as String?

  override var email: String? = email
    get() = field ?: attributes?.get("email") as String?

  override val imageUrl: String?
    get() = attributes?.get("pictureUrl") as String?
}