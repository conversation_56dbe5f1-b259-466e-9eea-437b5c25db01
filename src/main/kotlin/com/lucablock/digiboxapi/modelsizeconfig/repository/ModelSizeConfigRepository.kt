package com.lucablock.digiboxapi.modelsizeconfig.repository

import com.lucablock.digiboxapi.entity.ModelSize
import com.lucablock.digiboxapi.entity.ModelSizeConfig
import com.lucablock.digiboxapi.model.dto.ModelPriceDto
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ModelSizeConfigRepository : JpaRepository<ModelSizeConfig, Long> {
  fun deleteAllByModelSizeIdIn(id:List<Long>)
  fun deleteAllByModelSizeIdIn(modelSizeIds: MutableCollection<Long>)
  fun findAllByModelSizeIn(modelSizes: MutableCollection<ModelSize>): List<ModelSizeConfig>
  fun findByMaterialConfigId(materialConfigId: Long): MutableList<ModelSizeConfig>
  fun existsModelSizeConfigByModelSizeIdAndMaterialConfigId(modelSizeId: Long, materialConfigId: Long): Boolean
  fun findAllByModelSizeId(modelSizeId: Long): MutableList<ModelSizeConfig>
  fun deleteAllByModelSizeIdAndMaterialConfigIdIn(modelSizeId: Long, materialConfigIds: MutableCollection<Long>)
  fun findAllByModelSizeIdIsInAndMaterialConfig_IsActiveTrue(modelSizeIds: List<Long>): MutableList<ModelSizeConfig>
//  fun findAllByPrintingId(printingId: Long): MutableList<ModelSizeConfig>
}