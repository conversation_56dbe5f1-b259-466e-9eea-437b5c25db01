package com.lucablock.digiboxapi.modelsizeconfig.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.model.request.ModelPriceRequest
import com.lucablock.digiboxapi.modelsizeconfig.request.ModelSizeConfigRequest
import com.lucablock.digiboxapi.modelsizeconfig.service.ModelSizeConfigService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class ModelSizeConfigController {
  private val logger: Logger = LoggerFactory.getLogger(ModelSizeConfigController::class.java)
  @Autowired lateinit var modelSizeConfigService: ModelSizeConfigService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/model-size-config/{modelSizeId}")
  fun createModelSizeConfig(
    @PathVariable modelSizeId : Long,
    @Valid @RequestBody modelSizeConfigRequest: List<ModelSizeConfigRequest>,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เพิ่มวัสดุสำหรับโมเดลสำเร็จ",
          modelSizeConfigService.createModelSizeConfig(modelSizeId,modelSizeConfigRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("create model error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่มีโมเดลนี้"))
    } catch (e: BadRequestException) {
      logger.error("create model error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่โมเดลนี้อยู่แล้ว"))
    } catch (e: Exception) {
      logger.error("create model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/model-size-config/{modelSizeId}")
  fun updateModelSizeConfig(
    @PathVariable modelSizeId : Long,
    @Valid @RequestBody modelSizeConfigRequest: List<ModelSizeConfigRequest>,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกวัสดุสำหรับโมเดลสำเร็จ",
          modelSizeConfigService.updateModelSizeConfig(modelSizeId,modelSizeConfigRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีโมเดลนี้"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่โมเดลนี้อยู่แล้ว"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/model-size-config/{modelSizeId}")
  fun findModelSizeConfigByModelSizeId(
    @PathVariable modelSizeId : Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          modelSizeConfigService.getModelSizeConfigByModelSizeId(modelSizeId)
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
  @GetMapping("/web/model-size-config/material/{modelSizeId}")
  fun findModelSizeConfigMaterialsByModelSizeId(
    @PathVariable modelSizeId : Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          modelSizeConfigService.getModelSizeConfigMaterialsByModelSizeId(modelSizeId)
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/model-size-config/{id}")
  fun deleteModelSizeConfigById(
    @PathVariable id : Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบวัสดุสำหรับโมเดลสำเร็จ",
          modelSizeConfigService.deleteModelSizeConfigById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีโมเดลนี้"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่โมเดลนี้อยู่แล้ว"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/model-size-config/printing/{id}")
  fun findPrintingConfigByModelSizeId(
    @PathVariable id : Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          modelSizeConfigService.getPrintingConfigByModelSizeId(id)
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}