package com.lucablock.digiboxapi.modelsizeconfig.request

import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive

class ModelSizeConfigRequest {
//  @NotNull(message = "กรุณาระบุรหัสขนาดโมเดลสินค้า")
//  var modelSizeId: Long = 0

  @NotNull(message = "กรุณาระบุรหัสวัสดุต่อแกรม")
  var materialConfigId: Long = 0

  @Valid
  var printingConfig : List<PrintingConfigRequest> = mutableListOf()

//  @NotEmpty(message = "กรุณาระบุรายการโมเดล")
//  @Valid
//  var modelSizeConfigDetail : List<ModelSizeConfigDetailRequest> = mutableListOf()
//
//  @NotEmpty(message = "กรุณาระบุรายการวัสดุโมเดล")
//  @Valid
//  var modelSizeConfigCoating : List<ModelSizeConfigCoatingRequest> = mutableListOf()
}

class ModelSizeConfigDetailRequest{
  @NotNull(message = "กรุณาระบุจำนวนโมเดล")
  @Positive(message = "กรุณาระบุจำนวนโมเดลมากกว่า 0")
  var amount : Int = 0

  @NotNull(message = "กรุณาระบุราคาต่อโมเดล")
  @Positive(message = "กรุณาระบุราคาต่อโมเดลมากกว่า 0")
  var price : Double = 0.0

  @NotNull(message = "กรุณาระบุระยะเวลาการผลิต")
  @Positive(message = "กรุณาระบุระยะเวลาการผลิตมากกว่า 0")
  var period : Int = 0

  var modelSizeConfigCoating : List<Int> = mutableListOf()
}

class ModelSizeConfigCoatingRequest{
  @NotNull(message = "กรุณาระบุรหัสวัสดุ")
  var coatingId : Long = 0
}

class PrintingConfigRequest {
  @NotNull(message = "กรุณาระบุรหัสการพิมพ์")
  var printingId : Long = 0
  @NotNull(message = "กรุณาระบุรหัสขนาดโมเดลสินค้า")
  var modelSizeConfigId : Long = 0
}