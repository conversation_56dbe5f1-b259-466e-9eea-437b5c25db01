package com.lucablock.digiboxapi.modelsizeconfig.dto

import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigDto
import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigListDto
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeListDto
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigDto
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigListDto
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigModelSizeDto
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable

data class ModelSizeConfigDto
@QueryProjection
constructor(
  val id: Long,
  var modelSizeId: Long,
  var materialConfigDto: MaterialConfigDto?,
  var printingConfig: List<PrintingConfigDto> = mutableListOf(),
//  var modelSizeConfigDetail : List<ModelSizeConfigDetailDto>,
//  var modelSizeConfigCoating : List<ModelSizeConfigCoatingDto>
) : Serializable

data class ModelSizeConfigListDto
@QueryProjection
constructor(
  val id: Long,
  var modelSizeId: Long,
  var materialConfigId: Long,
) : Serializable

data class ModelSizeConfigMaterialDto
@QueryProjection constructor(
  val id: Long,
  var modelSize: ModelSizeListDto?,
  var materialConfig: MaterialConfigListDto?
)

data class ModelSizeConfigListPrintDto
@QueryProjection
constructor(
  val id: Long,
//  var modelSizeId: Long,
  var materialConfig: MaterialConfigDto?,
  var printingConfig: List<PrintingConfigListDto> = mutableListOf(),
//  var modelSizeConfigDetail : List<ModelSizeConfigDetailDto>,
//  var modelSizeConfigCoating : List<ModelSizeConfigCoatingDto>
) : Serializable

data class ModelSizeConfigPrintingDto
@QueryProjection constructor(
  val id: Long,
  var modelSize: ModelSizeListDto?,
  var materialConfig: MaterialConfigListDto?,
  var printingConfig: List<PrintingConfigModelSizeDto> = mutableListOf(),
)

