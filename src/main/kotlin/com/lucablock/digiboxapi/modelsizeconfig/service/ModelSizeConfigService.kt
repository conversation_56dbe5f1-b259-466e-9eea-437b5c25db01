package com.lucablock.digiboxapi.modelsizeconfig.service

import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigMaterialDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigPrintingDto
import com.lucablock.digiboxapi.modelsizeconfig.request.ModelSizeConfigRequest

interface ModelSizeConfigService {
  fun createModelSizeConfig(modelSizeId : Long ,modelSizeConfigRequest: List<ModelSizeConfigRequest>):List<ModelSizeConfigDto>
  fun updateModelSizeConfig(modelSizeId : Long ,modelSizeConfigRequest: List<ModelSizeConfigRequest>):List<ModelSizeConfigDto>
  fun getModelSizeConfigByModelSizeId(modelSizeId : Long):List<ModelSizeConfigDto>
  fun getModelSizeConfigMaterialsByModelSizeId(modelSizeId : Long):List<ModelSizeConfigMaterialDto>
  fun deleteModelSizeConfigById(id:Long):Boolean
  fun getPrintingConfigByModelSizeId(id:Long): ModelSizeConfigPrintingDto
}