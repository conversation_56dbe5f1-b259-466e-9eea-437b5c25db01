package com.lucablock.digiboxapi.modelsizeconfig.service.impl

import com.lucablock.digiboxapi.ModelSizeConfigCoating.repository.ModelSizeConfigCoatingRepository
import com.lucablock.digiboxapi.entity.ModelSizeConfig
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.materialconfig.repository.MaterialConfigRepository
import com.lucablock.digiboxapi.modelsize.repository.ModelSizeRepository
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigMaterialDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigPrintingDto
import com.lucablock.digiboxapi.modelsizeconfig.repository.ModelSizeConfigRepository
import com.lucablock.digiboxapi.modelsizeconfig.request.ModelSizeConfigRequest
import com.lucablock.digiboxapi.modelsizeconfig.service.ModelSizeConfigService
import com.lucablock.digiboxapi.modelsizeconfigdetail.repository.ModelSizeConfigDetailRepository
import com.lucablock.digiboxapi.printing.repository.PrintingRepository
import com.lucablock.digiboxapi.printingconfig.repository.PrintingConfigRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ModelSizeConfigServiceImpl @Autowired constructor(
  private val modelSizeConfigRepository: ModelSizeConfigRepository,
  private val modelSizeRepository: ModelSizeRepository,
  private val materialConfigRepository: MaterialConfigRepository,
  private val printingRepository: PrintingRepository,
  private val printingConfigRepository: PrintingConfigRepository,
  private val modelSizeConfigDetailRepository: ModelSizeConfigDetailRepository,
  private val modelSizeConfigCoatingRepository: ModelSizeConfigCoatingRepository
): ModelSizeConfigService {
  @Transactional
  override fun createModelSizeConfig(modelSizeId : Long ,modelSizeConfigRequest: List<ModelSizeConfigRequest>): List<ModelSizeConfigDto> {
    val modelSize = modelSizeRepository.findById(modelSizeId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดล")
    }
    if(modelSizeConfigRequest.size == 0 || modelSizeConfigRequest.isEmpty()){
      throw BadRequestException("กรุณาเลือกวัสดุ")
    }
    val materialConfig = modelSizeConfigRequest.map { it.materialConfigId }.toSet()
    val material = materialConfigRepository.findAllById(materialConfig)
    val materialId = material.map { it.id }.toSet()
    val missingIds = materialConfig - materialId
    if (missingIds.isNotEmpty()) {
      throw NotFoundException("ไม่พบข้อมูลวัสดุต่อแกรม")
//      throw NotFoundException("ไม่พบข้อมูลวัสดุต่อแกรมที่ id: ${missingIds.joinToString(", ")}")
    }
    val existing = modelSizeConfigRepository.findAllByModelSizeId(modelSize.id)
      .map { it.materialConfigId }.toSet()
    val duplicated = materialId.intersect(existing)
    if (duplicated.isNotEmpty()) {
      throw BadRequestException("ขนาดโมเดลมีนี้วัสดุนี้อยู่แล้ว")
    }
//
//    modelSizeConfigRepository.existsModelSizeConfigByModelSizeIdAndMaterialConfigId(modelSize.id,materialConfig.id).let {
//      if(it){
//        throw BadRequestException("ขนาดโมเดลมีวัสดุนี้อยู่แล้ว")
//      }
//    }
    val modelSizeConfigs = material.map {
      ModelSizeConfig(
        modelSizeId = modelSize.id,
        materialConfigId = it.id,
      )
    }
    val savedModelSizeConfig = modelSizeConfigRepository.saveAll(modelSizeConfigs)
    return savedModelSizeConfig.map { it.toModelSizeConfigDto() }
  }

  @Transactional
  override fun updateModelSizeConfig(
    modelSizeId: Long,
    modelSizeConfigRequest: List<ModelSizeConfigRequest>
  ): List<ModelSizeConfigDto> {
    if (modelSizeConfigRequest.isEmpty()) {
      throw BadRequestException("กรุณาเลือกวัสดุ")
    }
    val modelSize = modelSizeRepository.findById(modelSizeId).orElseThrow {
      NotFoundException("ไม่พบข้อมูลขนาดโมเดลสินค้านี้")
    }
    val materials = modelSizeConfigRequest.map { it.materialConfigId }.toSet()

    val existingConfigs = modelSizeConfigRepository.findAllByModelSizeId(modelSizeId)
    val existingMaterialIds = existingConfigs.map { it.materialConfigId }.toSet()
    val toAdd = materials - existingMaterialIds
    val toDelete = (existingMaterialIds - materials).toMutableSet()

    if (toDelete.isNotEmpty()) {
      val prints = printingConfigRepository.findAllByModelSizeConfigId(modelSizeId).toMutableSet()
      val modelSizeConfig = modelSizeConfigRepository.findAllByModelSizeId(modelSizeId)
      val details = modelSizeConfigDetailRepository.findAllByPrintingConfigIdIn(prints.map { it.id })
      modelSizeConfigDetailRepository.deleteAllByPrintingConfigIdIn(prints.map { it.id })
      modelSizeConfigCoatingRepository.deleteAllByModelSizeConfigDetailIdIn(details.map { it.id })
      printingConfigRepository.deleteAllPrintingConfigsByModelSizeConfigIdIn(modelSizeConfig.map { it.id })
      modelSizeConfigRepository.deleteAllByModelSizeIdAndMaterialConfigIdIn(modelSizeId, toDelete)
    }
    val materialConfigs = materialConfigRepository.findAllById(toAdd)
    val material = materialConfigs.map { it.id }.toSet()
    val missingId = toAdd - material
    if (missingId.isNotEmpty()) {
      throw NotFoundException("ไม่พบข้อมูลวัสดุที่ id: ${missingId.joinToString()}")
    }

    val modelSizeConfig = materialConfigs.map {
      ModelSizeConfig(
        modelSizeId = modelSizeId,
        materialConfigId = it.id
      )
    }
    val savedConfigs = modelSizeConfigRepository.saveAll(modelSizeConfig)
    val updatedConfigs = (existingConfigs.filterNot { toDelete.contains(it.materialConfigId) } + savedConfigs)
    return updatedConfigs.map { it.toModelSizeConfigDto() }
  }

  override fun getModelSizeConfigByModelSizeId(modelSizeId: Long): List<ModelSizeConfigDto> {
    val modelSizeConfig = modelSizeConfigRepository.findAllByModelSizeId(modelSizeId)
    return modelSizeConfig.map { it.toModelSizeConfigDto() }
  }

  override fun getModelSizeConfigMaterialsByModelSizeId(modelSizeId: Long): List<ModelSizeConfigMaterialDto> {
    val modelSizeConfig = modelSizeConfigRepository.findAllByModelSizeId(modelSizeId)
    return modelSizeConfig.map {
      ModelSizeConfigMaterialDto(
        id = it.id,
        modelSize = it.modelSize?.toModelSizeDto(),
        materialConfig = it.materialConfig?.toMaterialConfigListDto()
      )
    }
  }

  override fun deleteModelSizeConfigById(id: Long): Boolean {
    val modelSizeConfig = modelSizeConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดล")
    }
    val printingConfig = printingConfigRepository.findAllByModelSizeConfigId(modelSizeConfig.id)
    val configDetail = modelSizeConfigDetailRepository.findAllByPrintingConfigIdIn(printingConfig.map { it.id })
    val modelCoating = modelSizeConfigCoatingRepository.findAllByModelSizeConfigDetailIdIn(configDetail.map { it.id })
    modelSizeConfigCoatingRepository.deleteAllById(modelCoating.map { it.id })
    modelSizeConfigDetailRepository.deleteAllById(configDetail.map { it.id })
    printingConfigRepository.deleteAllById(printingConfig.map { it.id })
    modelSizeConfigRepository.deleteById(modelSizeConfig.id)
    return true
  }

  override fun getPrintingConfigByModelSizeId(id: Long): ModelSizeConfigPrintingDto {
    val modelSizeConfig = modelSizeConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดล")
    }
    return modelSizeConfig.toModelSizeConfigPrintingDto()
  }
}