package com.lucablock.digiboxapi.productspecialtechnic.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class ProductSpecialTechnicDto
@QueryProjection
constructor(
  val id: Long,
  val productId: Long,
  val specialTechnicId: Int,
  val specialTechnicName:String?,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
)
