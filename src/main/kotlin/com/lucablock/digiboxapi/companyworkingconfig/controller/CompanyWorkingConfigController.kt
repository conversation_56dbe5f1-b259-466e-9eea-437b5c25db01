package com.lucablock.digiboxapi.companyworkingconfig.controller

import com.lucablock.digiboxapi.company.controller.CompanyController
import com.lucablock.digiboxapi.company.request.CompanyRequest
import com.lucablock.digiboxapi.companyworkingconfig.request.CompanyWorkingConfigRequest
import com.lucablock.digiboxapi.companyworkingconfig.service.CompanyWorkingConfigService
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api/company-working-config")
class CompanyWorkingConfigController @Autowired internal constructor(
  private val companyWorkingConfigService: CompanyWorkingConfigService
){
  private val logger: Logger = LoggerFactory.getLogger(CompanyController::class.java)

  @PreAuthorize("hasAnyRole('USER','ADMIN','SUPER_ADMIN')")
  @PutMapping()
  fun updateCompanyWorkingConfig(
    @Valid @RequestBody request: List<CompanyWorkingConfigRequest>
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลวันทำงานสำเร็จ",
          companyWorkingConfigService.updateCompanyWorkingConfig(request)
        )
      )
    }catch (e: NotFoundException){
      logger.error(e.message)
      ResponseEntity.badRequest().body(HttpResponse(false, e.message?: "ไม่พบข้อมูลวันทำงาน"))
    }catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลวันทำงาน กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @GetMapping("/{id}")
  fun findCompanyWorkingById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = companyWorkingConfigService.getCompanyWorkingConfigById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวันทำงาน"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/company/{id}")
  fun findCompanyWorkingByCompanyId(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลวันทำงานโดยรหัสบริษัทสำเร็จ",
          data = companyWorkingConfigService.getCompanyWorkingConfigByCompanyId(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวันทำงาน"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping()
  fun findAllCompanyWorkingConfig(): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = companyWorkingConfigService.getAllCompanyWorkingConfig()
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}