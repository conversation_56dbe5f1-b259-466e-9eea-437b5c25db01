package com.lucablock.digiboxapi.companyworkingconfig.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive

class CompanyWorkingConfigRequest {
  @NotNull(message = "กรุณาเลือกรหัสวันทำงาน")
  val id : Long = 0
  @NotNull(message = "กรุณาเลือกสถานะวันทำงาน")
  @JsonProperty("isActive")
  val isActive : Boolean = true
}