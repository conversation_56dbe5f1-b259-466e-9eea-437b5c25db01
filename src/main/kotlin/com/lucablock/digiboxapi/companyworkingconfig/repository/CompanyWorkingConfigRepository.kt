package com.lucablock.digiboxapi.companyworkingconfig.repository

import com.lucablock.digiboxapi.entity.CompanyWorkingConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CompanyWorkingConfigRepository : JpaRepository<CompanyWorkingConfig, Long>{
  fun findCompanyWorkingConfigByCompanyId(companyId: Long): MutableList<CompanyWorkingConfig>
  fun findCompanyWorkingConfigByCompanyIdOrderByCompanyWorkingId(companyId: Long): MutableList<CompanyWorkingConfig>
  fun findAllOrderBy(): MutableList<CompanyWorkingConfig>
  fun findAllByOrderByIdAsc(): MutableList<CompanyWorkingConfig>
  fun findAllCompanyWorkingConfigByCompanyIdAndIsActiveFalse(companyId: Long): MutableList<CompanyWorkingConfig>
  fun findAllCompanyWorkingConfigByCompanyIdAndIsActiveTrue(companyId: Long): MutableList<CompanyWorkingConfig>
}