package com.lucablock.digiboxapi.companyworkingconfig.service.impl

import com.lucablock.digiboxapi.company.repository.CompanyRepository
import com.lucablock.digiboxapi.companyworking.dto.CompanyWorkingDto
import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyWorkingConfigDto
import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyWorkingConfigListDto
import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyWorkingListDto
import com.lucablock.digiboxapi.companyworkingconfig.repository.CompanyWorkingConfigRepository
import com.lucablock.digiboxapi.companyworkingconfig.request.CompanyWorkingConfigRequest
import com.lucablock.digiboxapi.companyworkingconfig.service.CompanyWorkingConfigService
import com.lucablock.digiboxapi.entity.CompanyWorkingConfig
import com.lucablock.digiboxapi.exception.NotFoundException
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CompanyWorkingConfigServiceImpl @Autowired constructor(
  private val companyWorkingConfigRepository: CompanyWorkingConfigRepository,
  private val companyRepository: CompanyRepository
):CompanyWorkingConfigService{
  @Transactional
  override fun updateCompanyWorkingConfig(request: List<CompanyWorkingConfigRequest>): List<CompanyWorkingConfigDto> {
    val ids = request.map { it.id }
    val existingConfigs = companyWorkingConfigRepository.findAllById(ids)
    val existingIds = existingConfigs.map { it.id }.toSet()
    val missingIds = ids.filterNot { it in existingIds }
    if (missingIds.isNotEmpty()) {
      throw NotFoundException("ไม่พบวันทำงาน id: $missingIds")
    }
    val companyConfig = companyWorkingConfigRepository.findAllById(request.map { it.id})
    val companyConfigMap = companyConfig.associateBy { it.id }
    val updateConfigs = request.map { req ->
      val companyWorkingConfig = companyConfigMap[req.id]
        ?: throw NotFoundException("ไม่พบข้อมูล CompanyWorkingConfig")

      CompanyWorkingConfig(
        id = req.id,
        isActive = req.isActive,
        companyWorkingId = companyWorkingConfig.companyWorkingId,
        companyId = companyWorkingConfig.companyId,
        companyWorking = companyWorkingConfig.companyWorking,
        company = companyWorkingConfig.company,
      )
    }
    val newConfigs = companyWorkingConfigRepository.saveAll(updateConfigs)
    return newConfigs.map { it.toCompanyWorkingConfigDto() }
  }

  override fun getAllCompanyWorkingConfig(): List<CompanyWorkingConfigDto> {
    return companyWorkingConfigRepository.findAllByOrderByIdAsc().map { it.toCompanyWorkingConfigDto() }
  }

  override fun getCompanyWorkingConfigById(id: Long): CompanyWorkingConfigDto {
    val working = companyWorkingConfigRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันทำงาน")
    }
    return working.toCompanyWorkingConfigDto()
  }

  override fun getCompanyWorkingConfigByCompanyId(companyId: Long): CompanyWorkingConfigListDto {
    val company = companyRepository.findById(companyId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลบริษัท")
    }
    val working = companyWorkingConfigRepository.findCompanyWorkingConfigByCompanyIdOrderByCompanyWorkingId(company.id)
    val workingPerWeek = companyWorkingConfigRepository.findAllCompanyWorkingConfigByCompanyIdAndIsActiveTrue(companyId)
    return CompanyWorkingConfigListDto(
      company = company.toCompanyListDto(),
      workingPerWeek = workingPerWeek.size,
      companyWorking = working.map { CompanyWorkingListDto(
        id = it.companyWorking!!.id,
        day = it.companyWorking.day,
        sort = it.companyWorking.sort,
        isActive = it.isActive
      )  }
    )
  }
}