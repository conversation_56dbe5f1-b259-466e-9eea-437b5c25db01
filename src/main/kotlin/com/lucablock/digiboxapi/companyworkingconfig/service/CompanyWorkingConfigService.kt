package com.lucablock.digiboxapi.companyworkingconfig.service

import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyWorkingConfigDto
import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyWorkingConfigListDto
import com.lucablock.digiboxapi.companyworkingconfig.request.CompanyWorkingConfigRequest

interface CompanyWorkingConfigService {
  fun updateCompanyWorkingConfig(request : List<CompanyWorkingConfigRequest>):List<CompanyWorkingConfigDto>
  fun getAllCompanyWorkingConfig():List<CompanyWorkingConfigDto>
  fun getCompanyWorkingConfigById(id:Long):CompanyWorkingConfigDto
  fun getCompanyWorkingConfigByCompanyId(companyId : Long): CompanyWorkingConfigListDto
}