package com.lucablock.digiboxapi.companyworkingconfig.dto

import com.lucablock.digiboxapi.companyworking.dto.CompanyWorkingDto
import com.querydsl.core.annotations.QueryProjection

class CompanyWorkingConfigDto
@QueryProjection constructor(
  val id : Long,
  val isActive : Boolean,
  val companyWorking : CompanyWorkingDto,
  val company : CompanyListDto,
)
class CompanyListDto
@QueryProjection constructor(
  val id : Long,
  val name : String
)

class CompanyWorkingConfigListDto
@QueryProjection constructor(
  val company : CompanyListDto,
  val workingPerWeek : Int,
  val companyWorking : List<CompanyWorkingListDto>,
)
class CompanyWorkingListDto
@QueryProjection constructor(
  val id : Long,
  val day : String,
  val sort : Int,
  val isActive: Boolean
)
