package com.lucablock.digiboxapi.producttag.service

import com.lucablock.digiboxapi.producttag.dto.ProductTagDto
import com.lucablock.digiboxapi.producttag.request.ProductTagRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface ProductTagService {
  fun createProductTag(productTagRequest: ProductTagRequest): ProductTagDto
  fun updateProductTag(id : Long, productTagRequest: ProductTagRequest): ProductTagDto
  fun findProductTagById(id: Long): ProductTagDto
  fun findPageProductTag(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?,
  ): Page<ProductTagDto>
  fun deleteProductTag(id: Long): Boolean
}