package com.lucablock.digiboxapi.producttag.service.impl

import com.lucablock.digiboxapi.entity.QProductCategory
import com.lucablock.digiboxapi.entity.QProductTag
import com.lucablock.digiboxapi.producttag.dto.ProductTagDto
import com.lucablock.digiboxapi.producttag.repository.ProductTagRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProductTagRepositoryImpl : ProductTagRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }
  private val qProductTag = QProductTag.productTag

  override fun findPageProductTag(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<ProductTagDto> {
    var criteria = qProductTag.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qProductTag.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qProductTag.name.containsIgnoreCase(search))
    }

    val sort = if (ascending) {
      qProductTag.id.asc()
    } else {
      qProductTag.id.desc()
    }

    val query = queryFactory
      .select(qProductTag)
      .from(qProductTag)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toProductTagDto()
      }

    val total = queryFactory
      .select(qProductTag)
      .from(qProductTag)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}