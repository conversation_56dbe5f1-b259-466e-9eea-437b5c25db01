package com.lucablock.digiboxapi.producttag.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.ProductTag
import com.lucablock.digiboxapi.producttag.dto.ProductTagDto
import com.lucablock.digiboxapi.producttag.repository.ProductTagRepository
import com.lucablock.digiboxapi.producttag.request.ProductTagRequest
import com.lucablock.digiboxapi.producttag.service.ProductTagService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProductTagServiceImpl @Autowired constructor(
  private val productTagRepository: ProductTagRepository,
  private val s3Service: S3Service
) : ProductTagService {
  @Transactional
  override fun createProductTag(productTagRequest: ProductTagRequest): ProductTagDto {
    productTagRepository.existsByNameIgnoreCaseAndIsDeletedFalse(productTagRequest.name).let {
      if (it) {
        throw Exception("ชื่อของแท็กซ้ำ")
      }
    }
    val productTag = ProductTag(
        name = productTagRequest.name,
        imageUrl = productTagRequest.imageUrl,
      )

    val urlFile = s3Service.moveFile(productTagRequest.imageUrl).url
    productTag.imageUrl = urlFile
    val savedProductTag = productTagRepository.save(productTag)

    return savedProductTag.toProductTagDto()
  }

  override fun updateProductTag(id: Long, productTagRequest: ProductTagRequest): ProductTagDto {
    val productTag = productTagRepository.findById(id).orElseThrow {
      throw Exception("ไม่พบข้อมูลแท็ก")
    }
      productTagRepository.existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(productTagRequest.name,id).let {
        if (it) {
          throw Exception("ชื่อของแท็กซ้ำ")
        }
    }
    productTag.name = productTagRequest.name
    productTag.isActive = productTagRequest.isActive

    if (productTagRequest.imageUrl != productTag.imageUrl) {
      s3Service.deleteFile(productTag.imageUrl)
      val newFileUrl = s3Service.moveFile(productTagRequest.imageUrl).url
      productTag.imageUrl = newFileUrl
    }

    val updateProductTag = productTagRepository.save(productTag)
    return updateProductTag.toProductTagDto()
  }

  override fun findProductTagById(id: Long): ProductTagDto {
    val productTag = productTagRepository.findById(id).orElseThrow {
      throw Exception("ไม่พบข้อมูลแท็ก")
    }
    return productTag.toProductTagDto()
  }


  override fun findPageProductTag(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<ProductTagDto> {
    return productTagRepository.findPageProductTag(
      pageable, ascending, search, isActive
    )
  }

  override fun deleteProductTag(id: Long): Boolean {
    val productTag = productTagRepository.findById(id).orElseThrow{
      throw Exception("ไม่พบข้อมูลแท็ก")
    }
    productTag.isDeleted = true
    productTagRepository.save(productTag)
    return true
  }
}