package com.lucablock.digiboxapi.producttag.repository

import com.lucablock.digiboxapi.entity.ProductTag
import org.springframework.data.jpa.repository.JpaRepository

interface ProductTagRepository : JpaRepository<ProductTag, Long> , ProductTagRepositoryCustom
{
  fun existsByNameIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  fun existsByNameIgnoreCaseAndIdNot(name: String,id:Long): Boolean
  fun existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String, id: Long): Boolean
}