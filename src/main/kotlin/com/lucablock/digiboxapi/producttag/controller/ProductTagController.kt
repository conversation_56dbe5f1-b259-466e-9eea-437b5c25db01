package com.lucablock.digiboxapi.producttag.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.productCategory.controller.ProductCategoryController
import com.lucablock.digiboxapi.productCategory.request.ProductCategoryRequest
import com.lucablock.digiboxapi.productCategory.service.ProductCategoryService
import com.lucablock.digiboxapi.producttag.request.ProductTagRequest
import com.lucablock.digiboxapi.producttag.service.ProductTagService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class ProductTagController @Autowired internal constructor(
  private val productTagService: ProductTagService
) {
  private val logger: Logger = LoggerFactory.getLogger(ProductTagController::class.java)
  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/product-tag")
  fun createProductTag(
    @Valid @RequestBody productTagRequest: ProductTagRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างข้อมูลแท็กสำเร็จ",
          data = productTagService.createProductTag(productTagRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลแท็กซ้ำ"))
    } catch (e: Exception) {
      logger.error("create product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลแท็ก กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/product-tag/{id}")
  fun updateProductTag(
    @PathVariable id: Long,
    @Valid @RequestBody productTagRequest: ProductTagRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "บันทึกข้อมูลแท็กสำเร็จ",
          data = productTagService.updateProductTag(id, productTagRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลแท็ก"))
    } catch (e: BadRequestException) {
      logger.error("update product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลแท็กซ้ำ"))
    } catch (e: Exception) {
      logger.error("update product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลแท็ก กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-tag/page")
  fun findAllProductTag(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = productTagService.findPageProductTag(
            pageable,
            ascending,
            search,
            isActive
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-tag/{id}")
  fun findProductTagById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = productTagService.findProductTagById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find product tag by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลแท็ก"))
    } catch (e: Exception) {
      logger.error("find product tag by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/product-tag/{id}")
  fun deleteProductTag(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบข้อมูลสำเร็จ",
          data = productTagService.deleteProductTag(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลแท็ก"))
    } catch (e: BadRequestException) {
      logger.error("delete product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ มีการใช้งานอยู่"))
    } catch (e: Exception) {
      logger.error("delete product tag error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}
