package com.lucablock.digiboxapi.companyholiday.repository

import com.lucablock.digiboxapi.entity.CompanyHoliday
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface CompanyHolidayRepository : JpaRepository<CompanyHoliday, Long>{
  fun existsCompanyHolidayByHolidayDateAndCompanyId(holidayDate: Date, companyId: Long): Boolean
  fun findByCompanyId(companyId: Long): MutableList<CompanyHoliday>
}