package com.lucablock.digiboxapi.companyholiday.controller

import com.lucablock.digiboxapi.companyholiday.request.CompanyHolidayRequest
import com.lucablock.digiboxapi.companyholiday.service.CompanyHolidayService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api/company-holiday")
class CompanyHolidayController @Autowired internal constructor(
  private val companyHolidayService: CompanyHolidayService
){
  private val logger: Logger = LoggerFactory.getLogger(CompanyHolidayController::class.java)
  @PreAuthorize("hasAnyRole('USER','ADMIN','SUPER_ADMIN')")
  @PostMapping("/{companyId}")
  fun createHoliday(
    @Valid @RequestBody companyHolidayRequest: CompanyHolidayRequest,
    @PathVariable companyId: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างข้อมูลวันหยุดสำเร็จ",
          data = companyHolidayService.createCompanyHoliday(companyHolidayRequest, companyId)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลวันหยุดซ้ำ"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลวันหยุด กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('USER','ADMIN','SUPER_ADMIN')")
  @PutMapping("/update/{id}")
  fun updateHoliday(
    @Valid @RequestBody companyHolidayRequest: CompanyHolidayRequest,
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "บันทึกข้อมูลวันหยุดสำเร็จ",
          data = companyHolidayService.updateCompanyHoliday(id,companyHolidayRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลวันหยุดซ้ำ"))
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวันหยุด"))
    }catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลวันหยุด กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/{id}")
  fun findHolidayById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลวันหยุดสำเร็จ",
          data = companyHolidayService.getCompanyHolidayById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวันหยุด"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลวันหยุดได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping()
  fun findAllHoliday(): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลวันหยุดสำเร็จ",
          data = companyHolidayService.getAllCompanyHoliday()
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลวันหยุดได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("company/{id}")
  fun findAllByCompany(
    @PathVariable id : Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลวันหยุดสำเร็จ",
          data = companyHolidayService.getCompanyHolidayByCompanyId(id)
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลวันหยุดได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('USER','ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/{id}")
  fun deleteHoliday(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบข้อมูลวันหยุดสำเร็จ",
          data = companyHolidayService.deleteCompanyHoliday(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลวันหยุด"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลวันหยุดได้ มีการใช้งานอยู่"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลวันหยุด กรุณาลองอีกครั้ง"))
    }
  }
}