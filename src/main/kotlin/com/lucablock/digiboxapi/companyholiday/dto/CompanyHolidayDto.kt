package com.lucablock.digiboxapi.companyholiday.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.company.dto.CompanyDto
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class CompanyHolidayDto
@QueryProjection constructor(
  val id : Long,
  val name : String,
  val description : String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val holidayDate : Date = Date(),
  val company : CompanyListDto?
)

data class CompanyListDto
@QueryProjection constructor(
  val id : Long,
  val name : String,
)