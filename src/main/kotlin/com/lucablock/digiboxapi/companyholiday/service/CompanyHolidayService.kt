package com.lucablock.digiboxapi.companyholiday.service

import com.lucablock.digiboxapi.companyholiday.dto.CompanyHolidayDto
import com.lucablock.digiboxapi.companyholiday.request.CompanyHolidayRequest

interface CompanyHolidayService {
  fun createCompanyHoliday(
    companyHolidayRequest: CompanyHolidayRequest,
    companyId : Long
  ):CompanyHolidayDto
  fun updateCompanyHoliday(
    id:Long,
    companyHolidayRequest: CompanyHolidayRequest
  ):CompanyHolidayDto
  fun getAllCompanyHoliday():List<CompanyHolidayDto>
  fun getCompanyHolidayById(id:Long):CompanyHolidayDto
  fun getCompanyHolidayByCompanyId(id:Long):List<CompanyHolidayDto>
  fun deleteCompanyHoliday(id:Long):Boolean
}