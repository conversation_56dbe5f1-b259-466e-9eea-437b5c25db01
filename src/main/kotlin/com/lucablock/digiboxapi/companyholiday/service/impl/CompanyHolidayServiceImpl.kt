package com.lucablock.digiboxapi.companyholiday.service.impl

import com.lucablock.digiboxapi.company.repository.CompanyRepository
import com.lucablock.digiboxapi.companyholiday.dto.CompanyHolidayDto
import com.lucablock.digiboxapi.companyholiday.repository.CompanyHolidayRepository
import com.lucablock.digiboxapi.companyholiday.request.CompanyHolidayRequest
import com.lucablock.digiboxapi.companyholiday.service.CompanyHolidayService
import com.lucablock.digiboxapi.entity.CompanyHoliday
import com.lucablock.digiboxapi.exception.NotFoundException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CompanyHolidayServiceImpl @Autowired constructor(
  private val companyHolidayRepository: CompanyHolidayRepository,
  private val companyRepository: CompanyRepository
):CompanyHolidayService{
  override fun createCompanyHoliday(companyHolidayRequest: CompanyHolidayRequest, companyId: Long): CompanyHolidayDto {
    val company = companyRepository.findById(companyId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลบริษัท")
    }
    companyHolidayRepository.existsCompanyHolidayByHolidayDateAndCompanyId(companyHolidayRequest.holidayDate,company.id).let {
      if (it){
        throw NotFoundException("วันหยุดนี้มีอยู่แล้ว")
      }
    }
    val savedCompanyHoliday = companyHolidayRepository.save(
        CompanyHoliday(
          name = companyHolidayRequest.name,
          description = companyHolidayRequest.description,
          holidayDate = companyHolidayRequest.holidayDate,
          companyId = company.id
        )
    )
    return savedCompanyHoliday.toCompanyHolidayDto()
  }

  override fun updateCompanyHoliday(id: Long, companyHolidayRequest: CompanyHolidayRequest): CompanyHolidayDto {
    val holiday = companyHolidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    holiday.name = companyHolidayRequest.name
    holiday.description = companyHolidayRequest.description
    companyHolidayRepository.save(holiday)
    return holiday.toCompanyHolidayDto()
  }

  override fun getAllCompanyHoliday(): List<CompanyHolidayDto> {
    return companyHolidayRepository.findAll().map { it.toCompanyHolidayDto() }
  }

  override fun getCompanyHolidayById(id: Long): CompanyHolidayDto {
    val holiday = companyHolidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    return holiday.toCompanyHolidayDto()
  }

  override fun getCompanyHolidayByCompanyId(id: Long): List<CompanyHolidayDto> {
    val company = companyRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลบริษัท")
    }
    val holiday = companyHolidayRepository.findByCompanyId(company.id)
    return holiday.map { it.toCompanyHolidayDto() }
  }

  override fun deleteCompanyHoliday(id: Long): Boolean {
    val holiday = companyHolidayRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันหยุด")
    }
    companyHolidayRepository.delete(holiday)
    return true
  }
}