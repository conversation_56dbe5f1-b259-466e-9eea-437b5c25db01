package com.lucablock.digiboxapi.companyholiday.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.util.*

class CompanyHolidayRequest {
  @NotBlank(message = "กรุณาระบุชื่อวันหยุด")
  val name : String = ""
  val description : String? = null

  @NotNull(message = "กรุณาเลือกวัน")
  val holidayDate : Date = Date()

  @NotNull(message = "กรุณาระบุรหัสบริษัท")
  val companyId : Long = 0
}