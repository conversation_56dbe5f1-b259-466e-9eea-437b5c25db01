package com.lucablock.digiboxapi.model.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.model.request.ModelPriceRequest
import com.lucablock.digiboxapi.model.request.ModelRequest
import com.lucablock.digiboxapi.model.request.ModelThumbnailRequest
import com.lucablock.digiboxapi.model.request.UpdateModelRequest
import com.lucablock.digiboxapi.model.service.ModelService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("api")
class ModelController {
  private val logger: Logger = LoggerFactory.getLogger(ModelController::class.java)

  @Autowired
  lateinit var modelService: ModelService

//  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
//  @PostMapping()
//  fun createModel(
//    @RequestBody @Valid modelRequest: ModelRequest,
//  ): ResponseEntity<Any> {
//    return try {
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "สร้างข้อมูลโมเดลสินค้าสำเร็จ",
//          modelService.createModel(modelRequest)
//        )
//      )
//    } catch (e: NotFoundException) {
//      logger.error("create model error : ${e.message}")
//      ResponseEntity.status(HttpStatus.NOT_FOUND)
//        .body(HttpResponse(false, "ไม่มีข้อมูลสินค้านี้"))
//    } catch (e: BadRequestException) {
//      logger.error("create model error : ${e.message}")
//      ResponseEntity.status(HttpStatus.BAD_REQUEST)
//        .body(HttpResponse(false, "มีโมเดลสินค้านี้อยู่แล้ว"))
//    } catch (e: Exception) {
//      logger.error("create model error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, "ไม่สามารถสร้างข้อมูลโมเดลสินค้า กรุณาลองอีกครั้ง"))
//    }
//  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/model/{modelId}/price")
  fun createModelPrice(
    @PathVariable modelId: Long,
    @Valid @RequestBody modelPriceRequest: ModelPriceRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เพิ่มราคาโมเดลสำเร็จ",
          modelService.createModelPrice(modelId, modelPriceRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("create model price error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีโมเดลนี้"))
    } catch (e: Exception) {
      logger.error("create model price error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/model/{modelId}/thumbnail")
  fun createModelThumbnail(
    @PathVariable modelId: Long,
    @Valid @RequestBody modelThumbnailRequest: ModelThumbnailRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เพิ่มรูปภาพตัวอย่างสินค้าสำเร็จ",
          modelService.createModelThumbnail(modelId, modelThumbnailRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("create model thumbnail error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีโมเดลนี้"))
    } catch (e: Exception) {
      logger.error("create model thumbnail error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/model")
  fun findAllModel(): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          modelService.findAllModels()
        )
      )
    } catch (e: Exception) {
      logger.error("find all model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

//  @GetMapping("/populars")
//  fun searchModel(
//    @RequestParam("page", defaultValue = "0") page: Int,
//    @RequestParam("size", defaultValue = "20") size: Int,
//  ): ResponseEntity<Any> {
//    return try {
//      val pageable: Pageable = PageRequest.of(page, size)
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "ค้นหาโมเดลยอดนิยมสำเร็จ",
//          modelService.getModelPopular(pageable)
//        )
//      )
//    } catch (e: Exception) {
//      logger.error("get popular model error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
//    }
//  }

  @GetMapping("/model/search")
  fun searchModel(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("searchTerm", required = false) searchTerm: String?,
    @RequestParam("sortField", required = false) sortField: String?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหาโมเดลสำเร็จ",
          modelService.searchModels(pageable, ascending, searchTerm, sortField)
        )
      )
    } catch (e: Exception) {
      logger.error("search model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/model/{id}")
  fun findModelById(@PathVariable id: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          modelService.findModelById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find model by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้านี้"))
    } catch (e: Exception) {
      logger.error("find model by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

//  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
//  @PutMapping(
//    "/{id}",
//    consumes = [MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE]
//  )
//  fun updateModel(
//    @PathVariable id: Long,
//    @Valid @RequestBody modelRequest: UpdateModelRequest,
//  ): ResponseEntity<Any> {
//    return try {
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "บันทึกข้อมูลโมเดลสินค้าสำเร็จ",
//          modelService.updateModel(id, modelRequest)
//        )
//      )
//    } catch (e: NotFoundException) {
//      logger.error("update model error : ${e.message}")
//      ResponseEntity.status(HttpStatus.NOT_FOUND)
//        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้านี้"))
//    } catch (e: BadRequestException) {
//      logger.error("update model error : ${e.message}")
//      ResponseEntity.status(HttpStatus.BAD_REQUEST)
//        .body(HttpResponse(false, e.message ?: "มีข้อมูลโมเดลสินค้านี้อยู่แล้ว"))
//    } catch (e: Exception) {
//      logger.error("update model error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลโมเดลสินค้า กรุณาลองอีกครั้ง"))
//    }
//  }

//  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
//  @PutMapping("/{modelId}/popular")
//  fun modelPopular(
//    @PathVariable modelId: Long,
//    @RequestParam("status") status: Boolean,
//  ): ResponseEntity<Any> {
//    return try {
//      val msg = if (status) "เพิ่มโมเดลยอดนิยมสำเร็จ" else "ลบโมเดลยอดนิยมสำเร็จ"
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          msg,
//          modelService.modelPopular(modelId, status)
//        )
//      )
//    } catch (e: NotFoundException) {
//      logger.error("model popular error : ${e.message}")
//      ResponseEntity.status(HttpStatus.NOT_FOUND)
//        .body(HttpResponse(false, e.message ?: "ไม่มีโมเดลนี้"))
//    } catch (e: BadRequestException) {
//      logger.error("model popular error : ${e.message}")
//      ResponseEntity.status(HttpStatus.BAD_REQUEST)
//        .body(HttpResponse(false, e.message ?: "กรุณาเพิ่มอัลบั้ม"))
//    } catch (e: Exception) {
//      logger.error("model popular error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
//    }
//  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/model/{modelId}/publish")
  fun modelPublish(
    @PathVariable modelId: Long,
    @RequestParam("status") status: Boolean,
  ): ResponseEntity<Any> {
    return try {
      val msg = if (status) "เปิดใช้งานโมเดลสำเร็จ" else "ปิดใช้งานโมเดลสำเร็จ"
      ResponseEntity.ok(
        HttpResponse(
          true,
          msg,
          modelService.modelActive(modelId, status)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("model publish error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีโมเดลนี้"))
    } catch (e: BadRequestException) {
      logger.error("model publish error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "โมเดลนี้ไม่มีราคา ไม่สามารถเปิดใช้งานได้"))
    } catch (e: Exception) {
      logger.error("model publish error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/model/{id}")
  fun deleteModel(@PathVariable id: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลสำเร็จ",
          modelService.deleteModel(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete model error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้านี้"))
    } catch (e: BadRequestException) {
      logger.error("delete model error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "มีการใช้งานโมเดลนี้แล้ว ไม่สามารถลบได้"))
    } catch (e: Exception) {
      logger.error("delete model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/model/product")
  fun findAllModelByProductId(
    @RequestParam("productId", required = true) productId: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลโมเดลด้วยรหัสสินค้าสำเร็จ",
          modelService.findAllModelByProductId(productId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find model by product id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลสินค้านี้"))
    } catch (e: Exception) {
      logger.error("find model by product id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/model/page")
  fun findAllModelPage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("sortByCode", required = false) sortByCode: Boolean?,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = modelService.getModelPage(
            pageable,
            ascending,
            search,
            sortByCode,
            isActive,
          )
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/model/active")
  fun updateModelActive(
    @RequestParam("id" , required = true) id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกสถานะโมเดลสินค้าสำเร็จ",
          modelService.updateStatusModel(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update model error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลโมเดลสินค้า"))
    } catch (e: Exception) {
      logger.error("update model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลโมเดลสินค้า กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/model")
  fun createNewModel(
    @RequestBody @Valid modelRequest: ModelRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลโมเดลสินค้าสำเร็จ",
          modelService.createNewModel(modelRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("create model error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีข้อมูลสินค้านี้"))
    } catch (e: BadRequestException) {
      logger.error("create model error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, "มีโมเดลสินค้านี้อยู่แล้ว"))
    } catch (e: Exception) {
      logger.error("create model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถสร้างข้อมูลโมเดลสินค้า กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/model/{id}")
  fun updateNewModel(
    @PathVariable id: Long,
    @RequestBody @Valid modelRequest: ModelRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลโมเดลสินค้าสำเร็จ",
          modelService.updateNewModel(id,modelRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update model error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีข้อมูลสินค้านี้"))
    } catch (e: BadRequestException) {
      logger.error("update model error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, "มีโมเดลสินค้านี้อยู่แล้ว"))
    } catch (e: Exception) {
      logger.error("update model error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถบันทึกข้อมูลโมเดลสินค้า กรุณาลองอีกครั้ง"))
    }
  }
}