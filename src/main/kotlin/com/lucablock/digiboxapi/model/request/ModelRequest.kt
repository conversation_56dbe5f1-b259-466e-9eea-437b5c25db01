package com.lucablock.digiboxapi.model.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.lucablock.digiboxapi.modelsize.request.ModelSizeRequest
import com.lucablock.digiboxapi.product.request.ProductCoatingRequest
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

class ModelRequest {
  @NotNull(message = "กรุณาระบุรหัสสินค้า")
  @Min(value = 1, message = "รหัสสินค้าไม่ถูกต้อง")
  val productId: Long = 0

  @NotBlank(message = "กรุณากรอกชื่อโมเดลสินค้า")
  val name: String = ""

  @NotBlank(message = "กรุณาอัปโหลดรูปภาพโมเดลสินค้า")
  val imageUrl: String = ""

  @NotBlank(message = "กรุณากรอกรหัสโมเดลสินค้า")
  val modelCode: String = ""

  val description: String = ""

  @JsonProperty("isActive")
  val isActive: Boolean = true
//
//  @NotEmpty(message = "กรุณาระบุขนาดโมเดล")
//  @Valid
//  val modelSize: List<ModelSizeRequest> = mutableListOf()
}

class UpdateModelRequest {
  @NotNull(message = "ระบุรหัสสินค้า")
  @Min(value = 1, message = "รหัสสินค้าไม่ถูกต้อง")
  val productId: Long = 0

  @NotBlank(message = "กรุณาระบุชื่อโมเดลสินค้า")
  val name: String = ""

  @NotBlank(message = "กรุณาอัปโหลดรูปภาพโมเดลสินค้า")
  val imageUrl: String = ""

  @NotBlank(message = "กรุณาระบุรหัสโมเดลสินค้า")
  val modelCode: String = ""

  val description: String = ""

  @JsonProperty("isActive")
  val isActive: Boolean = true

  @NotEmpty(message = "กรุณาระบุขนาดโมเดล")
  @Valid
  val modelSize: List<ModelSizeRequest> = mutableListOf()

}


