package com.lucablock.digiboxapi.model.request

import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty

class ModelThumbnailRequest {
  @NotEmpty(message = "อัพโหลดรูปภาพตัวอย่างสินค้า")
  @Valid
  val thumbnails: List<ThumbnailRequest> = mutableListOf()
}

class ThumbnailRequest {
  var isCover: Boolean = false

  @NotEmpty(message = "ระบุ url รูปภาพตัวอย่างสินค้า")
  var thumbnailUrl: String = ""
}