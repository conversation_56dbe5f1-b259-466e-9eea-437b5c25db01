package com.lucablock.digiboxapi.model.request

import jakarta.validation.Valid
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

class ModelPriceRequest {
  @NotEmpty(message = "ระบุจำนวน และราคาสินค้า")
  @Valid
  val prices: List<PriceRequest> = mutableListOf()
}

class PriceRequest {
  @NotNull(message = "ระบุรหัสสินค้า")
  @Min(value = 100, message = "จำนวนสินค้าเริ่มต้น 100 ชิ้น")
  val amount: Long = 0

  @NotNull(message = "ระบุราคาสินค้า")
  @DecimalMin(value = "0.1", message = "ราคาเริ่มต้น 0.1 บาท")
  val price: Double = 0.0

  @NotNull(message = "ระบุเปอร์เซ็นต์ส่วนลด")
  val percentage: Long = 0
}