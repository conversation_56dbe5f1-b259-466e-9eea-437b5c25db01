package com.lucablock.digiboxapi.model.repository

import com.lucablock.digiboxapi.entity.ModelPrice
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ModelPriceRepository : JpaRepository<ModelPrice, Long> {
  @Modifying
  @Transactional
  @Query("DELETE FROM model_price WHERE model_id = :modelId", nativeQuery = true)
  fun deleteAllByModelId(modelId: Long)
  fun countByModelId(modelId: Long): Int
}