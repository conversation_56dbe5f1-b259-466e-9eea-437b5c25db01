package com.lucablock.digiboxapi.model.repository

import com.lucablock.digiboxapi.entity.Model
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ModelRepository : JpaRepository<Model, Long>, ModelRepositoryCustom {
  fun findAllByProductIdAndIsActiveTrueAndIsDeletedFalse(productId: Long): List<Model>
  fun findAllByNameContainingIgnoreCaseOrderByCreatedDateDesc(name: String, pageable: Pageable): Page<Model>
//  fun findAllByIsPopularTrueAndIsActiveTrueOrderByIdAsc(pageable: Pageable): Page<Model>
  fun existsByNameIgnoreCase(name: String): Boolean
  fun existsByNameIgnoreCaseAndIdNot(name: String, id: Long): Boolean
  fun existsByNameIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  fun existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String, id: Long): Boolean
}