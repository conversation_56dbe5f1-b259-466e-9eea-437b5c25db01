package com.lucablock.digiboxapi.model.repository

import com.lucablock.digiboxapi.entity.ModelThumbnail
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ModelThumbnailRepository : JpaRepository<ModelThumbnail, Long> {
  fun findAllByModelId(modelId: Long): List<ModelThumbnail>

  @Modifying
  @Transactional
  @Query(
    "UPDATE model_thumbnail SET is_cover = :isCover WHERE model_id = :modelId",
    nativeQuery = true
  )
  fun resetIsCoverByModelId(modelId: Long, isCover: Boolean)

  @Modifying
  @Transactional
  @Query("DELETE FROM model_thumbnail WHERE model_id = :modelId", nativeQuery = true)
  fun deleteAllByModelId(modelId: Long)
  fun countByModelId(modelId: Long): Int
}