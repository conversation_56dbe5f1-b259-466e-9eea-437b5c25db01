package com.lucablock.digiboxapi.model.repository

import com.lucablock.digiboxapi.model.dto.ModelDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository

@Repository
interface ModelRepositoryCustom {
  fun getModelPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    sortByCode : Boolean?,
    isActive : Boolean?
  ): Page<ModelDto>
}