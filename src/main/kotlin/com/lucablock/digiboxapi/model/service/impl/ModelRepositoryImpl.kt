package com.lucablock.digiboxapi.model.service.impl

import com.lucablock.digiboxapi.entity.QModel
import com.lucablock.digiboxapi.model.dto.ModelDto
import com.lucablock.digiboxapi.model.repository.ModelRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ModelRepositoryImpl : ModelRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }
  private val qModel = QModel.model

  override fun getModelPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    sortByCode: Boolean?,
    isActive: Boolean?
  ): Page<ModelDto> {
    var criteria = qModel.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qModel.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qModel.name.containsIgnoreCase(search))
    }

    val sort =
      if(sortByCode == null){
      if (ascending) {
        qModel.id.desc()
      } else {
        qModel.id.asc()
      }
    }else{
      if (sortByCode) {
        qModel.modelCode.desc()
      } else {
        qModel.modelCode.asc()
      }
    }

    val query = queryFactory
      .select(qModel)
      .from(qModel)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toModelDto()
      }

    val total = queryFactory
      .select(qModel)
      .from(qModel)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}