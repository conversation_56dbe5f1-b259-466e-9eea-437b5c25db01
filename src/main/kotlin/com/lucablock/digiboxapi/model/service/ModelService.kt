package com.lucablock.digiboxapi.model.service

import com.lucablock.digiboxapi.model.dto.ModelDto
import com.lucablock.digiboxapi.model.dto.ModelPriceDto
import com.lucablock.digiboxapi.model.dto.ModelThumbnailDto
import com.lucablock.digiboxapi.model.request.ModelPriceRequest
import com.lucablock.digiboxapi.model.request.ModelRequest
import com.lucablock.digiboxapi.model.request.ModelThumbnailRequest
import com.lucablock.digiboxapi.model.request.UpdateModelRequest
import com.lucablock.digiboxapi.specialTechnic.dto.SpecialTechnicDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile


interface ModelService {
//  fun createModel(modelRequest: ModelRequest): ModelDto
  fun createModelPrice(modelId: Long, modelPriceRequest: ModelPriceRequest): List<ModelPriceDto>
  fun createModelThumbnail(
    modelId: Long,
    modelThumbnailRequest: ModelThumbnailRequest
  ): List<ModelThumbnailDto>

  fun findAllModels(): List<ModelDto>
  fun findAllModelByProductId(productId: Long): List<ModelDto>
//  fun getModelPopular(pageable: Pageable): Page<ModelDto>
  fun searchModels(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<ModelDto>

  fun findModelById(id: Long): ModelDto
//  fun updateModel(id: Long, modelRequest: UpdateModelRequest): ModelDto
  fun deleteModel(id: Long)
//  fun modelPopular(modelId: Long, status: Boolean): ModelDto
  fun modelActive(modelId: Long, status: Boolean): ModelDto
  fun getModelPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    sortByCode : Boolean?,
    isActive : Boolean?
  ): Page<ModelDto>
  fun updateStatusModel(id:Long):Boolean
  fun createNewModel(modelRequest: ModelRequest): ModelDto
  fun updateNewModel(id: Long,modelRequest: ModelRequest): ModelDto
}