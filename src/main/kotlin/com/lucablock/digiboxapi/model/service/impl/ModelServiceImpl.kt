package com.lucablock.digiboxapi.model.service.impl

import com.lucablock.digiboxapi.ModelSizeConfigCoating.repository.ModelSizeConfigCoatingRepository
import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.coating.repository.CoatingRepository
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.item.repository.ItemRepository
import com.lucablock.digiboxapi.materialconfig.repository.MaterialConfigRepository
import com.lucablock.digiboxapi.model.dto.ModelDto
import com.lucablock.digiboxapi.model.dto.ModelPriceDto
import com.lucablock.digiboxapi.model.dto.ModelThumbnailDto
import com.lucablock.digiboxapi.model.repository.ModelPriceRepository
import com.lucablock.digiboxapi.model.repository.ModelRepository
import com.lucablock.digiboxapi.model.repository.ModelThumbnailRepository
import com.lucablock.digiboxapi.model.request.ModelPriceRequest
import com.lucablock.digiboxapi.model.request.ModelRequest
import com.lucablock.digiboxapi.model.request.ModelThumbnailRequest
import com.lucablock.digiboxapi.model.request.UpdateModelRequest
import com.lucablock.digiboxapi.model.service.ModelService
import com.lucablock.digiboxapi.modelsize.repository.ModelSizeRepository
import com.lucablock.digiboxapi.modelsizeconfig.repository.ModelSizeConfigRepository
import com.lucablock.digiboxapi.modelsizeconfigdetail.repository.ModelSizeConfigDetailRepository
import com.lucablock.digiboxapi.product.repository.ProductRepository
import com.lucablock.digiboxapi.unfoldedsize.repository.UnfoldedSizeRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ModelServiceImpl @Autowired constructor(
  private val productRepository: ProductRepository,
  private val modelRepository: ModelRepository,
  private val modelPriceRepository: ModelPriceRepository,
  private val modelThumbnailRepository: ModelThumbnailRepository,
  private val itemRepository: ItemRepository,
  private val s3Service: S3Service,
  private val modelSizeRepository: ModelSizeRepository,
  private val modelSizeConfigRepository: ModelSizeConfigRepository,
  private val unfoldedSizeRepository: UnfoldedSizeRepository,
  private val materialConfigRepository: MaterialConfigRepository,
  private val coatingRepository: CoatingRepository,
  private val modelSizeConfigDetailRepository: ModelSizeConfigDetailRepository,
  private val modelSizeConfigCoatingRepository: ModelSizeConfigCoatingRepository,
) : ModelService {


  @Transactional
  override fun createModelPrice(
    modelId: Long,
    modelPriceRequest: ModelPriceRequest
  ): List<ModelPriceDto> {
    modelRepository.findById(modelId).orElseThrow { throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }

    modelPriceRepository.deleteAllByModelId(modelId)
    val modelPrice = modelPriceRequest.prices.map {
      ModelPrice(
        modelId = modelId,
        amount = it.amount,
        price = it.price,
        percentage = it.percentage
      )
    }

    val savedModelPrice = modelPriceRepository.saveAll(modelPrice)
    return savedModelPrice.map { it.toModelPriceDto() }
  }

  override fun createModelThumbnail(
    modelId: Long,
    modelThumbnailRequest: ModelThumbnailRequest
  ): List<ModelThumbnailDto> {
    modelRepository.findById(modelId).orElseThrow { throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }

    val modelThumbnail = modelThumbnailRepository.findAllByModelId(modelId)
    if (modelThumbnail.isNotEmpty()) {
      modelThumbnail.forEach { it ->
        if (it.thumbnailUrl !in modelThumbnailRequest.thumbnails.map { it.thumbnailUrl }) {
          s3Service.deleteFile(it.thumbnailUrl)
          modelThumbnailRepository.delete(it)
        } else {
          val request =
            modelThumbnailRequest.thumbnails.find { req -> req.thumbnailUrl == it.thumbnailUrl }
          if (request != null) {
            if (request.isCover) {
              modelThumbnailRepository.resetIsCoverByModelId(modelId, false)
              it.isCover = true
              modelThumbnailRepository.save(it)
            }
          }
        }
      }

      modelThumbnailRequest.thumbnails.forEach { it ->
        if (it.thumbnailUrl !in modelThumbnail.map { it.thumbnailUrl }) {
          if (it.isCover) {
            modelThumbnailRepository.resetIsCoverByModelId(modelId, false)
          }
          val newFileUrl = s3Service.moveFile(it.thumbnailUrl).url
          modelThumbnailRepository.save(
            ModelThumbnail(
              modelId = modelId,
              isCover = it.isCover,
              thumbnailUrl = newFileUrl
            )
          )
        }
      }
    } else {
      modelThumbnailRequest.thumbnails.forEach {
        val newFileUrl = s3Service.moveFile(it.thumbnailUrl).url
        modelThumbnailRepository.save(
          ModelThumbnail(
            modelId = modelId,
            isCover = it.isCover,
            thumbnailUrl = newFileUrl
          )
        )
      }
    }

    return modelThumbnailRepository.findAllByModelId(modelId)
      .map { it.toModelThumbnailDto() }
  }

  override fun findAllModels(): List<ModelDto> {
    return modelRepository.findAll().map { it.toModelDto() }
  }

  override fun findAllModelByProductId(productId: Long): List<ModelDto> {
    return modelRepository.findAllByProductIdAndIsActiveTrueAndIsDeletedFalse(productId).map { it.toModelDto() }
  }

//  override fun getModelPopular(pageable: Pageable): Page<ModelDto> {
//    return modelRepository.findAllByIsPopularTrueAndIsActiveTrueOrderByIdAsc(pageable)
//      .map { it.toModelDto() }
//  }

  override fun searchModels(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<ModelDto> {
    var term = ""
    if (searchTerm != null) {
      term = searchTerm
    }
    return modelRepository.findAllByNameContainingIgnoreCaseOrderByCreatedDateDesc(term, pageable).map { it.toModelDto() }
  }

  override fun findModelById(id: Long): ModelDto {
    return modelRepository.findById(id).orElseThrow { throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }
      .toModelDto()
  }


  @Transactional
  override fun deleteModel(id: Long) {
    val model = modelRepository.findById(id).orElseThrow { NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }

//    itemRepository.existsItemByModelId(id.toInt())
//      .let { if (it) throw BadRequestException("มีการใช้งานโมเดลนี้ ไม่สามารถลบได้") }
//
//    modelPriceRepository.deleteAllByModelId(id)
//
//    val thumbnail = modelThumbnailRepository.findAllByModelId(id)
//    if (thumbnail.isNotEmpty()) {
//      thumbnail.forEach {
//        s3Service.deleteFile(it.thumbnailUrl)
//      }
//      modelThumbnailRepository.deleteAllByModelId(id)
//    }
//
//    s3Service.deleteFile(model.imageUrl)
//    modelRepository.deleteById(id)
      model.isDeleted = true
      modelRepository.save(model)
  }

//  override fun modelPopular(modelId: Long, status: Boolean): ModelDto {
//    val model = modelRepository.findById(modelId).orElseThrow { NotFoundException("ไม่มีโมเดลนี้") }
//
//    if (status) {
//      modelThumbnailRepository.countByModelId(modelId)
//        .let { if (it == 0) throw BadRequestException("ไม่สารมารถเพิ่มได้ กรุณาเพิ่มอัลบั้ม") }
//    }
//
////    model.isPopular = status
//    return modelRepository.save(model).toModelDto()
//  }

  override fun modelActive(modelId: Long, status: Boolean): ModelDto {
    val model = modelRepository.findById(modelId).orElseThrow { NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }
    if (status) {
      modelPriceRepository.countByModelId(modelId)
        .let { if (it == 0) throw BadRequestException("ไม่สามารถเปิดใช้งานได้ กรุณาเพิ่มจำนวน และราคา") }
    }

    model.isActive = status
    val updatedModel = modelRepository.save(model)
    return updatedModel.toModelDto()
  }

  override fun getModelPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    sortByCode: Boolean?,
    isActive: Boolean?
  ): Page<ModelDto> {
    return modelRepository.getModelPage(pageable, ascending, search, sortByCode, isActive)
  }

  override fun updateStatusModel(id: Long): Boolean {
    val model = modelRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้")
    }
    model.isActive = !model.isActive
    modelRepository.save(model)
    return true
    }

  override fun createNewModel(modelRequest: ModelRequest): ModelDto {
    modelRepository.existsByNameIgnoreCaseAndIsDeletedFalse(modelRequest.name).let {
      if (it) throw BadRequestException("มีโมเดลสินค้านี้อยู่แล้ว")
    }
    val product = productRepository.findById(modelRequest.productId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลสินค้านี้")
    }
    val model = Model(
        name = modelRequest.name,
        imageUrl = modelRequest.imageUrl,
        modelCode = modelRequest.modelCode,
        description = modelRequest.description,
        productId = modelRequest.productId,
        product = product,
        isActive = true
      )
    val urlFile = s3Service.moveFile(modelRequest.imageUrl).url
    model.imageUrl = urlFile
    val savedModel = modelRepository.save(model)
    return savedModel.toModelDto()
  }

  override fun updateNewModel(id: Long, modelRequest: ModelRequest): ModelDto {
    modelRepository.existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(modelRequest.name,id).let {
      if (it) throw BadRequestException("มีโมเดลสินค้านี้อยู่แล้ว")
    }
    val model = modelRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้")
    }
    model.name = modelRequest.name
    model.modelCode = modelRequest.modelCode
    model.description = modelRequest.description
    model.isActive = modelRequest.isActive
    model.productId = modelRequest.productId

    if (modelRequest.imageUrl != model.imageUrl) {
      s3Service.deleteFile(model.imageUrl)
      val newFileUrl = s3Service.moveFile(modelRequest.imageUrl).url
      model.imageUrl = newFileUrl
    }

    val updateModel = modelRepository.save(model)
    return updateModel.toModelDto()
  }

//  @Transactional
//  override fun createModel(modelRequest: ModelRequest): ModelDto {
//    val product = productRepository.findById(modelRequest.productId)
//      .orElseThrow { throw NotFoundException("ไม่พบข้อมูลสินค้านี้") }
//
//    modelRepository.existsByNameIgnoreCase(modelRequest.name)
//      .let { if (it) throw BadRequestException("มีโมเดลสินค้านี้อยู่แล้ว") }
//
//    val checkUnfolded = modelRequest.modelSize.map {
//      it.unfoldedSizeId
//    }.toSet()
//    val unfoldedSize = unfoldedSizeRepository.findAllById(checkUnfolded)
//    if (unfoldedSize.size != checkUnfolded.size) {
//      throw NotFoundException("ไม่พบข้อมูลขนาดกางออก")
//    }
//
//    val checkMatConfig = modelRequest.modelSize.map {
//      it.modelSizeConfig.map { config ->
//        config.materialConfigId
//      }
//    }.flatten().toSet()
//    val materialConfig = materialConfigRepository.findAllById(checkMatConfig)
//    if(checkMatConfig.size != materialConfig.size ) {
//      throw NotFoundException("ไม่พบข้อมูลขนาดต่อแกรม")
//    }
//
//    modelRequest.modelSize.map { sizeReq ->
//      if (sizeReq.length == null || sizeReq.length == 0.0 && sizeReq.isThreeD) {
//        throw Exception("กรุณาระบุความยาว")
//      }
//    }
//
//    val model = Model(
//      productId = modelRequest.productId,
//      name = modelRequest.name,
//      imageUrl = modelRequest.imageUrl,
//      modelCode = modelRequest.modelCode,
//      description = modelRequest.description,
//    )
//    val urlFile = s3Service.moveFile(modelRequest.imageUrl).url
//    model.imageUrl = urlFile
//    val savedModel = modelRepository.save(model)
//
//    val savedModelSize = modelRequest.modelSize.map { sizeReq ->
//      val modelSize = modelSizeRepository.save(
//        ModelSize(
//          modelId = savedModel.id,
//          unfoldedSizeId = sizeReq.unfoldedSizeId,
//          width = sizeReq.width,
//          height = sizeReq.height,
//          length = sizeReq.length,
//          isThreeD = sizeReq.isThreeD,
//        )
//      )
//      val savedModelSizeConfig = sizeReq.modelSizeConfig.map { configReq ->
//        val modelSizeConfig = modelSizeConfigRepository.save(
//          ModelSizeConfig(
//            modelSizeId = modelSize.id,
//            materialConfigId = configReq.materialConfigId,
//            printingId = configReq.printingId
//          )
//        )
//        val savedModelSizeConfigDetail = configReq.modelSizeConfigDetail.map { detail ->
//          modelSizeConfigDetailRepository.save(
//            ModelSizeConfigDetail(
//              modelSizeConfigId = modelSizeConfig.id,
//              amount = detail.amount,
//              price = detail.price,
//              period = detail.period
//            )
//          )
//        }
//        modelSizeConfig.modelSizeConfigDetail = savedModelSizeConfigDetail
//
//        val savedModelSizeConfigCoating = configReq.modelSizeConfigCoating.map { coating ->
//          modelSizeConfigCoatingRepository.save(
//            ModelSizeConfigCoating(
//              modelSizeConfigId = modelSizeConfig.id,
//              coatingId = coating.coatingId
//            )
//          )
//        }
//        modelSizeConfig.modelSizeConfigCoating = savedModelSizeConfigCoating
//        modelSizeConfig
//      }
//      modelSize.modelSizeConfig = savedModelSizeConfig
//      modelSizeRepository.save(modelSize)
//    }
//
//    savedModel.modelSize = savedModelSize
//    val updatedModel = modelRepository.save(savedModel)
//    return updatedModel.toModelDto()
//  }
//
//  @Transactional
//  override fun updateModel(id: Long, modelRequest: UpdateModelRequest): ModelDto {
//    val model =
//      modelRepository.findById(id).orElseThrow { throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }
//
//    productRepository.findById(modelRequest.productId)
//      .orElseThrow { throw NotFoundException("ไม่พบข้อมูลสินค้านี้") }
//
//    modelRepository.existsByNameIgnoreCaseAndIdNot(modelRequest.name, id)
//      .let { if (it) throw BadRequestException("มีโมเดลสินค้านี้อยู่แล้ว") }
//
//
//    val checkUnfolded = modelRequest.modelSize.map {
//      it.unfoldedSizeId
//    }.toSet()
//    val unfoldedSize = unfoldedSizeRepository.findAllById(checkUnfolded)
//    if (unfoldedSize.size != checkUnfolded.size) {
//      throw NotFoundException("ไม่พบข้อมูลขนาดกางออก")
//    }
//
//    val checkMatConfig = modelRequest.modelSize.map {
//      it.modelSizeConfig.map { config ->
//        config.materialConfigId
//      }
//    }.flatten().toSet()
//    val materialConfig = materialConfigRepository.findAllById(checkMatConfig)
//    if(checkMatConfig.size != materialConfig.size ) {
//      throw NotFoundException("ไม่พบข้อมูลขนาดต่อแกรม")
//    }
//    val oldModelSize = modelSizeRepository.findByModelId(id)
//    val oldModelSizeConfig = modelSizeConfigRepository.findAllByModelSizeIn(oldModelSize)
//    val oldModelSizeConfigIds = oldModelSizeConfig.map { it.id }
//    modelSizeConfigDetailRepository.deleteAllByModelSizeConfigIdIn(oldModelSizeConfigIds)
//    modelSizeConfigCoatingRepository.deleteAllByModelSizeConfigIdIn(oldModelSizeConfigIds)
//
//    val modelSizeId = modelSizeRepository.findByModelId(id)
//    modelSizeConfigRepository.deleteAllByModelSizeIdIn(modelSizeId.map { it.id })
//    modelSizeRepository.deleteAllById(modelSizeId.map { it.id })
//
//    model.name = modelRequest.name
//    model.modelCode = modelRequest.modelCode
//    model.description = modelRequest.description
//    model.isActive = modelRequest.isActive
//    model.productId = modelRequest.productId
//    model.modelSize = modelRequest.modelSize.map { sizeReq ->
//      val modelSize = modelSizeRepository.save(
//        ModelSize(
//          modelId = id,
//          unfoldedSizeId = sizeReq.unfoldedSizeId,
//          width = sizeReq.width,
//          height = sizeReq.height,
//          length = sizeReq.length,
//          isThreeD = sizeReq.isThreeD,
//        )
//      )
//      val savedModelSizeConfig = sizeReq.modelSizeConfig.map { configReq ->
//        val modelSizeConfig = modelSizeConfigRepository.save(
//          ModelSizeConfig(
//            modelSizeId = modelSize.id,
//            materialConfigId = configReq.materialConfigId,
//            printingId = configReq.printingId,
//          )
//        )
//        val detailList = configReq.modelSizeConfigDetail.map { detail ->
//          modelSizeConfigDetailRepository.save(
//            ModelSizeConfigDetail(
//              modelSizeConfigId = modelSizeConfig.id,
//              amount = detail.amount,
//              price = detail.price,
//              period = detail.period
//            )
//          )
//        }
//        val coatingList = configReq.modelSizeConfigCoating.map { coating ->
//          modelSizeConfigCoatingRepository.save(
//            ModelSizeConfigCoating(
//              modelSizeConfigId = modelSizeConfig.id,
//              coatingId = coating.coatingId
//            )
//          )
//        }
//        modelSizeConfig.modelSizeConfigDetail = detailList
//        modelSizeConfig.modelSizeConfigCoating = coatingList
//        modelSizeConfig
//      }
//
//
//      modelSize.modelSizeConfig = savedModelSizeConfig
//      modelSizeRepository.save(modelSize)
//    }
//
//    if (modelRequest.imageUrl != model.imageUrl) {
//      s3Service.deleteFile(model.imageUrl)
//      val newFileUrl = s3Service.moveFile(modelRequest.imageUrl).url
//      model.imageUrl = newFileUrl
//    }
//
//    val updateModel = modelRepository.save(model)
//
//    return updateModel.toModelDto()
//  }

}