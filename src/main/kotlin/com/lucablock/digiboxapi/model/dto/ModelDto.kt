package com.lucablock.digiboxapi.model.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.entity.ModelReviewStat
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeNewDto
import com.lucablock.digiboxapi.product.dto.ProductListDto
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.util.*

data class ModelDto
@QueryProjection
constructor(
  val id: Long,
  var name: String,
  var imageUrl: String,
  var modelCode: String,
  var description: String?,
  var isActive: Boolean? = true,
  var product: ProductListDto?,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
  val modelSize: List<ModelSizeNewDto> = mutableListOf(),
  val stat : ModelReviewStat? = null,
) : Serializable

data class ModelCustomDto
@QueryProjection
constructor(
  val id: Long,
  var name: String,
  var imageUrl: String,
  var modelCode: String,
  var description: String?,
  var isActive: Boolean? = true,
  var product: ProductListDto?,
)