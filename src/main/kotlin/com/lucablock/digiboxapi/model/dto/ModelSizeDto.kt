package com.lucablock.digiboxapi.model.dto

import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable


data class CreateModelSizeDto(
  var name: String,
  var width: Int,
  var length: Int,
  var height: Int,
  var modelId: Int,
)

data class CreateModelSizeByModelDto(
  var name: String,
  var width: Int,
  var length: Int,
  var height: Int,
)
data class ModelSizeDto
@QueryProjection
constructor(
  val id: Int,
  var name: String,
  var width: Int,
  var length: Int,
  var height: Int,
  var modelId: Int,
) : Serializable

data class ModelSizeByModelIdDto
@QueryProjection
constructor(
  val id: Int,
  var name: String,
  var width: Int,
  var length: Int,
  var height: Int,
) : Serializable