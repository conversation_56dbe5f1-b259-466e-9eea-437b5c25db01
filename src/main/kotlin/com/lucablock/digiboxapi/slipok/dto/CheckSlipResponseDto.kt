package com.lucablock.digiboxapi.slipok.dto

data class CheckSlipResponseDto(
  val success: Boolean,
//    val message: String,
  val data: SlipData? = null
)

data class SlipData(
  val success: Boolean,
  val message: String,
  val language: String? = null,
  val transRef: String,
  val sendingBank: String,
  val receivingBank: String? = null,
  val transDate: String,
  val transTime: String,
  val transTimestamp: String,
  val sender: Sender,
  val receiver: Receiver,
  val amount: Int,
  val paidLocalAmount: Int? = 0,
  val paidLocalCurrency: String? = null,
  val countryCode: String,
  val transFeeAmount: Int? = 0,
  val ref1: String,
  val ref2: String,
  val ref3: String,
  val toMerchantId: String,
  val qrcodeData: String
)

data class Sender(
  val displayName: String,
  val name: String,
  val proxy: Proxy,
  val account: Account
)

data class Receiver(
  val displayName: String,
  val name: String,
  val proxy: Proxy,
  val account: Account
)

data class Proxy(
  val type: String?,
  val value: String?
)

data class Account(
  val type: String,
  val value: String
)
