package com.lucablock.digiboxapi.slipok.controller

import com.lucablock.digiboxapi.slipok.request.QrCodeDataRequest
import com.lucablock.digiboxapi.slipok.service.SlipOkClientService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("/api/v1/slip-ok")
class SlipOkController @Autowired internal constructor(

  private val slipOkClientService: SlipOkClientService
) {
  @Value("\${slip-ok.api.key}")
  private val slipOkApiKey: String? = null

  @PostMapping("/qrcode")
  fun checkSlipByQrValue(
    @RequestBody qrCodeValue: QrCodeDataRequest
  ): Any {
    try {
      val response = slipOkClientService.checkSlipByQrValue(authorization = slipOkApiKey!!, data = qrCodeValue)
      return response
    } catch (e: Exception) {
      return mapOf("error" to "Failed to check slip: ${e.message}")
    }
  }

  @PostMapping("/file")
  fun checkSlipByFile(
    @RequestPart("file") file: MultipartFile
  ): Any {
    try {
      val response = slipOkClientService.checkSlipFile(authorization = slipOkApiKey!!, files = file)
      return response
    } catch (e: Exception) {
      return mapOf("error" to "Failed to check slip: ${e.message}")
    }
  }

}