package com.lucablock.digiboxapi.slipok.service

import com.lucablock.digiboxapi.config.SlipOkClientConfig
import com.lucablock.digiboxapi.slipok.dto.CheckSlipResponseDto
import com.lucablock.digiboxapi.slipok.request.QrCodeDataRequest
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.multipart.MultipartFile

@FeignClient("slip-ok", url = "\${slip-ok.api.url}", configuration = [SlipOkClientConfig::class])
interface SlipOkClientService {

  /**
   * Checks the slip with the given reference number.
   * @param qrCodeValue The transaction reference number to check.
   * @return A response containing the slip details.
   */
  @PostMapping("/\${slip-ok.branch.id}")
  fun checkSlipByQrValue(
    @RequestHeader("x-authorization") authorization: String = "\${slip-ok.api.key}",
    @RequestBody data: QrCodeDataRequest
  ): CheckSlipResponseDto

  @PostMapping("/\${slip-ok.branch.id}", consumes = ["multipart/form-data"])
  fun checkSlipFile(
    @RequestHeader("x-authorization") authorization: String = "\${slip-ok.api.key}",
    @RequestPart("files") files: MultipartFile
  ): CheckSlipResponseDto
}