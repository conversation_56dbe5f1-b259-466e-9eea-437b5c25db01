package com.lucablock.digiboxapi

import com.lucablock.digiboxapi.config.AppProperties
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.scheduling.annotation.EnableScheduling

@EnableConfigurationProperties(AppProperties::class)
@SpringBootApplication
@EnableScheduling
@EnableFeignClients
@EnableCaching
class DigiboxApiApplication

fun main(args: Array<String>) {
  runApplication<DigiboxApiApplication>(*args)
}
