package com.lucablock.digiboxapi.awss3.service.impl

import com.lucablock.digiboxapi.awss3.config.S3Config
import com.lucablock.digiboxapi.awss3.response.S3Response
import com.lucablock.digiboxapi.awss3.service.S3Service
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import software.amazon.awssdk.core.exception.SdkClientException
import software.amazon.awssdk.core.exception.SdkServiceException
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectResponse
import java.nio.ByteBuffer
import java.util.*


@Service
class S3ServiceImpl : S3Service {
  private val logger: Logger = LoggerFactory.getLogger(S3ServiceImpl::class.java)

  @Autowired
  lateinit var s3Config: S3Config

  @Value("\${s3.bucket}")
  private lateinit var bucketName: String

  @Value("\${s3.folderBuffer}")
  private lateinit var folderBuffer: String

  @Value("\${s3.folder}")
  private lateinit var folderName: String

  @Value("\${s3.url}")
  private lateinit var cdnUrl: String

  @Async
  override fun uploadFile(file: MultipartFile): S3Response {
    try {
      val originFileName: String? = file.originalFilename?.replace(" ", "-")
      val uuid = UUID.randomUUID().toString()
      val putObjectResult: PutObjectResponse = s3Config.s3Client().putObject(
        PutObjectRequest.builder().bucket(bucketName).key("$folderName/$uuid$originFileName")
          .contentType(file.contentType).build(),
        RequestBody.fromByteBuffer(ByteBuffer.wrap(file.bytes))
      )
      logger.info("putObjectResult = $putObjectResult")
      return S3Response(
        url = "$cdnUrl$folderName/$uuid$originFileName",
      )
    } catch (ase: SdkServiceException) {
      logger.error("SdkClientException = ${ase.message}")
      throw ase
    } catch (ace: SdkClientException) {
      logger.error("SdkServiceException = ${ace.message}")
      throw ace
    }
  }

  override fun uploadFileBuffer(file: MultipartFile): S3Response {
    try {
      val originFileName: String? = file.originalFilename.split(".").last()
      val uuid = UUID.randomUUID().toString()
      val putObjectResult: PutObjectResponse = s3Config.s3Client().putObject(
        PutObjectRequest.builder().bucket(bucketName).key("$folderBuffer/$uuid.$originFileName")
          .contentType(file.contentType).build(),
        RequestBody.fromByteBuffer(ByteBuffer.wrap(file.bytes))
      )
      logger.info("putObjectResult = $putObjectResult")
      return S3Response(
        url = "$cdnUrl$folderBuffer/$uuid.$originFileName",
      )
    } catch (ase: SdkServiceException) {
      logger.error("SdkClientException = ${ase.message}")
      throw ase
    } catch (ace: SdkClientException) {
      logger.error("SdkServiceException = ${ace.message}")
      throw ace
    }
  }

  override fun moveFile(fileKey: String): S3Response {
    try {
      val key = fileKey.substringAfterLast("$cdnUrl/")
      val newKey = fileKey.substringAfterLast("$cdnUrl$folderBuffer")

      s3Config.s3Client().copyObject {
        it.bucket(bucketName)
          .copySource("$bucketName/$key")
          .key("$folderName$newKey")
      }
      s3Config.s3Client().deleteObject {
        it.bucket(bucketName)
          .key(key)
      }
      return S3Response(url = "$cdnUrl$folderName$newKey")
    } catch (ase: SdkServiceException) {
      logger.error("SdkClientException = ${ase.message}")
      throw ase
    } catch (ace: SdkClientException) {
      logger.error("SdkServiceException = ${ace.message}")
      throw ace
    }
  }

  @Async
  override fun deleteFile(fileKey: String) {
    val key = fileKey.substringAfterLast("$cdnUrl/")
    s3Config.s3Client()
      .deleteObject(DeleteObjectRequest.builder().bucket(bucketName).key(key).build())
  }
}