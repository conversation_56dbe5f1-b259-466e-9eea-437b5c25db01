package com.lucablock.digiboxapi.customize.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.ModelSizeConfigCoating.dto.ModelSizeConfigCoatingDto
import com.lucablock.digiboxapi.coating.dto.CoatingListDto
import com.lucablock.digiboxapi.customize.constant.SampleProductEnum
import com.lucablock.digiboxapi.customize.constant.ShippingTypeEnum
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigDto
import com.lucablock.digiboxapi.model.dto.ModelCustomDto
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeCustomDto
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeListDto
import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailDto
import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailListDto
import com.lucablock.digiboxapi.printing.dto.PrintingDto
import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodDto
import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodListDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigListDto
import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeDto
import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeListDto
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.util.Date

data class CustomizeDto
@QueryProjection constructor(
  val id: Long,
  val model: ModelCustomDto?,
  val modelSizeId: Long,
  val width : Double,
  val height : Double,
  val length : Double,
  val unfoldedSize: UnfoldedSizeListDto?,
  val materialConfig: MaterialConfigDto?,
  val printing: PrintingDto?,
  val coating: List<CustomizeCoatingDto>,
  val modelSizeConfigDetail: ModelSizeConfigDetailDto?,
  val customizeSpecialTechnic : List<CustomizeSpacialTechnicDto>,
  val productPeriod: ProductPeriodDto?,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val periodDate : Date,
  val sampleProductEnum : Int,
  val sampleProductEnumName: String,
  val sampleProductEnumPrice : Double,
  val shippingTypeEnum: Int,
  val shippingTypeEnumName: String,
  val personPickupName: String?,
  val personPickupTell: String?,
  val zipcode: String,
  val isArtwork: Boolean,
  val artworkEnum : String,
  val dieLine : String?,
)
data class CustomizeSpacialTechnicDto
@QueryProjection constructor(
  val id: Long,
  val customizeId : Long,
  val specialTechnicConfig : SpecialTechnicConfigDto?,
)
data class CustomizeCoatingDto
@QueryProjection constructor(
  val id: Long,
  val customizeId : Long,
  val modelSizeConfigCoating : ModelSizeConfigCoatingDto?,
)
data class FindModelSizeIdDto
@QueryProjection constructor(
  val modelId : Long,
  val width : Int,
  val height : Int,
  val length : Int?,
  val modelSizeId : Long,
  val unfoldedSizeId : Long
)
data class CalModelSizeIdDto
@QueryProjection constructor(
  val modelSizeId : Long,
  val unfoldedSizeId : Long
)
data class CustomizeCoatingListDto
@QueryProjection constructor(
  val id: Long,
  val customId : Long,
  val coatingId : Int,
  val coatingName : String,
  val price : Double,
): Serializable