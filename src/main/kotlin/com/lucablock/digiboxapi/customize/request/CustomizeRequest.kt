package com.lucablock.digiboxapi.customize.request

import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

class CustomizeRequest {
  @NotNull(message = "กรถณาระบุรหัสโมเดล")
  val modelId : Long = 0
  @NotNull(message = "กรถณาระบุรหัสขนาดโมเดล")
  val modelSizeId : Long = 0
  @NotNull(message = "กรถณาระบุรหัสวัสดุ")
  val materialConfigId : Long = 0
  @NotNull(message = "กรถณาระบุรหัสระบบพิมพ์")
  val printingId : Long = 0
  @NotNull(message = "กรถณาระบุรหัสรายละเอียดโมเดล")
  val modelSizeConfigDetailId : Long = 0
  @Valid
  val customizeSpecialTechnic : List<CustomizeSpecialTechnicRequest> = mutableListOf()

  @Valid
  val customizeCoating : List<CustomizeCoatingRequest> = mutableListOf()

  val width : Double = 0.0
  val height : Double = 0.0
  val length : Double? = null
  val unfoldedSizeId : Long = 0

  @NotNull(message = "กรถณาระบุรหัสระยะเวลาการผลิต")
  val productPeriodId : Long = 0
  @NotNull(message = "กรุณาระบุตัวอย่างสินค้า")
  val sampleProduct : Int = 0
  val zipcode : String = ""
  @NotNull(message = "กรุณาระบุวิธีการรับสินค้า")
  val shippingType: Int = 0
  val personPickupName : String? = null
  val personPickupTell : String? = null
  val isArtwork : Boolean = false
  val dieLine : String? = null
}

class CustomizeSpecialTechnicRequest{
  @NotNull(message = "กรถณาระบุรหัสเทคนิคพิเศษ")
  val specialTechnicConfigId : Long = 0
  @NotNull(message = "กรถณาระบุรหัสการปรับแต่งสินค้า")
  val customize : Long = 0
}

class CustomizeCoatingRequest{
  @NotNull(message = "กรถณาระบุรหัสเคลือบ")
  val modelSizeConfigCoatingId : Long = 0
  @NotNull(message = "กรถณาระบุรหัสการปรับแต่งสินค้า")
  val customize : Long = 0
}