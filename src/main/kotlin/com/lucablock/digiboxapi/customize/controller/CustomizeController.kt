package com.lucablock.digiboxapi.customize.controller

import com.lucablock.digiboxapi.customize.request.CustomizeRequest
import com.lucablock.digiboxapi.customize.service.CustomizeService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.material.request.MaterialRequest
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class CustomizeController @Autowired internal constructor(
  private val customizeService: CustomizeService
){
  private val logger: Logger = LoggerFactory.getLogger(CustomizeController::class.java)

  @PostMapping("/web/customize")
  fun createCustomize(
    @Valid @RequestBody customizeRequest: CustomizeRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลปรับแต่งสินค้าสำเร็จ",
          customizeService.createCustomize(customizeRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("create customize error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลปรับแต่งสินค้า"))
    } catch (e: Exception) {
      logger.error("create customize error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถสร้างข้อมูลปรับแต่งสินค้า กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/customize/find-model-size")
  fun findCustomizeById(
  @RequestParam(value = "width", required = true) width: Int,
  @RequestParam(value = "length", required = true) length: Int,
  @RequestParam(value = "height", required = true) height: Int,
  @RequestParam(value = "unfoldedHeight", required = true) unfoldedHeight: Double,
  @RequestParam(value = "unfoldedWidth", required = true) unfoldedWidth: Double,
  @RequestParam(value = "modelId", required = true) modelId: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          customizeService.findModelSizeIdBySize(
            width,
            length,
            height,
            unfoldedHeight,
            unfoldedWidth ,
            modelId
          )
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find customize by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลปรับแต่งสินค้า"))
    } catch (e: Exception) {
      logger.error("find customize by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("web/customize/{id}")
  fun getCustomizeById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลปรับแต่งสินค้าสำเร็จ",
          customizeService.getCustomizeById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false,e.message?: "ไม่พบข้อมูลปรับแต่งสินค้า"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "คำร้องขอไม่ถูกต้อง"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PutMapping("/web/customize/update/{id}")
  fun updateCustomize(
    @PathVariable id: Long,
    @Valid @RequestBody customizeRequest: CustomizeRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลปรับแต่งสินค้าสำเร็จ",
          customizeService.updateCustomize(id,customizeRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update customize error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลปรับแต่งสินค้า"))
    } catch (e: Exception) {
      logger.error("update customize error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message?: "ไม่สามารถบันทึกข้อมูลปรับแต่งสินค้า กรุณาลองอีกครั้ง"))
    }
  }
}