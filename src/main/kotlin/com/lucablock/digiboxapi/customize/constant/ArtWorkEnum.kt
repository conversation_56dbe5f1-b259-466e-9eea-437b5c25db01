package com.lucablock.digiboxapi.customize.constant

enum class ArtWorkEnum(val value: Boolean, val description: String) {
  USE_ARTWORK(true, "มีไฟล์อาร์ตเวิร์ก"),
  UNUSED_ARTWORK(false, "ไม่ใช้ไฟล์อาร์ตเวิร์ก");

  companion object {
    fun fromValue(value: Boolean): ArtWorkEnum {
      return ArtWorkEnum.entries.firstOrNull { it.value == value }
        ?: throw IllegalArgumentException("ไม่พบสถานะสำหรับ value: $value")
    }
  }
}