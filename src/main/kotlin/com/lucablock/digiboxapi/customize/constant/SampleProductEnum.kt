package com.lucablock.digiboxapi.customize.constant

enum class SampleProductEnum(val value: Int, val description: String, val price : Double) {
  SAO_PROOF(1, "Soft and Online Proof", 0.0),
  MOCKUP_PROOF(2, "Mockup Proof", 300.0);

  companion object {
    fun fromValue(value: Int): SampleProductEnum {
      return SampleProductEnum.entries.firstOrNull { it.value == value }
        ?: throw IllegalArgumentException("ไม่พบสถานะสำหรับ value: $value")
    }
  }
}

enum class ShippingTypeEnum(val value: Int, val description: String) {
  PICK_UP(1, "รับสินค้าด้วยตัวเอง"),
  SHIPPING(2, "ส่งสินค้าถึงบ้าน");

  companion object {
    fun fromValue(value: Int): ShippingTypeEnum {
      return ShippingTypeEnum.entries.firstOrNull { it.value == value }
        ?: throw IllegalArgumentException("ไม่พบสถานะสำหรับ value: $value")
    }
  }
}