package com.lucablock.digiboxapi.customize.repository

import com.lucablock.digiboxapi.entity.CustomizeSpecialTechnic
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CustomizeSpecialTechnicRepository : JpaRepository<CustomizeSpecialTechnic,Long> {
  fun findAllBySpecialTechnicConfigIdIn(specialTechnicConfigIds: List<Long>): MutableList<CustomizeSpecialTechnic>
  fun existsCustomizeSpecialTechnicBySpecialTechnicConfigId(specialTechnicConfigId: Long): Boolean
  fun existsCustomizeSpecialTechnicBySpecialTechnicConfigIdIn(specialTechnicConfigIds: List<Long>): Boolean
  fun findAllByCustomizeId(customizeId: Long): MutableList<CustomizeSpecialTechnic>
}