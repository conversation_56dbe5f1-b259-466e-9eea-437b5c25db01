package com.lucablock.digiboxapi.customize.service

import com.lucablock.digiboxapi.customize.dto.CustomizeDto
import com.lucablock.digiboxapi.customize.dto.FindModelSizeIdDto
import com.lucablock.digiboxapi.customize.request.CustomizeRequest

interface CustomizeService {
  fun createCustomize(customizeRequest: CustomizeRequest):CustomizeDto
  fun getCustomizeById(id:Long):CustomizeDto
  fun findModelSizeIdBySize(
    width:Int,
    length:Int,
    height:Int,
    unfoldedHeight:Double,
    unfoldedWidth:Double,
    modelId:Long
  ): FindModelSizeIdDto
  fun updateCustomize(id: Long, customizeRequest: CustomizeRequest): CustomizeDto
}