package com.lucablock.digiboxapi.customize.service.impl

import com.lucablock.digiboxapi.ModelSizeConfigCoating.repository.ModelSizeConfigCoatingRepository
import com.lucablock.digiboxapi.coating.repository.CoatingRepository
import com.lucablock.digiboxapi.customize.dto.CalModelSizeIdDto
import com.lucablock.digiboxapi.customize.dto.CustomizeDto
import com.lucablock.digiboxapi.customize.dto.FindModelSizeIdDto
import com.lucablock.digiboxapi.customize.repository.CustomizeCoatingRepository
import com.lucablock.digiboxapi.customize.repository.CustomizeRepository
import com.lucablock.digiboxapi.customize.repository.CustomizeSpecialTechnicRepository
import com.lucablock.digiboxapi.customize.request.CustomizeRequest
import com.lucablock.digiboxapi.customize.service.CustomizeService
import com.lucablock.digiboxapi.entity.Customize
import com.lucablock.digiboxapi.entity.CustomizeCoating
import com.lucablock.digiboxapi.entity.CustomizeSpecialTechnic
import com.lucablock.digiboxapi.entity.ModelSize
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.materialconfig.repository.MaterialConfigRepository
import com.lucablock.digiboxapi.model.repository.ModelRepository
import com.lucablock.digiboxapi.modelsize.repository.ModelSizeRepository
import com.lucablock.digiboxapi.modelsizeconfigdetail.repository.ModelSizeConfigDetailRepository
import com.lucablock.digiboxapi.printing.repository.PrintingRepository
import com.lucablock.digiboxapi.productperiod.repository.ProductPeriodRepository
import com.lucablock.digiboxapi.specialtechnicconfig.repository.SpecialTechnicConfigRepository
import com.lucablock.digiboxapi.unfoldedsize.repository.UnfoldedSizeRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.ZoneId
import java.util.*

@Service
class CustomizeServiceImpl @Autowired constructor(
  private val customizeRepository: CustomizeRepository,
  private val modelRepository: ModelRepository,
  private val modelSizeRepository: ModelSizeRepository,
  private val materialConfigRepository: MaterialConfigRepository,
  private val printingRepository: PrintingRepository,
  private val coatingRepository: CoatingRepository,
  private val modelSizeConfigDetailRepository: ModelSizeConfigDetailRepository,
  private val customizeSpecialTechnicRepository: CustomizeSpecialTechnicRepository,
  private val specialTechnicConfigRepository: SpecialTechnicConfigRepository,
  private val customizeCoatingRepository: CustomizeCoatingRepository,
  private val modelSizeConfigCoatingRepository: ModelSizeConfigCoatingRepository,
  private val unfoldedSizeRepository: UnfoldedSizeRepository,
  private val productPeriodRepository: ProductPeriodRepository
) : CustomizeService {
  @Transactional
  override fun createCustomize(customizeRequest: CustomizeRequest): CustomizeDto {
    val model = modelRepository.findById(customizeRequest.modelId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลโมเดล")
    }
    val modelSize = modelSizeRepository.findById(customizeRequest.modelSizeId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดล")
    }

    val materialConfig = materialConfigRepository.findById(customizeRequest.materialConfigId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลวัสดุ")
    }
    val printing = printingRepository.findById(customizeRequest.printingId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์")
    }
    val modelSizeConfigDetail = modelSizeConfigDetailRepository.findById(customizeRequest.modelSizeConfigDetailId)
      .orElseThrow { NotFoundException("ไม่พบข้อมูลรายละเอียดโมเดล") }
    val productPeriod = productPeriodRepository.findById(customizeRequest.productPeriodId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระยะเวลาการผลิต")
    }
    val unfolded = unfoldedSizeRepository.findById(customizeRequest.unfoldedSizeId).orElseThrow {
      throw NotFoundException("ไม่พบขนาดกางออก")
    }

    val periodDate = LocalDate.now().plusDays(productPeriod.maxPeriod.toLong())
    val period: Date = Date.from(periodDate.atStartOfDay(ZoneId.systemDefault()).toInstant())

    val customize = customizeRepository.save(
      Customize(
        modelId = model.id,
        model = model,
        modelSizeId = modelSize.id,
        materialConfigId = materialConfig.id,
        materialConfig = materialConfig,
        printingId = printing.id,
        printing = printing,
        modelSizeConfigDetailId = modelSizeConfigDetail.id,
        modelSizeConfigDetail = modelSizeConfigDetail,
        width = customizeRequest.width,
        height = customizeRequest.height,
        length = customizeRequest.length,
        unfoldedSizeId = unfolded.id,
        unfoldedSize = unfolded,
        productPeriodId = productPeriod.id,
        productPeriod = productPeriod,
        periodDate = period,
        sampleProduct = customizeRequest.sampleProduct,
        shippingType = customizeRequest.shippingType,
        personPickupName = customizeRequest.personPickupName,
        personPickupTell = customizeRequest.personPickupTell,
        zipcode = customizeRequest.zipcode,
        modelSize = modelSize,
        isArtwork = customizeRequest.isArtwork,
        dieLine = customizeRequest.dieLine
      )
    )
    val customizeSpecialTechnic = customizeSpecialTechnicRepository.saveAll(
      customizeRequest.customizeSpecialTechnic.map {
        val specialTechnicConfig =
          specialTechnicConfigRepository.findAllSpecialTechnicConfigById(it.specialTechnicConfigId)
        CustomizeSpecialTechnic(
          customizeId = customize.id,
          specialTechnicConfigId = it.specialTechnicConfigId,
          specialTechnicConfig = specialTechnicConfig.first()
        )
      }
    )
    val customizeCoating = customizeCoatingRepository.saveAll(
      customizeRequest.customizeCoating.map {
        val coatingConfig =
          modelSizeConfigCoatingRepository.findAllModelSizeConfigCoatingById(it.modelSizeConfigCoatingId)
        CustomizeCoating(
          customizeId = customize.id,
          modelSizeConfigCoatingId = it.modelSizeConfigCoatingId,
          modelSizeConfigCoating = coatingConfig.firstOrNull()
        )
      }
    )
    customize.customizeSpecialTechnic = customizeSpecialTechnic
    customize.customizeCoating = customizeCoating
    val savedCustomize = customizeRepository.save(customize)
    return savedCustomize.toCustomizeDto()
  }

  override fun getCustomizeById(id: Long): CustomizeDto {
    val customize = customizeRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลปรับแต่งสินค้า")
    }
    return customize.toCustomizeDto()
  }

  override fun findModelSizeIdBySize(
    width: Int,
    length: Int,
    height: Int,
    unfoldedHeight: Double,
    unfoldedWidth: Double,
    modelId: Long
  ): FindModelSizeIdDto {
    val nearestModelSize = findNearestModelSizeId(
      width, length, height, unfoldedHeight, unfoldedWidth, modelId
    )
    return FindModelSizeIdDto(
      modelId = modelId,
      width = width,
      height = height,
      length = length,
      modelSizeId = nearestModelSize.modelSizeId,
      unfoldedSizeId = nearestModelSize.unfoldedSizeId
    )
  }

  override fun updateCustomize(id: Long, customizeRequest: CustomizeRequest): CustomizeDto {
    val customize = customizeRepository.findById(id).orElseThrow {
      NotFoundException("ไม่พบข้อมูลปรับแต่งสินค้า")
    }
    val model = modelRepository.findById(customizeRequest.modelId).orElseThrow {
      NotFoundException("ไม่พบข้อมูลโมเดล")
    }
    val modelSize = modelSizeRepository.findById(customizeRequest.modelSizeId).orElseThrow {
      NotFoundException("ไม่พบข้อมูลขนาดโมเดล")
    }
    val materialConfig = materialConfigRepository.findById(customizeRequest.materialConfigId).orElseThrow {
      NotFoundException("ไม่พบข้อมูลวัสดุ")
    }
    val printing = printingRepository.findById(customizeRequest.printingId).orElseThrow {
      NotFoundException("ไม่พบข้อมูลระบบพิมพ์")
    }
    val modelSizeConfigDetail = modelSizeConfigDetailRepository.findById(customizeRequest.modelSizeConfigDetailId)
      .orElseThrow { NotFoundException("ไม่พบข้อมูลรายละเอียดโมเดล") }
    val productPeriod = productPeriodRepository.findById(customizeRequest.productPeriodId).orElseThrow {
      NotFoundException("ไม่พบข้อมูลระยะเวลาการผลิต")
    }
    val unfolded = unfoldedSizeRepository.findById(customizeRequest.unfoldedSizeId).orElseThrow {
      NotFoundException("ไม่พบขนาดกางออก")
    }

    val periodDate = LocalDate.now().plusDays(productPeriod.maxPeriod.toLong())
    val period: Date = Date.from(periodDate.atStartOfDay(ZoneId.systemDefault()).toInstant())

    customize.modelId = model.id
    customize.model = model
    customize.modelSizeId = modelSize.id
    customize.materialConfigId = materialConfig.id
    customize.materialConfig = materialConfig
    customize.printingId = printing.id
    customize.printing = printing
    customize.modelSizeConfigDetailId = modelSizeConfigDetail.id
    customize.modelSizeConfigDetail = modelSizeConfigDetail
    customize.width = customizeRequest.width
    customize.length = customizeRequest.length
    customize.height = customizeRequest.height
    customize.unfoldedSizeId = unfolded.id
    customize.unfoldedSize = unfolded
    customize.productPeriodId = productPeriod.id
    customize.productPeriod = productPeriod
    customize.periodDate = period
    customize.sampleProduct = customizeRequest.sampleProduct
    customize.shippingType = customizeRequest.shippingType
    customize.personPickupName = customizeRequest.personPickupName
    customize.personPickupTell = customizeRequest.personPickupTell
    customize.zipcode = customizeRequest.zipcode
    customize.isArtwork = customizeRequest.isArtwork
    customize.dieLine = customizeRequest.dieLine

    val oldCustomSpecialTechnic = customizeSpecialTechnicRepository.findAllByCustomizeId(id)
    customizeSpecialTechnicRepository.deleteAll(oldCustomSpecialTechnic)
    val customizeSpecialTechnic = customizeSpecialTechnicRepository.saveAll(
      customizeRequest.customizeSpecialTechnic.map {
        val specialTechnicConfig =
          specialTechnicConfigRepository.findById(it.specialTechnicConfigId).orElseThrow{
            NotFoundException("ไม่พบข้อมูลเทคนิคพิเศษ")
          }
        CustomizeSpecialTechnic(
          customizeId = customize.id,
          specialTechnicConfigId = it.specialTechnicConfigId,
          specialTechnicConfig = specialTechnicConfig
        )
      }
    )

    val oldCustomCoating = customizeCoatingRepository.findByCustomizeId(id)
    customizeCoatingRepository.deleteAll(oldCustomCoating)
    val customizeCoating = customizeCoatingRepository.saveAll(
      customizeRequest.customizeCoating.map {
        val coatingConfig =
          modelSizeConfigCoatingRepository.findById(it.modelSizeConfigCoatingId).orElseThrow{
            NotFoundException("ไม่พบข้อมูลการเคลือบ")
          }
        CustomizeCoating(
          customizeId = customize.id,
          modelSizeConfigCoatingId = it.modelSizeConfigCoatingId,
          modelSizeConfigCoating = coatingConfig
        )
      }
    )
    customize.customizeSpecialTechnic = customizeSpecialTechnic
    customize.customizeCoating = customizeCoating
    val savedCustomize = customizeRepository.save(customize)
    return savedCustomize.toCustomizeDto()
  }

  fun findNearestModelSizeId(
    width: Int,
    length: Int,
    height: Int,
    unfoldedHeight: Double,
    unfoldedWidth: Double,
    modelId: Long
  ): CalModelSizeIdDto {
    val model = modelRepository.findById(modelId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลโมเดล")
    }
    val unfoldedSize = unfoldedSizeRepository.findAllOrderByWidthAndHeightDesc()

    val filteredUnfold = unfoldedSize.firstOrNull { uf ->
      val unfoldedMatch =
        (uf.width >= unfoldedWidth && uf.height >= unfoldedHeight) ||
            (uf.width >= unfoldedHeight && uf.height >= unfoldedWidth)
      unfoldedMatch
    }
    if (filteredUnfold == null) {
      throw NotFoundException("ไม่พบขนาดโมเดลที่ตรงกับ ขนาดกางออก")
    }

    val modelSize = modelSizeRepository.findAllByUnfoldedSizeIdAndModelId(filteredUnfold.id, model.id)
    if (modelSize.isEmpty()) {
      throw NotFoundException("ไม่พบข้อมูลขนาดโมเดล")
    }
    val isThreeD = modelSize.first().isThreeD
    val filteredModelSizes = modelSize.filter { ms ->
      val dimensionMatch = width <= ms.width.toInt() &&
          height <= ms.height.toInt() &&
          (!isThreeD || (ms.length != null && length <= (ms.length!!)))
      dimensionMatch
    }
    if (filteredModelSizes.isEmpty()) {
      throw NotFoundException("ไม่พบราคาจากขนาดที่คุณเลือก กรุณาตรวจสอบขนาดอีกครั้ง")
    }

    var newModelSize: ModelSize
    var minModelSize: Double


    if (isThreeD) {
      newModelSize = filteredModelSizes[0]
      minModelSize =
        (newModelSize.width - width) +
            (newModelSize.height - height) +
            (newModelSize.length!! - length)

      for (i in 1 until filteredModelSizes.size) {
        val size = filteredModelSizes[i]
        val diff =
          (size.width - width) +
              (size.height - height) +
              (size.length!! - length)

        if (diff < minModelSize) {
          minModelSize = diff
          newModelSize = size
        }
      }
    } else {
      newModelSize = filteredModelSizes[0]
      minModelSize =
        (newModelSize.width - width) +
            (newModelSize.height - height)
      for (i in 1 until filteredModelSizes.size) {
        val size = filteredModelSizes[i]
        val diff =
          (size.width - width) +
              (size.height - height)

        if (diff < minModelSize) {
          minModelSize = diff
          newModelSize = size
        }
      }
    }
    return CalModelSizeIdDto(
      modelSizeId = newModelSize.id,
      unfoldedSizeId = filteredUnfold.id
    )
  }
}