package com.lucablock.digiboxapi.coating.repository

import com.lucablock.digiboxapi.coating.dto.CoatingDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository

@Repository
interface CoatingRepositoryCustom {
  fun getCoatingPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?
  ): Page<CoatingDto>
}