package com.lucablock.digiboxapi.coating.repository

import com.lucablock.digiboxapi.entity.Coating
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface CoatingRepository : JpaRepository<Coating, Int>,
  CoatingRepositoryCustom {
  fun existsByNameIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  abstract fun existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String, id: Int): Boolean
  fun findAllByOrderByIdAsc(): List<Coating>
  fun findAllByNameContainingIgnoreCase(name: String, pageable: Pageable): Page<Coating>

  @Query(
    value = "SELECT * FROM coating c " +
        "JOIN product_category_coating pcc ON c.id = pcc.coating_id " +
        "WHERE pcc.product_category_id = :categoryId",
    nativeQuery = true
  )
  fun findAllByCategoryId(categoryId: Long): List<Coating>
  fun existsByIdIn(ids: List<Int>): <PERSON><PERSON>an
}