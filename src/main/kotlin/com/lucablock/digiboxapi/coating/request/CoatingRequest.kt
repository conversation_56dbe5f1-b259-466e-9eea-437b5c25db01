package com.lucablock.digiboxapi.coating.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.*

class CoatingRequest {
  @NotBlank(message = "กรุณากรอกชื่อเคลือบ")
  var name: String = ""

  var description : String = ""

  @NotNull(message = "กรุณากรอกราคาเคลือบ")
//  @DecimalMin(value = "0.1", message = "ราคาเริ่มต้น 0.1 บาท")
  @PositiveOrZero(message = "ราคาเคลือบต้องไม่ติดลบ")
  val price: Double = 0.0

  @NotBlank(message = "กรุณาระบุ url รูปภาพ")
  var imageUrl: String = ""

//  @NotEmpty(message = "ต้องระบุหมวดหมู่สินค้า")
//  var categories: List<Long> = mutableListOf()

  @JsonProperty
  val isActive: Boolean = true
}
