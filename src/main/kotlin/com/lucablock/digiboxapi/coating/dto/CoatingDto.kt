package com.lucablock.digiboxapi.coating.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class CoatingDto
@QueryProjection
constructor(
  val id: Int,
  val name: String,
  val description : String?,
  val price: Double,
  val imageUrl: String,
  val isActive: Boolean,
  val isDeleted: Boolean,
//  val categories: List<ProductCategoryDto> = mutableListOf(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
)
data class CoatingListDto
@QueryProjection
constructor(
  val id: Int,
  val name: String,
  val price: Double,
  val imageUrl: String,
  val isActive: Boolean,
)