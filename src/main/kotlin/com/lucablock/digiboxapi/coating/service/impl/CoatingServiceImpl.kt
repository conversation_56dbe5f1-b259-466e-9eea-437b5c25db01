package com.lucablock.digiboxapi.coating.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.coating.dto.CoatingDto
import com.lucablock.digiboxapi.coating.repository.CoatingRepository
import com.lucablock.digiboxapi.coating.request.CoatingRequest
import com.lucablock.digiboxapi.coating.service.CoatingService
import com.lucablock.digiboxapi.entity.Coating
import com.lucablock.digiboxapi.entity.ProductCategoryCoating
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.item.repository.ItemRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryCoatingRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class CoatingServiceImpl @Autowired internal constructor(
  private val coatingRepository: CoatingRepository,
  private val productCategoryRepository: ProductCategoryRepository,
  private val productCategoryCoatingRepository: ProductCategoryCoatingRepository,
  private val itemRepository: ItemRepository,
  private val s3Service: S3Service
) : CoatingService {

  @Transactional
  override fun createCoating(coatingRequest: CoatingRequest): CoatingDto {
    coatingRepository.existsByNameIgnoreCaseAndIsDeletedFalse(coatingRequest.name)
      .let { if (it) throw BadRequestException("ข้อมูลเคลือบนี้มีอยู่แล้ว") }

    val savedCoating = Coating(
        name = coatingRequest.name,
        description = coatingRequest.description,
        price = coatingRequest.price,
        imageUrl = coatingRequest.imageUrl,
    )

    val newFileUrl = s3Service.moveFile(coatingRequest.imageUrl).url
    savedCoating.imageUrl = newFileUrl
    val updateCoating = coatingRepository.save(savedCoating)

    return updateCoating.toCoatingDto()
  }

  override fun findAllCoatings(category: Long?): List<CoatingDto> {
    println(category)
    if (category != null) {
      println("by cate")
      return coatingRepository.findAllByCategoryId(category).map { it.toCoatingDto() }
    }
    return coatingRepository.findAllByOrderByIdAsc().map { it.toCoatingDto() }
  }

  override fun searchCoating(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<CoatingDto> {
    var term = ""
    if (searchTerm != null) {
      term = searchTerm
    }
    return coatingRepository.findAllByNameContainingIgnoreCase(term, pageable)
      .map { it.toCoatingDto() }
  }

  override fun getCoatingPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<CoatingDto> {
    return coatingRepository.getCoatingPage(pageable, ascending, search, isActive)
  }

  override fun findCoatingById(id: Int): CoatingDto {
    return coatingRepository.findById(id).orElseThrow { NotFoundException("ไม่มีข้อมูลเคลือบ") }
      .toCoatingDto()
  }

  @Transactional
  override fun updateCoating(id: Int, coatingRequest: CoatingRequest): CoatingDto {
    val coating =
      coatingRepository.findById(id).orElseThrow { NotFoundException("ไม่มีข้อมูลเคลือบ") }

    coatingRepository.existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(coatingRequest.name, id)
      .let { if (it) throw BadRequestException("ข้อมูลเคลือบนี้มีอยู่แล้ว") }

    coating.name = coatingRequest.name
    coating.description = coatingRequest.description
    coating.price = coatingRequest.price
    coating.isActive = coatingRequest.isActive

    if (coatingRequest.imageUrl != coating.imageUrl) {
      s3Service.deleteFile(coating.imageUrl)
      val newFileUrl = s3Service.moveFile(coatingRequest.imageUrl).url
      coating.imageUrl = newFileUrl
    }
//
//    productCategoryCoatingRepository.deleteAllByCoatingId(id.toLong())
//    val category = coatingRequest.categories.map {
//      productCategoryRepository.findById(it)
//        .orElseThrow { NotFoundException("ไม่มีหมวดหมู่ผลิตภัณฑ์") }
//
//      ProductCategoryCoating(
//        coatingId = id.toLong(),
//        productCategoryId = it,
//      )
//    }
//
//    category.let { productCategoryCoatingRepository.saveAll(it) }

    val updatedCoating = coatingRepository.save(coating)
    return updatedCoating.toCoatingDto()
  }

  override fun deleteCoating(id: Int): Boolean {
    val coating =
      coatingRepository.findById(id).orElseThrow { NotFoundException("ไม่มีข้อมูลเคลือบ") }

//    itemRepository.existsItemByCoatingId(coating.id)
//      .let { if (it) throw BadRequestException("การเคลือบนี้ถูกใช้งานอยู่ ไม่สามารถลบได้") }

//    s3Service.deleteFile(coating.imageUrl)
//    coatingRepository.deleteById(id)
    coating.isDeleted = true
    coatingRepository.save(coating)
    return true
  }

}
