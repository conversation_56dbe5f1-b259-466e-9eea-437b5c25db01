package com.lucablock.digiboxapi.coating.service.impl

import com.lucablock.digiboxapi.coating.dto.CoatingDto
import com.lucablock.digiboxapi.coating.repository.CoatingRepositoryCustom
import com.lucablock.digiboxapi.entity.QCoating
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CoatingRepositoryImpl : CoatingRepositoryCustom{
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }
  private val qCoating = QCoating.coating
  override fun getCoatingPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?
  ): Page<CoatingDto> {
    var criteria = qCoating.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qCoating.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qCoating.name.containsIgnoreCase(search))
    }

    val sort = if (ascending) {
      qCoating.id.asc()
    } else {
      qCoating.id.desc()
    }
    val query = queryFactory
      .select(qCoating)
      .from(qCoating)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toCoatingDto()
      }

    val total = queryFactory
      .select(qCoating)
      .from(qCoating)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}