package com.lucablock.digiboxapi.coating.service

import com.lucablock.digiboxapi.coating.dto.CoatingDto
import com.lucablock.digiboxapi.coating.request.CoatingRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile

interface CoatingService {
  fun createCoating(coatingRequest: CoatingRequest): CoatingDto
  fun findAllCoatings(category: Long?): List<CoatingDto>
  fun searchCoating(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<CoatingDto>

  fun getCoatingPage(
    pageable: Pageable,
    ascending: <PERSON><PERSON><PERSON>,
    search: String?,
    isActive : Boolean?,
  ): Page<CoatingDto>

  fun findCoatingById(id: Int): CoatingDto
  fun updateCoating(id: Int, coatingRequest: CoatingRequest): CoatingDto
  fun deleteCoating(id: Int) : <PERSON><PERSON><PERSON>
}