package com.lucablock.digiboxapi.coating.controller

import com.lucablock.digiboxapi.coating.request.CoatingRequest
import com.lucablock.digiboxapi.coating.service.CoatingService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping("api")
class CoatingController {
  private val logger: Logger = LoggerFactory.getLogger(CoatingController::class.java)

  @Autowired
  lateinit var coatingService: CoatingService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/coating")
  fun createCoating(
    @Valid @RequestBody coatingRequest: CoatingRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลเคลือบสำเร็จ",
          coatingService.createCoating(coatingRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, "ข้อมูลเคลือบนี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error("create coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีหมวดหมู่ผลิตภัณฑ์"))
    } catch (e: Exception) {
      logger.error("create coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถสร้างข้อมูลเคลือบ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/coating")
  fun findAllCoating(
    @RequestParam("category", required = false) category: Long?,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          coatingService.findAllCoatings(category)
        )
      )
    } catch (e: Exception) {
      logger.error("find all coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/search")
  fun searchCoating(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("searchTerm", required = false) searchTerm: String?,
    @RequestParam("sortField", required = false) sortField: String?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหาการเคลือบสำเร็จ",
          coatingService.searchCoating(pageable, ascending, searchTerm, sortField)
        )
      )
    } catch (e: Exception) {
      logger.error("search coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/coating/{id}")
  fun findCoatingById(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          coatingService.findCoatingById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find coating by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลเคลือบ"))
    } catch (e: Exception) {
      logger.error("find coating by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/coating/{id}")
  fun updateCoating(
    @PathVariable id: Int,
    @Valid @RequestBody coatingRequest: CoatingRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลเคลือบสินค้าสำเร็จ",
          coatingService.updateCoating(id, coatingRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("update coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลเคลือบนี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error("update coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลเคลือบ"))
    } catch (e: Exception) {
      logger.error("update coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลเคลือบ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/coating/{id}")
  fun deleteCoating(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลสำเร็จ",
          coatingService.deleteCoating(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลเคลือบ"))
    } catch (e: BadRequestException) {
      logger.error("delete coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลเคลือบนี้ถูกใช้งานอยู่ ไม่สามารถลบได้"))
    } catch (e: Exception) {
      logger.error("delete coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/coating/page")
  fun findCoatingPage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = coatingService.getCoatingPage(
            pageable,
            ascending,
            search,
            isActive,
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find coating error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

}