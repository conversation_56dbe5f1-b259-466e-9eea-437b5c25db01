package com.lucablock.digiboxapi.coupon.repository

import com.lucablock.digiboxapi.entity.Coupon
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface CouponRepository: JpaRepository<Coupon, Long>, CouponRepositoryCustom {
  fun existsCouponByNameIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  fun existsCouponByCodeIgnoreCaseAndIsDeletedFalse(code: String): Boolean
  fun findByIdAndIsDeletedFalse(id: Long): Optional<Coupon>
  fun findByNameIgnoreCaseAndIdNot(name: String, id: Long): Optional<Coupon>
  fun findByCodeIgnoreCaseAndIdNot(code: String, id: Long): Optional<Coupon>
  fun findAllByIdIn(ids: List<Long>): MutableList<Coupon>
}