package com.lucablock.digiboxapi.coupon.dto

import com.lucablock.digiboxapi.couponcategory.dto.CouponCategoryDto
import com.lucablock.digiboxapi.coupontype.dto.CouponTypeDto
import com.lucablock.digiboxapi.customerlevel.dto.CustomerLevelDto
import com.querydsl.core.annotations.QueryProjection
import java.util.Date

data class CouponDto
@QueryProjection
constructor(
  val id: Long,
  val name: String,
  val code: String,
  val description: String,
  val couponCategory: CouponCategoryDto? = null,
  val customerLevel: CustomerLevelDto? = null,
  val couponType: CouponTypeDto? = null,
  val percentage: Int? = null,
  val maxPrice: Double? = null,
  val minPrice: Double? = null,
  val maxUsage: Int,
  val startDate: Date,
  val endDate: Date,
  val isActive: Boolean = true,
  val createdDate: Date,
)
