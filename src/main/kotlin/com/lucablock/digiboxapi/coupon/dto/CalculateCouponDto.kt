package com.lucablock.digiboxapi.coupon.dto

import com.lucablock.digiboxapi.couponcategory.dto.CouponCategoryDto
import com.lucablock.digiboxapi.coupontype.dto.CouponTypeDto
import com.querydsl.core.annotations.QueryProjection

data class CalculateCouponDto
@QueryProjection
constructor(
  val calCouponDiscount: List<CalculateCouponDiscountResponse>
)

data class CalculateCouponDiscountResponse
@QueryProjection
constructor (
  val couponId: Long,
  val couponType: CouponTypeDto,
  val couponCategory: CouponCategoryDto,
  val couponDiscount: Double,
)