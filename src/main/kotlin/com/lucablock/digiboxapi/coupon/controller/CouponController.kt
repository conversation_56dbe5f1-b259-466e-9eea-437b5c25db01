package com.lucablock.digiboxapi.coupon.controller

import com.lucablock.digiboxapi.coupon.request.CalculateCouponDiscountRequest
import com.lucablock.digiboxapi.coupon.request.CreateCouponRequest
import com.lucablock.digiboxapi.coupon.request.UsedCouponRequest
import com.lucablock.digiboxapi.coupon.service.CouponService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import jakarta.validation.constraints.Max
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
class CouponController @Autowired constructor(
  private val couponService: CouponService
) {
  private val logger = LoggerFactory.getLogger(CouponController::class.java)

  @PostMapping("/admin/coupon")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun createCoupon(
    @Valid @RequestBody request: CreateCouponRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "สร้างคูปองสำเร็จ",
          data = couponService.createCoupon(request)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "คูปองไม่ถูกต้อง")
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบข้อมูลที่จำเป็นในการสร้างคูปอง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการสร้างคูปอง")
      )
    }
  }

  @PutMapping("/admin/coupon/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun updateCoupon(
    @PathVariable id: Long,
    @Valid @RequestBody request: CreateCouponRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "อัปเดตคูปองสำเร็จ",
          data = couponService.updateCoupon(id, request)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "คูปองไม่ถูกต้อง")
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบคูปองที่ต้องการอัปเดต")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการอัปเดตคูปอง")
      )
    }
  }

  @DeleteMapping("/admin/coupon/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun deleteCoupon(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ลบคูปองสำเร็จ",
          data = couponService.deleteCoupon(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบคูปองที่ต้องการลบ")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการลบคูปอง")
      )
    }
  }

  @GetMapping("/web/coupon/{id}")
  fun getCouponById(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลคูปองสำเร็จ",
          data = couponService.getCouponById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบคูปองที่ต้องการดึงข้อมูล")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงข้อมูลคูปอง")
      )
    }
  }

  @GetMapping("/web/coupon/page")
  fun getPaginationCoupon(
    @RequestParam ascending: Boolean = true,
    @RequestParam(required = false) search: String? = null,
    @RequestParam(defaultValue = "0") page: Int = 0,
    @RequestParam(defaultValue = "10") @Max(100) size: Int = 10,
    @RequestParam(required = false) isActive: Boolean? = null,
    @RequestParam(required = false, defaultValue = "Anonymous") customerLevel: String,
  ): ResponseEntity<Any> {
    return try {
      val pageable = PageRequest.of(page, size)
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลคูปองสำเร็จ",
          data = couponService.getPaginationCoupon(pageable, ascending, search, isActive, customerLevel)
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงข้อมูลคูปอง")
      )
    }
  }

  @PutMapping("/admin/coupon/status/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun changeCouponStatus(
    @PathVariable id: Long,
    @RequestParam isActive: Boolean
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "เปลี่ยนสถานะคูปองสำเร็จ",
          data = couponService.changeCouponStatus(id, isActive)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบคูปองที่ต้องการเปลี่ยนสถานะ")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการเปลี่ยนสถานะคูปอง")
      )
    }
  }

  @PostMapping("/web/coupon/calculate")
  fun calculateCouponDiscount(
    @Valid @RequestBody request: CalculateCouponDiscountRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "คำนวณส่วนลดคูปองสำเร็จ",
          data = couponService.calculateCouponDiscount(request)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบคูปองที่ต้องการคำนวณส่วนลด")
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดในการคำนวณส่วนลดคูปอง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการคำนวณส่วนลดคูปอง")
      )
    }
  }

  @PutMapping("/web/coupon/used")
  fun usedCouponDiscount(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody request: UsedCouponRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ใช้คูปองสำเร็จ",
          data = couponService.usedCoupon(userPrincipal, request)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบคูปองที่ต้องการใช้")
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดในการใช้คูปอง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการใช้คูปอง")
      )
    }
  }
}