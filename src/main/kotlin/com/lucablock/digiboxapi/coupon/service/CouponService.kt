package com.lucablock.digiboxapi.coupon.service

import com.lucablock.digiboxapi.coupon.dto.CalculateCouponDto
import com.lucablock.digiboxapi.coupon.dto.CouponDto
import com.lucablock.digiboxapi.coupon.request.CalculateCouponDiscountRequest
import com.lucablock.digiboxapi.coupon.request.CreateCouponRequest
import com.lucablock.digiboxapi.coupon.request.GenerateCouponRequest
import com.lucablock.digiboxapi.coupon.request.UsedCouponRequest
import com.lucablock.digiboxapi.entity.Coupon
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CouponService {
  fun createCoupon(request: CreateCouponRequest): CouponDto
  fun updateCoupon(id: Long, request: CreateCouponRequest): CouponDto
  fun deleteCoupon(id: Long): Boolean
  fun getCouponById(id: Long): CouponDto
  fun getPaginationCoupon(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
    customerLevel: String
  ): Page<CouponDto>
  fun changeCouponStatus(id: Long, isActive: Boolean): Boolean
  fun calculateCouponDiscount(
    request: CalculateCouponDiscountRequest
  ): CalculateCouponDto
  fun usedCoupon(
    userPrincipal: UserPrincipal,
    request: UsedCouponRequest
  ): Boolean
  fun generateCoupon(request: GenerateCouponRequest): Coupon
}