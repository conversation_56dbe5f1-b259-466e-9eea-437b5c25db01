package com.lucablock.digiboxapi.coupon.service.impl

import com.lucablock.digiboxapi.coupon.dto.CalculateCouponDiscountResponse
import com.lucablock.digiboxapi.coupon.dto.CalculateCouponDto
import com.lucablock.digiboxapi.coupon.dto.CouponDto
import com.lucablock.digiboxapi.coupon.repository.CouponRepository
import com.lucablock.digiboxapi.coupon.request.CalculateCouponDiscountRequest
import com.lucablock.digiboxapi.coupon.request.CreateCouponRequest
import com.lucablock.digiboxapi.coupon.request.GenerateCouponRequest
import com.lucablock.digiboxapi.coupon.request.UsedCouponRequest
import com.lucablock.digiboxapi.coupon.service.CouponService
import com.lucablock.digiboxapi.couponcategory.repository.CouponCategoryRepository
import com.lucablock.digiboxapi.couponproduct.repository.CouponProductRepository
import com.lucablock.digiboxapi.couponspecialtechnic.repository.CouponSpecialTechnicRepository
import com.lucablock.digiboxapi.coupontype.repository.CouponTypeRepository
import com.lucablock.digiboxapi.couponuser.repository.CouponUserRepository
import com.lucablock.digiboxapi.couponusagelog.repository.CouponUsageLogRepository
import com.lucablock.digiboxapi.customerlevel.repository.CustomerLevelRepository
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.order.repository.OrderRepository
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.text.DecimalFormat
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@Service
class CouponServiceImpl @Autowired constructor(
  private val couponRepository: CouponRepository,
  private val couponCategoryRepository: CouponCategoryRepository,
  private val couponTypeRepository: CouponTypeRepository,
  private val customerLevelRepository: CustomerLevelRepository,
  private val couponUserRepository: CouponUserRepository,
  private val orderRepository: OrderRepository,
  private val couponProductRepository: CouponProductRepository,
  private val couponSpecialTechnicRepository: CouponSpecialTechnicRepository,
  private val couponUsageLogRepository: CouponUsageLogRepository,
) : CouponService {
  @Transactional(rollbackFor = [BadRequestException::class, NotFoundException::class, Exception::class])
  override fun createCoupon(request: CreateCouponRequest): CouponDto {
    val trimmedName = request.name.trim()
    val trimmedCode = request.code.trim()

    if (request.startDate.after(request.endDate)) {
      throw BadRequestException("วันที่เริ่มต้นต้องไม่หลังจากวันที่สิ้นสุด")
    }

    if (couponRepository.existsCouponByNameIgnoreCaseAndIsDeletedFalse(trimmedName)) {
      throw BadRequestException("คูปองนี้มีอยู่แล้ว")
    }

    if (couponRepository.existsCouponByCodeIgnoreCaseAndIsDeletedFalse(trimmedCode)) {
      throw BadRequestException("รหัสคูปองนี้มีอยู่แล้ว")
    }

    val couponCategory = couponCategoryRepository.findById(request.couponCategoryId).orElseThrow {
      throw NotFoundException("ไม่พบหมวดหมู่คูปอง")
    }

    val couponType = couponTypeRepository.findById(request.couponTypeId).orElseThrow {
      throw NotFoundException("ไม่พบประเภทคูปอง")
    }

    val customerLevel = customerLevelRepository.findById(request.customerLevelId).orElseThrow {
      throw NotFoundException("ไม่พบระดับลูกค้า")
    }

    val newCoupon = Coupon(
      name = trimmedName,
      code = trimmedCode,
      description = request.description,
      couponCategoryId = couponCategory.id,
      couponTypeId = couponType.id,
      percentage = request.percentage,
      maxPrice = request.maxPrice,
      minPrice = request.minPrice,
      customerLevelId = customerLevel.id,
      maxUsage = request.maxUsage,
      startDate = request.startDate,
      endDate = request.endDate,
    )

    val savedCoupon = try {
      couponRepository.save(newCoupon)
    } catch (ex: Exception) {
      throw BadRequestException("ไม่สามารถสร้างคูปองได้: ${ex.message}")
    }

    when (couponCategory.name.uppercase()) {
      "PRODUCT" -> {
        val productIds = if (request.productId.isNullOrEmpty()) {
          throw BadRequestException("กรุณาระบุรหัสสินค้า")
        } else {
          request.productId
        }

        productIds.forEach {productId ->
          val product = couponProductRepository.findById(productId).orElseThrow {
            NotFoundException("ไม่สามารถใช้คูปองกับสินค้านี้ได้")
          }

          try {
            couponProductRepository.save(
              CouponProduct(
                couponId = savedCoupon.id,
                productId = product.id
              )
            )
          } catch (ex: Exception) {
            throw BadRequestException("ไม่สามารถสร้างคูปองสำหรับสินค้านี้ได้: ${ex.message}")
          }
        }
      }

      "SPECIAL TECHNIC" -> {
        val technicIds = if (request.specialTechnicId.isNullOrEmpty()) {
          throw BadRequestException("กรุณาระบุรหัสเทคนิคพิเศษ")
        } else {
          request.specialTechnicId
        }

        technicIds.forEach { technicId ->
          val technic = couponSpecialTechnicRepository.findById(technicId).orElseThrow {
            throw NotFoundException("ไม่สามารถใช้คูปองกับเทคนิคพิเศษนี้ได้")
          }

          try {
            couponSpecialTechnicRepository.save(
              CouponSpecialTechnic(
                couponId = savedCoupon.id,
                specialTechnicId = technic.id
              )
            )
          } catch (ex: Exception) {
            throw BadRequestException("ไม่สามารถสร้างคูปองสำหรับเทคนิคพิเศษนี้ได้: ${ex.message}")
          }
        }
      }
    }

    return savedCoupon.toCouponDto()
  }

  @Transactional
  override fun updateCoupon(
    id: Long,
    request: CreateCouponRequest
  ): CouponDto {
    couponTypeRepository.findByIdAndIsDeletedFalse(request.couponTypeId).orElseThrow {
      throw NotFoundException("ไม่พบประเภทคูปอง")
    }

    couponCategoryRepository.findByIdAndIsDeletedFalse(request.couponCategoryId).orElseThrow {
      throw NotFoundException("ไม่พบหมวดหมู่คูปอง")
    }

    customerLevelRepository.findById(request.customerLevelId).orElseThrow {
      throw NotFoundException("ไม่พบระดับลูกค้า")
    }

    val trimmedName = request.name.trim()
    val trimmedCode = request.code.trim()

    val coupon = couponRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบคูปอง")
    }

    val now = Date()
    if (coupon.isActive && now.after(request.startDate) && now.before(request.endDate)) {
      throw BadRequestException("ไม่สามารถแก้ไขคูปองที่กำลังใช้งานได้")
    }

    couponRepository.findByNameIgnoreCaseAndIdNot(trimmedName, id).ifPresent {
      throw BadRequestException("คูปองนี้มีอยู่แล้ว")
    }

    couponRepository.findByCodeIgnoreCaseAndIdNot(trimmedCode, id).ifPresent {
      throw BadRequestException("รหัสคูปองนี้มีอยู่แล้ว")
    }

    coupon.name = trimmedName
    coupon.code = trimmedCode
    coupon.description = request.description
    coupon.couponCategoryId = request.couponCategoryId
    coupon.couponTypeId = request.couponTypeId
    coupon.percentage = request.percentage
    coupon.maxPrice = request.maxPrice
    coupon.minPrice = request.minPrice
    coupon.customerLevelId = request.customerLevelId
    coupon.maxUsage = request.maxUsage
    coupon.startDate = request.startDate
    coupon.endDate = request.endDate

    val updatedCoupon = couponRepository.save(coupon)
    return updatedCoupon.toCouponDto()
  }

  override fun deleteCoupon(id: Long): Boolean {
    val coupon = couponRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบคูปอง")
    }

    coupon.isDeleted = true
    couponRepository.save(coupon)
    return true
  }

  override fun getCouponById(id: Long): CouponDto {
    val coupon = couponRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบคูปอง")
    }
    return coupon.toCouponDto()
  }

  override fun getPaginationCoupon(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
    customerLevel: String
  ): Page<CouponDto> {
    return couponRepository.getPaginationCoupon(
      pageable,
      ascending,
      search,
      isActive,
      customerLevel
    )
  }

  override fun changeCouponStatus(id: Long, isActive: Boolean): Boolean {
    val coupon = couponRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบคูปอง")
    }

    coupon.isActive = isActive
    couponRepository.save(coupon)
    return true
  }

  @Transactional
  override fun calculateCouponDiscount(
    request: CalculateCouponDiscountRequest,
  ): CalculateCouponDto {
    val now = Date()

    val usedCategories = mutableSetOf<Long>()
    val totalCategories = couponCategoryRepository.count()

    val discountList = mutableListOf<CalculateCouponDiscountResponse>()

    request.couponId.map { couponId ->
      val coupon = couponRepository.findByIdAndIsDeletedFalse(couponId).orElseThrow {
        NotFoundException("ไม่พบคูปอง")
      }

      // ตรวจสอบสถานะ
      if (!coupon.isActive) throw BadRequestException("คูปองนี้ไม่สามารถใช้งานได้")
      if (now.before(coupon.startDate) || now.after(coupon.endDate)) {
        throw BadRequestException("คูปองนี้ไม่สามารถใช้งานได้ในช่วงเวลานี้")
      }

      if (coupon.maxUsage <= 0) {
        throw BadRequestException("คูปองนี้ถูกใช้งานครบจำนวนแล้ว")
      }

      if (usedCategories.contains(coupon.couponCategoryId)) {
        throw BadRequestException("ไม่สามารถใช้คูปองจากหมวดหมู่เดียวกันได้มากกว่าหนึ่งครั้ง")
      }

      if (usedCategories.size >= totalCategories) {
        throw BadRequestException("ไม่สามารถใช้คูปองได้มากกว่าจำนวนหมวดหมู่ที่มีอยู่")
      }

      usedCategories.add(coupon.couponCategoryId)

      // ตรวจสอบสิทธิ์ผู้ใช้
      val customerLevelId =
        customerLevelRepository.findById(request.customerLevelId)
          .orElseThrow { throw NotFoundException("ไม่พบระดับลูกค้า") }
      if (coupon.customerLevelId != customerLevelId.id) {
        throw BadRequestException("คูปองนี้ไม่สามารถใช้งานได้สำหรับระดับลูกค้านี้")
      }

      if (coupon.couponType?.name?.uppercase() != "FREE SHIPPING" && request.totalPrice < coupon.minPrice) {
        throw BadRequestException("ยอดรวมต้องมากกว่าหรือเท่ากับ ${coupon.minPrice} บาท เพื่อใช้คูปองนี้")
      }

      val discount = when (coupon.couponType?.name?.uppercase()) {
        "FREE SHIPPING" -> {
          if (request.shippingCost > 0) request.shippingCost else 0.0
        }

        "CREDIT" -> {
          val couponUserCredit = if (coupon.percentage == null || coupon.percentage!! <= 0) {
            request.totalPrice - coupon.maxPrice
          } else {
            percentDiscount(request.totalPrice, coupon.percentage!!, coupon.maxPrice)
          }

          couponUserCredit
        }

        else -> {
          if (coupon.percentage == null || coupon.percentage!! <= 0) {
            coupon.maxPrice
          } else {
            percentDiscount(request.totalPrice, coupon.percentage!!, coupon.maxPrice)
          }
        }
      }

      discountList.add(
        CalculateCouponDiscountResponse(
          couponId = coupon.id,
          couponType = coupon.couponType!!.couponTypeToDto(),
          couponCategory = coupon.couponCategory!!.couponCategoryDto(),
          couponDiscount = discount.decimalFormat(),
        )
      )
    }

    return CalculateCouponDto(discountList)
  }

  @Transactional
  override fun usedCoupon(
    userPrincipal: UserPrincipal,
    request: UsedCouponRequest
  ): Boolean {
    request.couponId.map { coupon ->
      val coupon = couponRepository.findByIdAndIsDeletedFalse(coupon).orElseThrow {
        NotFoundException("ไม่พบคูปอง")
      }

      val couponUser = couponUserRepository.findCouponUserByUserIdAndCouponIdAndIsUsedFalse(
        userPrincipal.getUserId(), coupon.id
      ).orElseThrow { NotFoundException("ไม่พบคูปองที่ใช้งานได้") }

      if (couponUser.isUsed) {
        throw BadRequestException("คูปองนี้ถูกใช้งานแล้ว")
      }

      if (coupon.maxUsage <= 0) {
        throw BadRequestException("คูปองนี้ถูกใช้งานครบจำนวนแล้ว")
      }

      orderRepository.findById(request.orderId).orElseThrow {
        NotFoundException("ไม่พบคำสั่งซื้อ")
      }

      try {
        // บันทึกการใช้คูปอง
        couponUser.isUsed = true

        if (coupon.couponType!!.name.uppercase() == "CREDIT") {
          try {
            couponUsageLogRepository.save(
              CouponUsageLog(
                couponId = coupon.id,
                userId = userPrincipal.getUserId(),
                orderId = request.orderId.toInt(),
                discountAmount = request.totalDiscount,
                totalPrice = request.totalPrice,
                creditAmount = request.creditAmount
              )
            )
          } catch (e: Exception) {
            throw BadRequestException("ไม่สามารถบันทึกการใช้งานคูปองได้: ${e.message}")
          }
        } else {
          try {
            couponUsageLogRepository.save(
              CouponUsageLog(
                couponId = coupon.id,
                userId = userPrincipal.getUserId(),
                orderId = request.orderId.toInt(),
                discountAmount = request.totalDiscount,
                totalPrice = request.totalPrice
              )
            )
          } catch (e: Exception) {
            throw BadRequestException("ไม่สามารถบันทึกการใช้งานคูปองได้: ${e.message}")
          }
        }

        couponUserRepository.save(couponUser)

        // ลดจำนวนการใช้งานของคูปอง
        coupon.maxUsage -= 1
        couponRepository.save(coupon)
      } catch (e: Exception) {
        throw BadRequestException("ไม่สามารถใช้คูปองได้: ${e.message}")
      }
    }

    return true
  }

  override fun generateCoupon(request: GenerateCouponRequest): Coupon {
    val coupon = couponRepository.findById(request.couponId).orElseThrow {
      NotFoundException("ไม่พบคูปองที่ต้องการสร้าง")
    }

    val newCoupon = Coupon(
      name = "Credit Coupon - ${coupon.name}",
      code = "${UUID.randomUUID()}",
      description = "Coupon generated for credit usage",
      couponCategoryId = coupon.couponCategory!!.id,
      couponTypeId = 3L,
      maxPrice = request.creditAmount,
      minPrice = coupon.minPrice,
      isPrivate = true,
      customerLevelId = coupon.customerLevelId,
      maxUsage = 1,
      startDate = Date(),
      endDate = Date.from(
        Instant.now().plus(30, ChronoUnit.DAYS)
      )
    )

    val savedCoupon = try {
      couponRepository.save(newCoupon)
    } catch (e: Exception) {
      throw BadRequestException("ไม่สามารถสร้างคูปองได้: ${e.message}")
    }

    val couponUser = CouponUser(
      userId = request.userLevel,
      couponId = savedCoupon.id,
    )

    couponUserRepository.save(couponUser)

    return savedCoupon
  }

  private fun percentDiscount(total: Double, percent: Int, maxPrice: Double): Double {
    return minOf(total * (percent / 100.0), maxPrice)
  }

  private fun Double.decimalFormat(pattern: String = "#.##"): Double {
    val df = DecimalFormat(pattern)
    return df.format(this).toDouble()
  }
}
