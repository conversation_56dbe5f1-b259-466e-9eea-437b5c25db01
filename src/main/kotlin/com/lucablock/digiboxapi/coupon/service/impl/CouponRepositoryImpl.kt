package com.lucablock.digiboxapi.coupon.service.impl

import com.lucablock.digiboxapi.coupon.dto.CouponDto
import com.lucablock.digiboxapi.coupon.repository.CouponRepositoryCustom
import com.lucablock.digiboxapi.entity.QCoupon
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CouponRepositoryImpl : CouponRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qCoupon = QCoupon.coupon

  override fun getPaginationCoupon(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
    customerLevel: String
  ): Page<CouponDto> {
    var criteria = qCoupon.isDeleted.isFalse.and(qCoupon.customerLevel.name.eq(customerLevel))
      .and(qCoupon.isPrivate.isFalse)

    if (!search.isNullOrBlank()) {
      criteria = criteria.and(qCoupon.name.containsIgnoreCase(search)
        .or(qCoupon.code.containsIgnoreCase(search)))
    }

    if (isActive != null) {
      criteria = criteria.and(qCoupon.isActive.eq(isActive))
    }

    val orderBy = if (ascending) {
      qCoupon.createdDate.asc()
    } else {
      qCoupon.createdDate.desc()
    }

    val query = queryFactory
      .select(qCoupon)
      .from(qCoupon)
      .where(criteria)
      .orderBy(orderBy)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()

    val total = queryFactory
      .selectFrom(qCoupon)
      .where(criteria)
      .fetchCount()

    return PageImpl(query.map { it.toCouponDto() }, pageable, total)
  }
}