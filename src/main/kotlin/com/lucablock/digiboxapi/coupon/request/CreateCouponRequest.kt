package com.lucablock.digiboxapi.coupon.request

import jakarta.validation.constraints.FutureOrPresent
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.util.*

data class CreateCouponRequest (
  @NotBlank(message = "ชื่อคูปองต้องไม่ว่าง")
  val name: String,
  @NotBlank(message = "โค้ดคูปองต้องไม่ว่าง")
  val code: String,
  @NotBlank(message = "คำอธิบายคูปองต้องไม่ว่าง")
  val description: String,
  @NotNull(message = "หมวดหมู่คูปองต้องไม่ว่าง")
  @Min(1, message = "ต้องเลือกหมวดหมู่คูปอง")
  val couponCategoryId: Long = 0,
  @NotNull(message = "ระดับลูกค้าต้องไม่ว่าง")
  @Min(1, message = "ต้องเลือกระดับลูกค้า")
  val customerLevelId: Long = 0,
  @NotNull(message = "ประเภทคูปองต้องไม่ว่าง")
  @Min(1, message = "ต้องเลือกประเภทคูปอง")
  val couponTypeId: Long = 0,
  @Min(0, message = "Percentage ต้องไม่น้อยกว่า 0")
  @Max(100, message = "Percentage ต้องไม่เกิน 100")
  val percentage: Int? = null,
  @Min(1, message = "Max price ต้องไม่น้อยกว่า 1")
  val maxPrice: Double,
  @Min(1, message = "Min price ต้องไม่น้อยกว่า 1")
  val minPrice: Double,
  @Min(1, message = "Max usage ต้องไม่น้อยกว่า 1")
  val maxUsage: Int = 0,
  @NotNull(message = "วันที่เริ่มต้นคูปองต้องไม่ว่าง")
  @FutureOrPresent(message = "วันที่เริ่มต้นคูปองต้องไม่เป็นอดีต")
  val startDate: Date = Date(),
  @NotNull(message = "วันที่สิ้นสุดคูปองต้องไม่ว่าง")
  val endDate: Date = Date(),
  val productId: List<Long>? = mutableListOf(),
  val specialTechnicId: List<Long>? = mutableListOf(),
)