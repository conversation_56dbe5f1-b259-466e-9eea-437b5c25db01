package com.lucablock.digiboxapi.productperiod.dto

import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class ProductPeriodDto
@QueryProjection constructor(
  val id: Long,
  val name: String,
  val price: Double,
  val minPeriod: Int,
  val maxPeriod: Int,
  val imageUrl: String,
  val isActive : Boolean
)
data class ProductPeriodListDto
@QueryProjection constructor(
  val id: Long,
  val name: String,
  val price: Double,
  val imageUrl: String,
)