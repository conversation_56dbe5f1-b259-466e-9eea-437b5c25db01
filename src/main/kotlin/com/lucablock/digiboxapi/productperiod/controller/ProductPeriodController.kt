package com.lucablock.digiboxapi.productperiod.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.productperiod.request.ProductPeriodRequest
import com.lucablock.digiboxapi.productperiod.service.ProductPeriodService
import com.lucablock.digiboxapi.producttag.request.ProductTagRequest
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class ProductPeriodController @Autowired internal constructor(
  private val productPeriodService: ProductPeriodService
)  {
  private val logger: Logger = LoggerFactory.getLogger(ProductPeriodController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/product-period")
  fun createProductPeriod(
    @Valid @RequestBody productPeriodRequest: ProductPeriodRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างข้อมูลระยะเวลาการผลิตสำเร็จ",
          data = productPeriodService.createProductPeriod(productPeriodRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลระยะเวลาการผลิตซ้ำ"))
    } catch (e: Exception) {
      logger.error("create product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลระยะเวลาการผลิต กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/product-period/{id}")
  fun updateProductPeriod(
    @PathVariable id: Long,
    @Valid @RequestBody productPeriodRequest: ProductPeriodRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "บันทึกข้อมูลระยะเวลาการผลิตสำเร็จ",
          data = productPeriodService.updateProductPeriod(id, productPeriodRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลระยะเวลาการผลิต"))
    } catch (e: BadRequestException) {
      logger.error("update product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลระยะเวลาการผลิตซ้ำ"))
    } catch (e: Exception) {
      logger.error("update product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลระยะเวลาการผลิต กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-period/{id}")
  fun findProductPeriodById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = productPeriodService.getProductPeriodById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find product period by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลระยะเวลาการผลิต"))
    } catch (e: Exception) {
      logger.error("find product period by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-period")
  fun findAllProductPeriod(): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = productPeriodService.getAllProductPeriod()
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find product period by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลระยะเวลาการผลิต"))
    } catch (e: Exception) {
      logger.error("find product period by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/product-period/{id}")
  fun deleteProductPeriod(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบข้อมูลสำเร็จ",
          data = productPeriodService.deleteProductPeriod(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลระยะเวลาการผลิต"))
    } catch (e: BadRequestException) {
      logger.error("delete product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ มีการใช้งานอยู่"))
    } catch (e: Exception) {
      logger.error("delete product period error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}