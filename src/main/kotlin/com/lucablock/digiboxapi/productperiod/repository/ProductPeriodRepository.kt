package com.lucablock.digiboxapi.productperiod.repository

import com.lucablock.digiboxapi.entity.ProductPeriod
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ProductPeriodRepository : JpaRepository<ProductPeriod, Long> {
  fun existsProductPeriodsByNameContainsIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  fun existsProductPeriodsByNameContainsIgnoreCaseAndIdNotAndIsDeletedFalse(name: String, id: Long): Boolean
  fun findAllByIsDeletedFalse(): List<ProductPeriod>

}