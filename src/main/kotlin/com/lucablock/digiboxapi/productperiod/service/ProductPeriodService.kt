package com.lucablock.digiboxapi.productperiod.service

import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodDto
import com.lucablock.digiboxapi.productperiod.request.ProductPeriodRequest

interface ProductPeriodService {
  fun createProductPeriod(productPeriodRequest: ProductPeriodRequest):ProductPeriodDto
  fun updateProductPeriod(id:Long,productPeriodRequest: ProductPeriodRequest):ProductPeriodDto
  fun getProductPeriodById(id:Long):ProductPeriodDto
  fun getAllProductPeriod():List<ProductPeriodDto>
  fun deleteProductPeriod(id:Long)
}