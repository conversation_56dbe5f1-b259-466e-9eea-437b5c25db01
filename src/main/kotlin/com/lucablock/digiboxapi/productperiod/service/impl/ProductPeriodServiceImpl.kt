package com.lucablock.digiboxapi.productperiod.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.customize.repository.CustomizeRepository
import com.lucablock.digiboxapi.entity.ProductPeriod
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodDto
import com.lucablock.digiboxapi.productperiod.repository.ProductPeriodRepository
import com.lucablock.digiboxapi.productperiod.request.ProductPeriodRequest
import com.lucablock.digiboxapi.productperiod.service.ProductPeriodService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ProductPeriodServiceImpl @Autowired constructor(
  private val productPeriodRepository: ProductPeriodRepository,
  private val s3Service: S3Service,
  private val customizeRepository: CustomizeRepository
):ProductPeriodService{
  @Transactional
  override fun createProductPeriod(productPeriodRequest: ProductPeriodRequest): ProductPeriodDto {
    productPeriodRepository.existsProductPeriodsByNameContainsIgnoreCaseAndIsDeletedFalse(productPeriodRequest.name).let {
      if(it){
        throw BadRequestException("ชื่อข้อมูลระยะเวลาการผลิตซ้ำ")
      }
    }
    val productPeriod = ProductPeriod(
        name = productPeriodRequest.name,
        price = productPeriodRequest.price,
        minPeriod = productPeriodRequest.minPeriod,
        maxPeriod = productPeriodRequest.maxPeriod,
        imageUrl = productPeriodRequest.imageUrl
      )

    val urlFile = s3Service.moveFile(productPeriodRequest.imageUrl).url
    productPeriod.imageUrl = urlFile
    val savedProductPeriod = productPeriodRepository.save(productPeriod)

    return savedProductPeriod.toProductPeriodDto()
  }

  override fun updateProductPeriod(id: Long, productPeriodRequest: ProductPeriodRequest): ProductPeriodDto {
    productPeriodRepository.existsProductPeriodsByNameContainsIgnoreCaseAndIdNotAndIsDeletedFalse(
      productPeriodRequest.name,id
    ).let{
      if (it){
        throw BadRequestException("ชื่อข้อมูลระยะเวลาการผลิตซ้ำ")
      }
    }
    val productPeriod = productPeriodRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลระยะเวลาการผลิต")
    }
    productPeriod.name = productPeriodRequest.name
    productPeriod.price = productPeriodRequest.price
    productPeriod.minPeriod = productPeriodRequest.minPeriod
    productPeriod.maxPeriod = productPeriodRequest.maxPeriod

    if (productPeriodRequest.imageUrl != productPeriod.imageUrl) {
      s3Service.deleteFile(productPeriod.imageUrl)
      val newFileUrl = s3Service.moveFile(productPeriodRequest.imageUrl).url
      productPeriod.imageUrl = newFileUrl
    }
    val updateProductPeriod = productPeriodRepository.save(productPeriod)
    return updateProductPeriod.toProductPeriodDto()
  }

  override fun getProductPeriodById(id: Long): ProductPeriodDto {
    val productPeriod = productPeriodRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลระยะเวลาการผลิต")
    }
    return productPeriod.toProductPeriodDto()
  }

  override fun getAllProductPeriod(): List<ProductPeriodDto> {
    return productPeriodRepository.findAllByIsDeletedFalse().map { it.toProductPeriodDto() }
  }

  @Transactional
  override fun deleteProductPeriod(id: Long) {
    val productPeriod = productPeriodRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลระยะเวลาการผลิต")
    }
//    val customize = customizeRepository.findAllByProductPeriodId(productPeriod.id)
//    if (customize.isNotEmpty()){
//      throw BadRequestException("ไม่สามารถลบข้อมูลระยะเวลาการผลิตได้ เนื่องจากมีการใช้งานอยู่")
//    }
    s3Service.deleteFile(productPeriod.imageUrl)
    productPeriod.isActive = false
    productPeriod.isDeleted = true
    productPeriodRepository.save(productPeriod)
  }
}