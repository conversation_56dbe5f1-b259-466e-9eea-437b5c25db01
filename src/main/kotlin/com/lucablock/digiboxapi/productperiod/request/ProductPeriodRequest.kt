package com.lucablock.digiboxapi.productperiod.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.PositiveOrZero

class ProductPeriodRequest {
  @NotBlank(message = "กรุณาระบุชื่อระยะเวลาการผลิต")
  val name : String = ""
  @NotNull(message = "กรุณาระบุราคาระยะเวลาการผลิต")
  @PositiveOrZero(message = "กรุณาระบุราคาระยะเวลาการผลิตให้ถูกต้อง")
  val price : Double = 0.0
  @NotNull(message = "กรุณาระบุจำนวนวันผลิตต่ำสุด")
  @PositiveOrZero(message = "กรุณาระบุจำนวนวันผลิตต่ำสุดให้ถูกต้อง")
  val minPeriod : Int = 0
  @NotNull(message = "กรุณาระบุจำนวนวันผลิตสูงสุด")
  @PositiveOrZero(message = "กรุณาระบุจำนวนวันผลิตสูงสุดให้ถูกต้อง")
  val maxPeriod : Int = 0
  @NotNull(message = "กรุณาระบุรูปภาพระยะเวลาการผลิต")
  val imageUrl : String = ""
}