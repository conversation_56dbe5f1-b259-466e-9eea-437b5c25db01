package com.lucablock.digiboxapi.fileUploader.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.fileUploader.service.FileUploaderService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class FileUploaderServiceImpl @Autowired internal constructor(
  private val s3Service: S3Service
): FileUploaderService {

  override fun fileUpload(file: MultipartFile): String {
    if (file.isEmpty) throw BadRequestException("ไม่มีไฟล์ที่อัพโหลด")
    val upload = s3Service.uploadFileBuffer(file)
    return upload.url
  }
}