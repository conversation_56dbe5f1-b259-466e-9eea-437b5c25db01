package com.lucablock.digiboxapi.fileUploader.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.fileUploader.service.FileUploaderService
import com.lucablock.digiboxapi.response.HttpResponse
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("api/file-uploader")
class FileUploaderController @Autowired internal constructor(
  private val fileUploaderService: FileUploaderService
) {
  private val logger: Logger = LoggerFactory.getLogger(FileUploaderController::class.java)

  @PostMapping()
  fun uploadImage(
    @RequestPart("file", required = true) file: MultipartFile
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "อัพโหลดรูปภาพสำเร็จ",
          fileUploaderService.fileUpload(file)
        )
      )
    }
    catch (e: BadRequestException) {
      logger.error("upload image error: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่มีไฟล์ที่อัพโหลด"))
    }catch (e: Exception) {
      logger.error("upload image error: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }
}