package com.lucablock.digiboxapi.holidaycategory.service

import com.lucablock.digiboxapi.entity.HolidayCategory
import com.lucablock.digiboxapi.holidaycategory.dto.HolidayCategoryDto
import com.lucablock.digiboxapi.holidaycategory.request.HolidayCategoryRequest

interface HolidayCategoryService {
  fun createHolidayCategory(categoryRequest: HolidayCategoryRequest):HolidayCategoryDto
  fun updateHolidayCategory(id:Long,categoryRequest: HolidayCategoryRequest): HolidayCategoryDto
  fun getAllHolidayCategory():List<HolidayCategoryDto>
  fun getHolidayCategoryById(id:Long):HolidayCategoryDto
  fun deleteHolidayCategory(id: Long): <PERSON><PERSON><PERSON>
}