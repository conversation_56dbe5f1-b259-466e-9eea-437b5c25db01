package com.lucablock.digiboxapi.holidaycategory.service.impl

import com.lucablock.digiboxapi.entity.HolidayCategory
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.holidaycategory.dto.HolidayCategoryDto
import com.lucablock.digiboxapi.holidaycategory.repository.HolidayCategoryRepository
import com.lucablock.digiboxapi.holidaycategory.request.HolidayCategoryRequest
import com.lucablock.digiboxapi.holidaycategory.service.HolidayCategoryService
import com.lucablock.digiboxapi.holidaytype.repository.HolidayTypeRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class HolidayCategoryServiceImpl @Autowired constructor(
  private val holidayCategoryRepository: HolidayCategoryRepository,
  private val holidayTypeRepository: HolidayTypeRepository
) : HolidayCategoryService {
  override fun createHolidayCategory(categoryRequest: HolidayCategoryRequest): HolidayCategoryDto {
    holidayCategoryRepository.existsHolidayCategoryByNameContainsIgnoreCase(categoryRequest.name).let {
      if (it) {
        throw NotFoundException("ชื่อหมวดหมู่วันหยุดซ้ำ")
      }
    }
    val category = holidayCategoryRepository.save(
      HolidayCategory(
        name = categoryRequest.name
      )
    )
    return HolidayCategoryDto(
      id = category.id,
      name = category.name
    )
  }

  override fun updateHolidayCategory(id: Long, categoryRequest: HolidayCategoryRequest): HolidayCategoryDto {
    val category = holidayCategoryRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลหมวดหมู่วันหยุด")
    }
    holidayCategoryRepository.existsHolidayCategoryByNameContainsIgnoreCaseAndIdNot(categoryRequest.name,id).let {
      if (it) {
        throw NotFoundException("ชื่อหมวดหมู่วันหยุดซ้ำ")
      }
    }
    category.name = categoryRequest.name
    val updateCategory = holidayCategoryRepository.save(category)
    return HolidayCategoryDto(
      id= updateCategory.id,
      name = updateCategory.name
    )
  }

  override fun getAllHolidayCategory(): List<HolidayCategoryDto> {
    val category = holidayCategoryRepository.findAll()
    return category.map {
      HolidayCategoryDto(
        id = it.id,
        name = it.name
      )
    }
  }

  override fun getHolidayCategoryById(id: Long): HolidayCategoryDto {
    val category = holidayCategoryRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลหมวดหมู่วันหยุด")
    }
    return HolidayCategoryDto(
      id = category.id,
      name = category.name
    )
  }

  override fun deleteHolidayCategory(id: Long): Boolean {
    val category = holidayCategoryRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลหมวดหมู่วันหยุด")
    }
    val type = holidayTypeRepository.findAllByHolidayCategoryId(id)
    if(type.isNotEmpty()){
      throw BadRequestException("ไม่สามารถลบหมวดหมู่วันหยุดได้ เนื่องจากหมวดหมู่นี้กำลังถูกใช้งาน")
    }else{
      holidayCategoryRepository.deleteById(category.id)
      return true
    }
  }
}