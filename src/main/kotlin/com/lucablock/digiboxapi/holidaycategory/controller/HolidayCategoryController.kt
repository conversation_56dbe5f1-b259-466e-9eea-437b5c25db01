package com.lucablock.digiboxapi.holidaycategory.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.holidaycategory.request.HolidayCategoryRequest
import com.lucablock.digiboxapi.holidaycategory.service.HolidayCategoryService
import com.lucablock.digiboxapi.holidaytype.request.HolidayTypeRequest
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class HolidayCategoryController @Autowired internal constructor(
  private var holidayCategoryService: HolidayCategoryService
){
  private val logger: Logger = LoggerFactory.getLogger(HolidayCategoryController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/holiday-category")
  fun createHolidayCategory(
    @Valid @RequestBody holidayCategoryRequest: HolidayCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างข้อมูลหมวดหมู่วันหยุดสำเร็จ",
          data = holidayCategoryService.createHolidayCategory(holidayCategoryRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลหมวดหมู่วันหยุดซ้ำ"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/holiday-category/{id}")
  fun updateHolidayCategory(
    @PathVariable id: Long,
    @Valid @RequestBody holidayCategoryRequest: HolidayCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "บันทึกข้อมูลหมวดหมู่วันหยุดสำเร็จ",
          data = holidayCategoryService.updateHolidayCategory(id,holidayCategoryRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลหมวดหมู่วันหยุดซ้ำ"))
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลหมวดหมู่วันหยุด"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/holiday-category")
  fun getAllHolidayCategory(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลหมวดหมู่วันหยุดสำเร็จ",
          data = holidayCategoryService.getAllHolidayCategory()
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลหมวดหมู่วันหยุด"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/holiday-category/{id}")
  fun getHolidayCategoryById(
    @PathVariable id : Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลหมวดหมู่วันหยุดสำเร็จ",
          data = holidayCategoryService.getHolidayCategoryById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลหมวดหมู่วันหยุด"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/holiday-category/{id}")
  fun deleteHolidayCategory(
    @PathVariable id : Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบข้อมูลหมวดหมู่วันหยุดสำเร็จ",
          data = holidayCategoryService.deleteHolidayCategory(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลหมวดหมู่วันหยุด"))
    }catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบได้ ข้อมูลกำลังถูกใช้งาน"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}