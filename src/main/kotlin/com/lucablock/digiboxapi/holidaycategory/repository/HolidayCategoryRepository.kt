package com.lucablock.digiboxapi.holidaycategory.repository

import com.lucablock.digiboxapi.entity.HolidayCategory
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface HolidayCategoryRepository : JpaRepository<HolidayCategory, Long>{
  fun existsHolidayCategoryByNameContainsIgnoreCase(name: String): Boolean
  fun existsHolidayCategoryByNameContainsIgnoreCaseAndIdNot(name: String, id: Long): <PERSON><PERSON><PERSON>
}