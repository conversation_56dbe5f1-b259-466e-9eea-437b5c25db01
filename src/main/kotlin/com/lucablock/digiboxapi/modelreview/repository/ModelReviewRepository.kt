package com.lucablock.digiboxapi.modelreview.repository

import com.lucablock.digiboxapi.entity.ModelReview
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface ModelReviewRepository : JpaRepository<ModelReview, Long> {
   fun findByUserIdAndModelIdAndOrderId(userId: Long, modelId: Long, orderId: Long): Optional<ModelReview>
   fun findByModelId(modelId: Long, pageable: Pageable): Page<ModelReview>
}