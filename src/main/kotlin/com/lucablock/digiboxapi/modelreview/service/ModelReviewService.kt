package com.lucablock.digiboxapi.modelreview.service

import com.lucablock.digiboxapi.entity.ModelReview
import com.lucablock.digiboxapi.modelreview.request.CreateModelReviewRequest
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile


interface ModelReviewService {
  /**
   * Creates a model review based on the provided request.
   * @param userPrincipal The user making the review.
   * @param modelReviewRequest The request containing the review details.
   * @return The created model review or an error response.
   * @throws IllegalArgumentException if the review cannot be created due to validation errors.
   * @throws Exception for any other errors that occur during the review creation process.
   */
  fun createModelReview(userPrincipal: UserPrincipal, modelReviewRequest: CreateModelReviewRequest): Any?

  /**
   * Creates a model review image for the specified model.
   * @param userPrincipal The user uploading the image.
   * @param id The ID of the model for which the image is being uploaded.
   * @param files The list of image files to be uploaded.
   * @return The response containing the uploaded image details or an error response.
   * @throws IllegalArgumentException if the image cannot be uploaded due to validation errors.
   * @throws Exception for any other errors that occur during the image upload process.
   */
  fun createModelReviewImage(userPrincipal: UserPrincipal, id: Long, files: List<MultipartFile>): Any?

  /**
   * Retrieves a paginated list of model reviews for a specific model.
   * @param userPrincipal The user requesting the reviews.
   * @param modelId The ID of the model for which reviews are being requested.
   * @param pageable Pagination information.
   * @return A paginated list of model reviews.
   */
  fun getModelReviews(userPrincipal: UserPrincipal, modelId: Long, pageable: Pageable): Page<ModelReview>

  /**
   * Deletes a model review by its ID.
   * @param userPrincipal The user requesting the deletion.
   * @param modelReviewId The ID of the model review to be deleted.
   * @throws IllegalArgumentException if the review cannot be found or deleted.
   */
  fun deleteModelReview(userPrincipal: UserPrincipal, modelReviewId: Long)
}