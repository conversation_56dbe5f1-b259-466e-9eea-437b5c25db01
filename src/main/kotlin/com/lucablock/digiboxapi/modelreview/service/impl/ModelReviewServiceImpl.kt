package com.lucablock.digiboxapi.modelreview.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.ModelReview
import com.lucablock.digiboxapi.entity.ModelReviewImage
import com.lucablock.digiboxapi.model.repository.ModelRepository
import com.lucablock.digiboxapi.modelreview.repository.ModelReviewImageRepository
import com.lucablock.digiboxapi.modelreview.repository.ModelReviewRepository
import com.lucablock.digiboxapi.modelreview.request.CreateModelReviewRequest
import com.lucablock.digiboxapi.modelreview.service.ModelReviewService
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.repository.UserRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile


@Service
class ModelReviewServiceImpl @Autowired internal constructor(
  private val modelReviewRepository: ModelReviewRepository,
  private val modelReviewImageRepository: ModelReviewImageRepository,
  private val modelRepository: ModelRepository,
  private val userRepository: UserRepository,
  private val s3Service: S3Service
) : ModelReviewService {
  override fun createModelReview(userPrincipal: UserPrincipal, modelReviewRequest: CreateModelReviewRequest): Any? {
    userRepository.findById(userPrincipal.getUserId()).orElseThrow {
      throw IllegalArgumentException("User not found")
    }

    modelRepository.findById(modelReviewRequest.modelId).orElseThrow {
      throw IllegalArgumentException("Model not found")
    }

    val findReviewDuplicate = modelReviewRepository.findByUserIdAndModelIdAndOrderId(
      userPrincipal.getUserId(),
      modelReviewRequest.modelId,
      modelReviewRequest.orderId
    )

    if (findReviewDuplicate.isPresent) {
      throw IllegalArgumentException("คุณได้ให้คะแนนโมเดลนี้ไปแล้ว")
    }

    val modelReview = ModelReview(
      userId = userPrincipal.getUserId(),
      modelId = modelReviewRequest.modelId,
      orderId = modelReviewRequest.orderId,
      rating = modelReviewRequest.rating,
      comment = modelReviewRequest.comment
    )
    return modelReviewRepository.save(modelReview)
  }

  override fun createModelReviewImage(userPrincipal: UserPrincipal, id: Long, files: List<MultipartFile>): Any? {
    userRepository.findById(userPrincipal.getUserId()).orElseThrow {
      throw IllegalArgumentException("User not found")
    }

    val modelReview = modelReviewRepository.findById(id).orElseThrow {
      throw IllegalArgumentException("Model review not found")
    }

    if (modelReview.userId != userPrincipal.getUserId()) {
      throw IllegalArgumentException("You are not authorized to upload images for this review")
    }

    if (files.isEmpty()) {
      throw IllegalArgumentException("No files provided for upload")
    }

    val imageList: MutableList<ModelReviewImage> = mutableListOf()

    files.forEach { file ->
      if (file.isEmpty) {
        throw IllegalArgumentException("File is empty")
      }

      val fileName = s3Service.uploadFile(file)
      imageList.add(
        ModelReviewImage(
          modelReviewId = modelReview.id,
          imageUrl = fileName.url
        )
      )
    }
    modelReviewImageRepository.saveAll(imageList)
    return true
  }

  override fun getModelReviews(userPrincipal: UserPrincipal, modelId: Long, pageable: Pageable): Page<ModelReview> {
    return modelReviewRepository.findByModelId(modelId, pageable)
  }

  override fun deleteModelReview(userPrincipal: UserPrincipal, modelReviewId: Long) {
    val modelReview = modelReviewRepository.findById(modelReviewId).orElseThrow {
      throw IllegalArgumentException("Model review not found")
    }

    if (modelReview.userId != userPrincipal.getUserId()) {
      throw IllegalArgumentException("You are not authorized to delete this review")
    }
    // Delete associated images
    modelReview.images.forEach { image ->
      s3Service.deleteFile(image.imageUrl)
    }
    modelReviewImageRepository.deleteAll(modelReview.images)
    modelReviewRepository.delete(modelReview)
  }
}