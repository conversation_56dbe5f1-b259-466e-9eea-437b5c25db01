package com.lucablock.digiboxapi.modelreview.controller

import com.lucablock.digiboxapi.modelreview.request.CreateModelReviewRequest
import com.lucablock.digiboxapi.modelreview.service.ModelReviewService
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("/api/model/review")
class ModelReviewController @Autowired internal constructor(
  private val reviewService: ModelReviewService
) {

  private val logger: Logger = LoggerFactory.getLogger(ModelReviewController::class.java)

  @PostMapping
  fun createModelReview(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody modelReviewRequest: CreateModelReviewRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ให้คะแนนโมเดลสำเร็จ",
          reviewService.createModelReview(userPrincipal, modelReviewRequest)
        )
      )
    } catch (e: IllegalArgumentException) {
      logger.error("Error creating model review: ${e.message}")
      ResponseEntity.badRequest().body(
        HttpResponse(
          false,
          e.message ?: "ไม่สามารถให้คะแนนโมเดล กรุณาลองอีกครั้ง",
          null
        )
      )
    } catch (e: Exception) {
      logger.error("Error creating model review: ${e.message}")
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "ไม่สามารถให้คะแนนโมเดล กรุณาลองอีกครั้ง",
          null
        )
      )
    }
  }

  @PostMapping("/image/{id}", consumes = ["multipart/form-data"])
  fun createModelReviewImage(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @PathVariable id: Long,
    @RequestParam("files") files: List<MultipartFile>
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "อัพโหลดรูปภาพโมเดลสำเร็จ",
          reviewService.createModelReviewImage(userPrincipal, id, files)
        )
      )
    } catch (e: IllegalArgumentException) {
      logger.error("Error creating model review image: ${e.message}")
      ResponseEntity.badRequest().body(
        HttpResponse(
          false,
          e.message ?: "ไม่สามารถอัพโหลดรูปภาพโมเดล กรุณาลองอีกครั้ง",
          null
        )
      )
    } catch (e: Exception) {
      logger.error("Error creating model review image: ${e.message}")
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "ไม่สามารถอัพโหลดรูปภาพโมเดล กรุณาลองอีกครั้ง",
          null
        )
      )
    }
  }

  @GetMapping("/{modelId}")
  fun getModelReviews(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @PathVariable modelId: Long,
    @RequestParam(required = false, defaultValue = "0") page: Int,
    @RequestParam(required = false, defaultValue = "10") size: Int
  ): ResponseEntity<Any> {
    return try {
      val pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ดึงข้อมูลรีวิวโมเดลสำเร็จ",
          reviewService.getModelReviews(userPrincipal, modelId, pageable)
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching model reviews: ${e.message}")
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "ไม่สามารถดึงข้อมูลรีวิวโมเดล กรุณาลองอีกครั้ง",
          null
        )
      )
    }
  }

  @DeleteMapping("/{modelReviewId}")
  fun deleteModelReview(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @PathVariable modelReviewId: Long
  ): ResponseEntity<Any> {
    return try {
      reviewService.deleteModelReview(userPrincipal, modelReviewId)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบรีวิวโมเดลสำเร็จ",
          null
        )
      )
    } catch (e: IllegalArgumentException) {
      logger.error("Error deleting model review: ${e.message}")
      ResponseEntity.badRequest().body(
        HttpResponse(
          false,
          e.message ?: "ไม่สามารถลบรีวิวโมเดล กรุณาลองอีกครั้ง",
          null
        )
      )
    } catch (e: Exception) {
      logger.error("Error deleting model review: ${e.message}")
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "ไม่สามารถลบรีวิวโมเดล กรุณาลองอีกครั้ง",
          null
        )
      )
    }
  }
}