package com.lucablock.digiboxapi.product.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.product.request.ProductRequest
import com.lucablock.digiboxapi.product.service.ProductService
import com.lucablock.digiboxapi.productCategory.request.ProductCategoryRequest
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class ProductController {
  private val logger: Logger = LoggerFactory.getLogger(ProductController::class.java)

  @Autowired
  lateinit var productService: ProductService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/product")
  fun createProduct(
    @Valid @RequestBody productRequest: ProductRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เพิ่มผลิตภัณฑ์สำเร็จ",
          productService.createProduct(productRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create product error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, "ผลิตภัณฑ์นี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error("create product error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีหมวดหมู่ผลิตภัณฑ์"))
    } catch (e: Exception) {
      logger.error("create product error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/product/page")
  fun findAllProductPage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("categoryId", required = false) categoryId: List<Long>?,
    @RequestParam("tagId", required = false) tagId: List<Long>?,
    @RequestParam("typeId", required = false) typeId: List<Long>?,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสินค้าสำเร็จ",
          data = productService.getProductPage(
            pageable,
            ascending,
            search,
            categoryId,
            tagId,
            typeId,
            isActive,
          )
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product/{id}")
  fun findProductCategoryById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสินค้าสำเร็จ",
          data = productService.getProductById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลสินค้า"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @Transactional
  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/product/{id}")
  fun updateProduct(
    @PathVariable id: Long,
    @Valid @RequestBody productRequest: ProductRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "บันทึกข้อมูลหมวดหมู่สินค้าสำเร็จ",
          data = productService.updateProduct(id, productRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลสินค้า"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "สินค้านี้มีอยู่แล้ว"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลหมวดหมู่ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/product/{id}")
  fun deleteProduct(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบสินค้าสำเร็จ",
          data = productService.deleteProduct(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลสินค้า"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลสินค้าได้ มีการใช้งานอยู่"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลสินค้า กรุณาลองอีกครั้ง"))
    }
  }

//
//  @GetMapping()
//  fun findAllProduct(): ResponseEntity<Any> {
//    return try {
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "ค้นหาผลิตภัณฑ์ทั้งหมดได้สำเร็จ",
//          productService.findAllProducts()
//        )
//      )
//    } catch (e: Exception) {
//      logger.error("find all product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
//    }
//  }
//
//  @GetMapping("/search")
//  fun searchProduct(
//    @RequestParam("page", defaultValue = "0") page: Int,
//    @RequestParam("size", defaultValue = "20") size: Int,
//    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
//    @RequestParam("searchTerm", required = false) searchTerm: String?,
//    @RequestParam("sortField", required = false) sortField: String?,
//  ): ResponseEntity<Any> {
//    return try {
//      val pageable: Pageable = PageRequest.of(page, size)
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "ค้นหาผลิตภัณฑ์สำเร็จ",
//          productService.searchProducts(pageable, ascending, searchTerm, sortField)
//        )
//      )
//    } catch (e: Exception) {
//      logger.error("search product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
//    }
//  }
//
//  @GetMapping("/{id}")
//  fun findProductById(@PathVariable id: Long): ResponseEntity<Any> {
//    return try {
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "ค้นหาผลิตภัณฑ์ตามรหัสสำเร็จ",
//          productService.findProductById(id)
//        )
//      )
//    } catch (e: NotFoundException) {
//      logger.error("find product by id error : ${e.message}")
//      ResponseEntity.status(HttpStatus.NOT_FOUND)
//        .body(HttpResponse(false, e.message ?: "ไม่มีผลิตภัณฑ์"))
//    } catch (e: Exception) {
//      logger.error("find product by id error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
//    }
//  }
//
//  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
//  @PutMapping("/{id}")
//  fun updateProduct(
//    @PathVariable("id", required = true) id: Long,
//    @Valid @RequestBody productRequest: ProductRequest
//  ): ResponseEntity<Any> {
//    return try {
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "บันทึกการแก้ไขข้อมูลผลิตภัณฑ์สำเร็จ",
//          productService.updateProduct(id, productRequest)
//        )
//      )
//    } catch (e: NotFoundException) {
//      logger.error("update product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.NOT_FOUND)
//        .body(HttpResponse(false, e.message ?: "ไม่มีผลิตภัณฑ์"))
//    } catch (e: BadRequestException) {
//      logger.error("update product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.BAD_REQUEST)
//        .body(HttpResponse(false, e.message ?: "ผลิตภัณฑ์นี้มีอยู่แล้ว"))
//    } catch (e: Exception) {
//      logger.error("update product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
//    }
//  }
//
//  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
//  @DeleteMapping("/{id}")
//  fun deleteProduct(@PathVariable id: Long): ResponseEntity<Any> {
//    return try {
//      ResponseEntity.ok(
//        HttpResponse(
//          true,
//          "ลบข้อมูลผลิตภัณฑ์สำเร็จ",
//          productService.deleteProduct(id)
//        )
//      )
//    } catch (e: NotFoundException) {
//      logger.error("delete product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.NOT_FOUND)
//        .body(HttpResponse(false, e.message ?: "ไม่มีผลิตภัณฑ์"))
//    } catch (e: BadRequestException) {
//      logger.error("delete product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.BAD_REQUEST)
//        .body(HttpResponse(false, e.message ?: "ผลิตภัณฑ์นี้ถูกใช้งานอยู่ไม่สามารถลบได้"))
//    } catch (e: Exception) {
//      logger.error("delete product error : ${e.message}")
//      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
//    }
//  }
}