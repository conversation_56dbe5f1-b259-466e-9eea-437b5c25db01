package com.lucablock.digiboxapi.product.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.model.dto.ModelDto
import com.lucablock.digiboxapi.productcategoryconfig.dto.ProductCategoryConfigDto
import com.lucablock.digiboxapi.productgallery.dto.ProductGalleryDto
import com.lucablock.digiboxapi.productspecialtechnic.dto.ProductSpecialTechnicDto
import com.lucablock.digiboxapi.producttagconfig.dto.ProductTagConfigDto
import com.lucablock.digiboxapi.producttypeconfig.dto.ProductTypeConfigDto
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class ProductDto
@QueryProjection
constructor(
  val id: Long,
  val name: String,
  val description: String? = null,
  val guideDetail: String,
  val installmentDetail: String,
  val shippingDetail: String,
  val isActive: Boolean,
  val isDeleted: Boolean,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
  val category: List<ProductCategoryConfigDto> = mutableListOf(),
  val tag: List<ProductTagConfigDto> = mutableListOf(),
  val type: List<ProductTypeConfigDto> = mutableListOf(),
  val specialTechnic: List<ProductSpecialTechnicDto> = mutableListOf(),
  val gallery: List<ProductGalleryDto> = mutableListOf(),
)

data class ProductListDto
@QueryProjection
constructor(
  val id: Long,
  val name: String,
  val description: String? = null,
)
