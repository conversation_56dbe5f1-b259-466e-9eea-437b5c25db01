package com.lucablock.digiboxapi.product.repository

import com.lucablock.digiboxapi.product.dto.ProductDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository

@Repository
interface ProductRepositoryCustom {
  fun getProductPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    categoryId: List<Long>?,
    tagId: List<Long>?,
    typeId: List<Long>?,
    isActive : Boolean?,
  ): Page<ProductDto>
}