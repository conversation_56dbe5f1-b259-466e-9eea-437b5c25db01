package com.lucablock.digiboxapi.product.repository

import com.lucablock.digiboxapi.entity.Product
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository


@Repository
interface ProductRepository : JpaRepository<Product, Long>, ProductRepositoryCustom {
  fun findAllByNameContainingIgnoreCase(name: String, pageable: Pageable): Page<Product>
//  fun existsByProductCategoryId(productCategoryId: Long): Boolean
  fun existsByNameIgnoreCase(name: String): Boolean
  fun existsByNameIgnoreCaseAndIdNot(name: String, id: Long): Boolean

//  @Query(
//    "SELECT p FROM Product p JOIN FETCH p.models m WHERE p.id = :id AND m.isActive = true",
//    nativeQuery = false
//  )
//  fun findByIdAndModelsIsActiveTrue(id: Long): Product
//
//  @Query(
//    "SELECT p FROM Product p JOIN FETCH p.models m WHERE m.isActive = true", nativeQuery = false
//  )
//  fun findAllByModelsIsActiveTrue(): List<Product>
}