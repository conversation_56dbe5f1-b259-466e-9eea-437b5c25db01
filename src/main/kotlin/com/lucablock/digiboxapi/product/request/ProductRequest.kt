package com.lucablock.digiboxapi.product.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.lucablock.digiboxapi.productcategoryconfig.dto.ProductCategoryConfigDto
import com.lucablock.digiboxapi.productgallery.request.ProductGalleryRequest
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

class ProductRequest {
  @NotBlank(message = "ระบุชื่อผลิตภัณฑ์")
  val name: String = ""
  val description: String? = null
  val guideDetail: String = ""
  val installmentDetail: String = ""
  val shippingDetail: String = ""

  @JsonProperty("isActive")
  val isActive: Boolean = true

  @NotEmpty(message = "กรุณาระบุรหัสหมวดหมู่สินค้า")
  @Valid
  val category : List<ProductCategoryConfigRequest> = mutableListOf()

  @Valid
  val tag : List<ProductTagConfigRequest> = mutableListOf()

  @NotEmpty(message = "กรุณาระบุประเภทสินค้า")
  @Valid
  val type : List<ProductTypeConfigRequest> = mutableListOf()

//  @NotEmpty(message = "stages is required")
//  @Valid
//  val coating : List<ProductCoatingRequest> = mutableListOf()

//  @NotEmpty(message = "stages is required")
  @Valid
  val specialTechnic : List<ProductSpecialTechnicRequest> = mutableListOf()

  @NotEmpty(message = "กรุณาระบุรูปภาพสินค้า")
  @Valid
  val gallery : List<ProductGalleryRequest> = mutableListOf()
}

class ProductCategoryConfigRequest {
  @NotNull(message = "กรุณาระบุรหัสสินค้า")
  var productId : Long = 0
  @NotNull(message = "กรุณาระบุรหัสหมวดหมู่สินค้า")
  @Min(value = 1, message = "กรุณาระบุรหัสหมวดหมู่สินค้าให้ถูกต้อง")
  var productCategoryId: Long = 0
}
class ProductTagConfigRequest {
  @NotNull(message = "กรุณาระบุรหัสสินค้า")
  var productId : Long = 0
  @NotNull(message = "กรุณาระบุรหัสแท็ก")
  var productTagId: Long = 0
}
class ProductTypeConfigRequest {
  @NotNull(message = "กรุณาระบุรหัสสินค้า")
  var productId : Long = 0
  @NotNull(message = "กรุณาระบุรหัสประเภทสินค้า")
  @Min(value = 1, message = "กรุณาระบุรหัสประเภทสินค้าให้ถูกต้อง")
  var productTypeId: Long = 0
}
class ProductCoatingRequest {
  @NotNull(message = "กรุณาระบุรหัสสินค้า")
  var productId : Long = 0
  @NotNull(message = "กรุณาระบุรหัสการเคลือบ")
  @Min(value = 1, message = "กรุณาระบุรหัสการเคลือบให้ถูกต้อง")
  var coatingId: Int = 0
}
class ProductSpecialTechnicRequest {
  @NotNull(message = "กรุณาระบุรหัสสินค้า")
  var productId : Long = 0
  @NotNull(message = "กรุณาระบุรหัสเทคนิคพิเศษ")
  @Min(value = 1, message = "กรุณาระบุรหัสเทคนิคพิเศษให้ถูกต้อง")
  var specialTechnicId: Int = 0
}
