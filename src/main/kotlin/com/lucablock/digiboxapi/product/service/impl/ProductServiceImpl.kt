package com.lucablock.digiboxapi.product.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.coating.repository.CoatingRepository
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.model.repository.ModelRepository
import com.lucablock.digiboxapi.product.dto.ProductDto
import com.lucablock.digiboxapi.product.repository.ProductRepository
import com.lucablock.digiboxapi.product.request.ProductRequest
import com.lucablock.digiboxapi.product.service.ProductService
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryRepository
import com.lucablock.digiboxapi.productcategoryconfig.repository.ProductCategoryConfigRepository
import com.lucablock.digiboxapi.productgallery.repository.ProductGalleryRepository
import com.lucablock.digiboxapi.productspecialtechnic.repository.ProductSpecialTechnicRepository
import com.lucablock.digiboxapi.producttag.repository.ProductTagRepository
import com.lucablock.digiboxapi.producttagconfig.repository.ProductTagConfigRepository
import com.lucablock.digiboxapi.producttype.repository.ProductTypeRepository
import com.lucablock.digiboxapi.producttypeconfig.repository.ProductTypeConfigRepository
import com.lucablock.digiboxapi.specialTechnic.repository.SpecialTechnicRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProductServiceImpl @Autowired constructor(
  private val productCategoryRepository: ProductCategoryRepository,
  private val productRepository: ProductRepository,
  private val productTagRepository: ProductTagRepository,
  private val s3Service: S3Service,
  private val modelRepository: ModelRepository,
  private val productTypeRepository: ProductTypeRepository,
  private val coatingRepository: CoatingRepository,
  private val specialTechnicRepository: SpecialTechnicRepository,
  private val productCategoryConfigRepository: ProductCategoryConfigRepository,
  private val productTagConfigRepository: ProductTagConfigRepository,
  private val productTypeConfigRepository: ProductTypeConfigRepository,
  private val productSpecialTechnicRepository: ProductSpecialTechnicRepository,
  private val productGalleryRepository: ProductGalleryRepository
) : ProductService {
  @Transactional
  override fun createProduct(productRequest: ProductRequest): ProductDto {
    productRepository.existsByNameIgnoreCase(productRequest.name)
      .let { if (it) throw BadRequestException("สินค้านี้มีอยู่แล้ว") }

    val categoryId = productRequest.category.map { it.productCategoryId }.toSet()
    val productCategories = productCategoryRepository.findAllById(categoryId)
    if (productCategories.size != categoryId.size) {
      throw NotFoundException("ไม่มีหมวดหมู่ผลิตภัณฑ์")
    }

    val tagId = productRequest.tag.map { it.productTagId }.toSet()
    val productTags = productTagRepository.findAllById(tagId)
    if (productTags.size != tagId.size) {
      throw NotFoundException("ไม่มีแท็กผลิตภัณฑ์")
    }

    val typeId = productRequest.type.map { it.productTypeId }.toSet()
    val productType = productTypeRepository.findAllById(typeId)
    if (productType.size != typeId.size) {
      throw NotFoundException("ไม่มีประเภทผลิตภัณฑ์")
    }

    val specialTechnicId = productRequest.specialTechnic.map { it.specialTechnicId }.toSet()
    val specialTechnic = specialTechnicRepository.findAllById(specialTechnicId)
    if (specialTechnic.size != specialTechnicId.size) {
      throw NotFoundException("ไม่มีเทคนิคพิเศษ")
    }

    val product = Product(
        name = productRequest.name,
        description = productRequest.description,
        guideDetail = productRequest.guideDetail,
        installmentDetail = productRequest.installmentDetail,
        shippingDetail = productRequest.shippingDetail,
      )
    val savedProduct = productRepository.save(product)

    val saveCategory = productRequest.category.map {
      productCategoryConfigRepository.save(
        ProductCategoryConfig(
          productId = savedProduct.id,
          productCategoryId = it.productCategoryId,
        )
      )
    }

    val saveTag = productRequest.tag.map {
      productTagConfigRepository.save(
        ProductTagConfig(
          productId = savedProduct.id,
          productTagId = it.productTagId,
        )
      )
    }

    val saveType = productRequest.type.map {
      productTypeConfigRepository.save(
        ProductTypeConfig(
          productId = savedProduct.id,
          productTypeId = it.productTypeId,
        )
      )
    }

    val saveSpecialTechnic = productRequest.specialTechnic.map {
      productSpecialTechnicRepository.save(
        ProductSpecialTechnic(
          productId = savedProduct.id,
          specialTechnicId = it.specialTechnicId,
        )
      )
    }

    val saveGallery = productRequest.gallery.map {
      val movedUrl = s3Service.moveFile(it.imageUrl).url
      val fileType = if (movedUrl.endsWith(".mp4") || movedUrl.contains(".mov")) 2 else 1
      productGalleryRepository.save(
        ProductGallery(
          productId = savedProduct.id,
          imageUrl = movedUrl,
          fileType = fileType
        )
      )
    }

    savedProduct.productCategoryConfig = saveCategory
    savedProduct.productTagConfig = saveTag
    savedProduct.productTypeConfig = saveType
    savedProduct.productSpecialTechnic = saveSpecialTechnic
    savedProduct.productGallery = saveGallery
    val updateProduct = productRepository.save(savedProduct)

    return updateProduct.toProductDto()
  }

  override fun getProductPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    categoryId: List<Long>?,
    tagId: List<Long>?,
    typeId: List<Long>?,
    isActive: Boolean?,
  ): Page<ProductDto> {
    return productRepository.getProductPage(
      pageable, ascending, search, categoryId, tagId, typeId, isActive
    )
  }

  override fun getProductById(id: Long): ProductDto {
    val product = productRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบสินค้า")
    }
    return product.toProductDto()
  }

  @Transactional
  override fun updateProduct(id: Long, productRequest: ProductRequest): ProductDto {
    val product = productRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบสินค้า")
    }
    productRepository.existsByNameIgnoreCaseAndIdNot(productRequest.name,id).let {
      if(it){
        throw BadRequestException("สินค้านี้มีอยู่แล้ว")
      }
    }

    val categoryId = productRequest.category.map { it.productCategoryId }.toSet()
    val productCategories = productCategoryRepository.findAllById(categoryId)
    if (productCategories.size != categoryId.size) {
      throw NotFoundException("ไม่มีหมวดหมู่ผลิตภัณฑ์")
    }

    val tagId = productRequest.tag.map { it.productTagId }.toSet()
    val productTags = productTagRepository.findAllById(tagId)
    if (productTags.size != tagId.size) {
      throw NotFoundException("ไม่มีแท็กผลิตภัณฑ์")
    }

    val typeId = productRequest.type.map { it.productTypeId }.toSet()
    val productType = productTypeRepository.findAllById(typeId)
    if (productType.size != typeId.size) {
      throw NotFoundException("ไม่มีประเภทผลิตภัณฑ์")
    }

    val specialTechnicId = productRequest.specialTechnic.map { it.specialTechnicId }.toSet()
    val specialTechnic = specialTechnicRepository.findAllById(specialTechnicId)
    if (specialTechnic.size != specialTechnicId.size) {
      throw NotFoundException("ไม่มีการเคลือบ")
    }

    product.name = productRequest.name
    product.description = productRequest.description
    product.guideDetail = productRequest.guideDetail
    product.installmentDetail = productRequest.installmentDetail
    product.shippingDetail = productRequest.shippingDetail
    product.isActive = productRequest.isActive

    val savedProduct = productRepository.save(product)

    productCategoryConfigRepository.deleteAllByProductId(savedProduct.id)
    productTagConfigRepository.deleteAllByProductId(savedProduct.id)
    productTypeConfigRepository.deleteAllByProductId(savedProduct.id)
    productSpecialTechnicRepository.deleteAllByProductId(savedProduct.id)

    val saveCategory = productRequest.category.map {
        ProductCategoryConfig(
          productId = savedProduct.id,
          productCategoryId = it.productCategoryId,
        )
    }
    productCategoryConfigRepository.saveAll(saveCategory)

    val saveTag = productRequest.tag.map {
        ProductTagConfig(
          productId = savedProduct.id,
          productTagId = it.productTagId,
        )
    }
    productTagConfigRepository.saveAll(saveTag)

    val saveType = productRequest.type.map {
        ProductTypeConfig(
          productId = savedProduct.id,
          productTypeId = it.productTypeId,
        )
    }
    productTypeConfigRepository.saveAll(saveType)

    val saveSpecialTechnic = productRequest.specialTechnic.map {
        ProductSpecialTechnic(
          productId = savedProduct.id,
          specialTechnicId = it.specialTechnicId,
        )
    }
    productSpecialTechnicRepository.saveAll(saveSpecialTechnic)

    val oldGallery = productGalleryRepository.findAllByProductId(savedProduct.id)
    val oldGalleryUrls = oldGallery.map { it.imageUrl }.toSet()
    val newGalleryUrls = productRequest.gallery.map { it.imageUrl }.toSet()
    val toDelete = oldGallery.filter { it.imageUrl !in newGalleryUrls }
    toDelete.forEach {
      s3Service.deleteFile(it.imageUrl)
      productGalleryRepository.deleteById(it.id)
    }

    val toAdd = newGalleryUrls.subtract(oldGalleryUrls)
    val saveGallery = toAdd.map {
      val movedUrl = s3Service.moveFile(it).url
      val fileType = if (movedUrl.endsWith(".mp4") || movedUrl.contains(".mov")) 2 else 1
      productGalleryRepository.save(
        ProductGallery(
          productId = savedProduct.id,
          imageUrl = movedUrl,
          fileType = fileType
        )
      )
    }
    val gallery = productGalleryRepository.findAllByProductId(savedProduct.id)

    savedProduct.productCategoryConfig = saveCategory
    savedProduct.productTagConfig = saveTag
    savedProduct.productTypeConfig = saveType
    savedProduct.productSpecialTechnic = saveSpecialTechnic
    savedProduct.productGallery = gallery
    val updateProduct = productRepository.save(savedProduct)
    return updateProduct.toProductDto()
  }

  override fun deleteProduct(id: Long): Boolean {
    val product = productRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบสินค้า")
    }
    product.isDeleted = true
    productRepository.save(product)
    return true
  }
//
//  override fun findAllProducts(): List<ProductDto> {
//    return productRepository.findAllByModelsIsActiveTrue().map { it.toProductDto() }
//  }
//
//  override fun searchProducts(
//    pageable: Pageable,
//    ascending: Boolean,
//    searchTerm: String?,
//    sortField: String?
//  ): Page<ProductDto> {
//    var term = ""
//    if (searchTerm != null) {
//      term = searchTerm
//    }
//    return productRepository.findAllByNameContainingIgnoreCase(term, pageable)
//      .map { it.toProductDto() }
//  }
//
//  override fun findProductById(id: Long): ProductDto {
//    return productRepository.findByIdAndModelsIsActiveTrue(id).toProductDto()
//  }
//
//  override fun updateProduct(id: Long, productRequest: ProductRequest): ProductDto {
//    val product =
//      productRepository.findById(id).orElseThrow { throw NotFoundException("ไม่มีผลิตภัณฑ์") }
//
//    productCategoryRepository.findById(productRequest.categoryId)
//      .orElseThrow { throw NotFoundException("ไม่มีหมวดหมู่ผลิตภัณฑ์") }
//
//    productRepository.existsByNameContainingIgnoreCaseAndIdNot(productRequest.name, id)
//      .let { if (it) throw BadRequestException("ผลิตภัณฑ์นี้มีอยู่แล้ว") }
//
//    product.name = productRequest.name
//    product.productCategoryId = productRequest.categoryId
//
//    if (productRequest.imageUrl != product.imageUrl) {
//      s3Service.deleteFile(product.imageUrl)
//      val newFileUrl = s3Service.moveFile(productRequest.imageUrl).url
//      product.imageUrl = newFileUrl
//    }
//
//    val updateProduct = productRepository.save(product)
//    return updateProduct.toProductDto()
//  }
//
//  override fun deleteProduct(id: Long) {
//    val product = productRepository.findById(id).orElseThrow { NotFoundException("ไม่มีผลิตภัณฑ์") }
//
//    val models = modelRepository.findAllByProductId(id)
//    if (models.isNotEmpty()) throw BadRequestException("ผลิตภัณฑ์นี้ถูกใช้งานอยู่ ไม่สามารถลบได้")
//
//    s3Service.deleteFile(product.imageUrl)
//    productRepository.deleteById(id)
//  }
}