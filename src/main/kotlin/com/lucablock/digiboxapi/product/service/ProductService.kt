package com.lucablock.digiboxapi.product.service

import com.lucablock.digiboxapi.product.dto.ProductDto
import com.lucablock.digiboxapi.product.request.ProductRequest
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface ProductService {
  fun createProduct(productRequest: ProductRequest): ProductDto
  fun getProductPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    categoryId: List<Long>?,
    tagId: List<Long>?,
    typeId: List<Long>?,
    isActive : Boolean?,
  ): Page<ProductDto>
  fun getProductById(id: Long):ProductDto
  fun updateProduct(id:Long,productRequest: ProductRequest): ProductDto
  fun deleteProduct(id:Long): Boolean

//  fun findAllProducts(): List<ProductDto>
//  fun searchProducts(
//    pageable: Pageable,
//    ascending: Boolean,
//    searchTerm: String?,
//    sortField: String?
//  ): Page<ProductDto>
//
//  fun findProductById(id: Long): ProductDto
//  fun updateProduct(id: Long, productRequest: ProductRequest): ProductDto
//  fun deleteProduct(id: Long)
}