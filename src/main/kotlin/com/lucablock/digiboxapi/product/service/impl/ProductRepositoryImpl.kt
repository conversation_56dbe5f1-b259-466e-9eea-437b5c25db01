package com.lucablock.digiboxapi.product.service.impl

import com.lucablock.digiboxapi.entity.QProduct
import com.lucablock.digiboxapi.entity.QProductCategory
import com.lucablock.digiboxapi.product.dto.ProductDto
import com.lucablock.digiboxapi.product.repository.ProductRepositoryCustom
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProductRepositoryImpl : ProductRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qProduct = QProduct.product
  override fun getProductPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    categoryId: List<Long>?,
    tagId: List<Long>?,
    typeId: List<Long>?,
    isActive: Boolean?,
  ): Page<ProductDto> {
    var criteria = qProduct.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qProduct.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qProduct.name.containsIgnoreCase(search))
    }
    if (categoryId != null) {
      criteria = criteria.and(qProduct.productCategoryConfig.any().productCategory.id.`in`(categoryId))
    }
    if (tagId != null) {
      criteria = criteria.and(qProduct.productTagConfig.any().productTag.id.`in`(tagId))
    }
    if (typeId != null) {
      criteria = criteria.and(qProduct.productTypeConfig.any().productType.id.`in`(typeId))
    }

    val sort = if (ascending) {
      qProduct.id.desc()
    } else {
      qProduct.id.asc()
    }

    val query = queryFactory
      .select(qProduct)
      .from(qProduct)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toProductDto()
      }

    val total = queryFactory
      .select(qProduct)
      .from(qProduct)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)

  }

}