package com.lucablock.digiboxapi.coupontype.controller

import com.lucablock.digiboxapi.coupontype.request.CreateCouponTypeRequest
import com.lucablock.digiboxapi.coupontype.service.CouponTypeService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
class CouponTypeController @Autowired constructor(
  private val couponTypeService: CouponTypeService,
) {
  val logger = LoggerFactory.getLogger(CouponTypeController::class.java)

  @PostMapping("/admin/coupon-type")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun createCouponType(
    @Valid @RequestBody request: CreateCouponTypeRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "สร้างประเภทคูปองสำเร็จ",
          data = couponTypeService.createCouponType(request)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อประเภทคูปองไม่ถูกต้อง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการสร้างประเภทคูปอง")
      )
    }
  }

  @PutMapping("/admin/coupon-type/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun updateCouponType(
    @PathVariable id: Long,
    @Valid @RequestBody request: CreateCouponTypeRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "อัพเดตประเภทคูปองสำเร็จ",
          data = couponTypeService.updateCouponType(id, request)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อประเภทคูปองไม่ถูกต้อง")
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบประเภทคูปอง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการอัพเดตประเภทคูปอง")
      )
    }
  }

  @DeleteMapping("/admin/coupon-type/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun deleteCouponType(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ลบประเภทคูปองสำเร็จ",
          data = couponTypeService.deleteCouponType(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบประเภทคูปอง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการลบประเภทคูปอง")
      )
    }
  }

  @GetMapping("/web/coupon-type/{id}")
  fun getCouponTypeById(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลประเภทคูปองสำเร็จ",
          data = couponTypeService.getCouponTypeById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบประเภทคูปอง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงข้อมูลประเภทคูปอง")
      )
    }
  }

  @GetMapping("/web/coupon-type/list")
  fun getListOfCouponTypes(
    @RequestParam(defaultValue = "true") ascending: Boolean,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงรายการประเภทคูปองสำเร็จ",
          data = couponTypeService.getListOfCouponTypes(ascending)
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงรายการประเภทคูปอง")
      )
    }
  }

  @PutMapping("/admin/coupon-type/status/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun changeCouponTypeStatus(
    @PathVariable id: Long,
    @RequestParam isActive: Boolean,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "เปลี่ยนสถานะประเภทคูปองสำเร็จ",
          data = couponTypeService.changeCouponTypeStatus(id, isActive)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบประเภทคูปอง")
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการเปลี่ยนสถานะประเภทคูปอง")
      )
    }
  }

  @GetMapping("/web/coupon-type/page")
  fun getPaginationOfCouponType(
    @RequestParam(defaultValue = "0") page: Int,
    @RequestParam(defaultValue = "10") size: Int,
    @RequestParam(defaultValue = "true") ascending: Boolean,
  ): ResponseEntity<Any> {
    return try {
      val pageable = PageRequest.of(page, size)
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลประเภทคูปองสำเร็จ",
          data = couponTypeService.getPaginationOfCouponType(pageable, ascending)
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงข้อมูลประเภทคูปอง")
      )
    }
  }
}