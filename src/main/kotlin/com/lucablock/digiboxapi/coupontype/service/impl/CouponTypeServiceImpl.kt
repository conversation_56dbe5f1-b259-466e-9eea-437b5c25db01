package com.lucablock.digiboxapi.coupontype.service.impl

import com.lucablock.digiboxapi.coupontype.dto.CouponTypeDto
import com.lucablock.digiboxapi.coupontype.repository.CouponTypeRepository
import com.lucablock.digiboxapi.coupontype.request.CreateCouponTypeRequest
import com.lucablock.digiboxapi.coupontype.service.CouponTypeService
import com.lucablock.digiboxapi.entity.CouponType
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CouponTypeServiceImpl @Autowired constructor(
  private val couponTypeRepository: CouponTypeRepository
): CouponTypeService {
  override fun createCouponType(request: CreateCouponTypeRequest): CouponTypeDto {
    couponTypeRepository.existsCouponTypeByName(request.name).let {
      if (it) {
        throw BadRequestException("ชื่อประเภทคูปองนี้มีอยู่แล้ว กรุณาเปลี่ยนชื่อใหม่")
      }
    }

    val newCouponType = CouponType(
      name = request.name,
    )

    val saveCouponType = couponTypeRepository.save(newCouponType)

    return saveCouponType.couponTypeToDto()
  }

  override fun updateCouponType(
    id: Long,
    request: CreateCouponTypeRequest
  ): CouponTypeDto {
    val couponType = couponTypeRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบประเภทคูปอง")
    }

    couponTypeRepository.findByNameAndIdNot(request.name, id).ifPresent {
      throw BadRequestException("ชื่อประเภทคูปองนี้มีอยู่แล้ว กรุณาเปลี่ยนชื่อใหม่")
    }

    couponType.name = request.name
    val saveCouponType = couponTypeRepository.save(couponType)

    return saveCouponType.couponTypeToDto()
  }

  override fun deleteCouponType(id: Long): Boolean {
    val couponType = couponTypeRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบประเภทคูปอง")
    }

    couponType.isDeleted = true
    couponTypeRepository.save(couponType)

    return true
  }

  override fun getCouponTypeById(id: Long): CouponTypeDto {
    val couponType = couponTypeRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบประเภทคูปอง")
    }

    return couponType.couponTypeToDto()
  }

  override fun getListOfCouponTypes(ascending: Boolean): List<CouponTypeDto> {
    return couponTypeRepository.getListCouponType(ascending)
  }

  override fun getPaginationOfCouponType(
    pageable: Pageable,
    ascending: Boolean
  ): Page<CouponTypeDto> {
    return couponTypeRepository.getPaginationCouponType(pageable, ascending)
  }

  override fun changeCouponTypeStatus(
    id: Long,
    isActive: Boolean
  ): CouponTypeDto {
    val couponType = couponTypeRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบประเภทคูปอง")
    }

    couponType.isActive = isActive
    val updatedCouponType = couponTypeRepository.save(couponType)

    return updatedCouponType.couponTypeToDto()
  }

}