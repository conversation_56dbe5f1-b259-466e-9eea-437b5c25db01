package com.lucablock.digiboxapi.coupontype.service.impl

import com.lucablock.digiboxapi.coupontype.dto.CouponTypeDto
import com.lucablock.digiboxapi.coupontype.repository.CouponTypeRepositoryCustom
import com.lucablock.digiboxapi.entity.QCouponType
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CouponTypeRepositoryImpl: CouponTypeRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qCouponType = QCouponType.couponType

  override fun getListCouponType(ascending: Boolean): List<CouponTypeDto> {
    val criteria = qCouponType.isDeleted.isFalse

    val orderBy = if (ascending) {
      qCouponType.createdDate.asc()
    } else {
      qCouponType.createdDate.desc()
    }

    val query = queryFactory
      .select(qCouponType)
      .from(qCouponType)
      .where(criteria)
      .orderBy(orderBy)
      .fetch()

    return query.map { it.couponTypeToDto() }
  }

  override fun getPaginationCouponType(pageable: Pageable, ascending: Boolean): Page<CouponTypeDto> {
    val criteria = qCouponType.isDeleted.isFalse

    val orderBy = if (ascending) {
      qCouponType.createdDate.asc()
    } else {
      qCouponType.createdDate.desc()
    }

    val query = queryFactory
      .select(qCouponType)
      .from(qCouponType)
      .where(criteria)
      .orderBy(orderBy)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()

    val totalCount = queryFactory
      .select(qCouponType.count())
      .from(qCouponType)
      .where(criteria)
      .fetchOne() ?: 0L

    return PageImpl(query.map { it.couponTypeToDto() }, pageable, totalCount
    )
  }


}