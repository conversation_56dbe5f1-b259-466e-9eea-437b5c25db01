package com.lucablock.digiboxapi.coupontype.service

import com.lucablock.digiboxapi.coupontype.dto.CouponTypeDto
import com.lucablock.digiboxapi.coupontype.request.CreateCouponTypeRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CouponTypeService {
  fun createCouponType(request: CreateCouponTypeRequest): CouponTypeDto
  fun updateCouponType(id: Long, request: CreateCouponTypeRequest): CouponTypeDto
  fun deleteCouponType(id: Long): Boolean
  fun getCouponTypeById(id: Long): CouponTypeDto
  fun getListOfCouponTypes(
    ascending: Boolean,
  ): List<CouponTypeDto>
  fun getPaginationOfCouponType(
    pageable: Pageable,
    ascending: Boolean,
  ): Page<CouponTypeDto>
  fun changeCouponTypeStatus(id: Long, isActive: Boolean): CouponTypeDto
}