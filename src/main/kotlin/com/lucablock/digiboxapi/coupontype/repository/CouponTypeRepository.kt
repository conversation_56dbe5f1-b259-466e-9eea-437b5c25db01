package com.lucablock.digiboxapi.coupontype.repository

import com.lucablock.digiboxapi.entity.CouponType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface CouponTypeRepository: JpaRepository<CouponType, Long>, CouponTypeRepositoryCustom {
  fun findByIdAndIsDeletedFalse(id: Long): Optional<CouponType>
  fun findByNameAndIdNot(name: String, id: Long): Optional<CouponType>
  fun findAllByIsDeletedFalseAndIsActiveTrue(): List<CouponType>
  fun existsCouponTypeByName(name: String): <PERSON><PERSON><PERSON>
}