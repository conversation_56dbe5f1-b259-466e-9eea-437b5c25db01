package com.lucablock.digiboxapi.coupontype.repository

import com.lucablock.digiboxapi.coupontype.dto.CouponTypeDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CouponTypeRepositoryCustom {
  fun getListCouponType(ascending: Boolean): List<CouponTypeDto>
  fun getPaginationCouponType(
    pageable: Pageable,
    ascending: Boolean,
  ): Page<CouponTypeDto>
}