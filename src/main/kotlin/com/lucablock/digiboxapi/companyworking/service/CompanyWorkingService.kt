package com.lucablock.digiboxapi.companyworking.service

import com.lucablock.digiboxapi.companyworking.dto.CompanyWorkingDto
import com.lucablock.digiboxapi.companyworking.request.CompanyWorkingRequest

interface CompanyWorkingService {
  fun updateCompanyWorking(companyWorkingRequest: List<CompanyWorkingRequest>):List<CompanyWorkingDto>
  fun getAllCompanyWorking():List<CompanyWorkingDto>
  fun getCompanyWorkingById(id:Long):CompanyWorkingDto
}