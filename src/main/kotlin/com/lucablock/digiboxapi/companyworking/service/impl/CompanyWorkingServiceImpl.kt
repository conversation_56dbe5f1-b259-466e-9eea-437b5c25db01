package com.lucablock.digiboxapi.companyworking.service.impl

import com.lucablock.digiboxapi.company.repository.CompanyRepository
import com.lucablock.digiboxapi.companyworking.dto.CompanyWorkingDto
import com.lucablock.digiboxapi.companyworking.repository.CompanyWorkingRepository
import com.lucablock.digiboxapi.companyworking.request.CompanyWorkingRequest
import com.lucablock.digiboxapi.companyworking.service.CompanyWorkingService
import com.lucablock.digiboxapi.entity.CompanyWorking
import com.lucablock.digiboxapi.exception.NotFoundException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CompanyWorkingServiceImpl @Autowired constructor(
  private val companyWorkingRepository: CompanyWorkingRepository,
  private val companyRepository: CompanyRepository
): CompanyWorkingService {
  override fun updateCompanyWorking(companyWorkingRequest: List<CompanyWorkingRequest>): List<CompanyWorkingDto> {
    val working = companyWorkingRepository.findAllById(companyWorkingRequest.map { it.id }).let {
      if (it.isEmpty()) {
        throw NotFoundException("ไม่พบข้อมูลวันทำงาน")
      }
    }
    val updateWorkings = companyWorkingRequest.map {
      CompanyWorking(
        id = it.id,
        day = it.day,
        sort = it.sort,
      )
    }
    companyWorkingRepository.saveAll(updateWorkings)
    return updateWorkings.map { it.toCompanyWorkingDto() }
  }

  override fun getAllCompanyWorking(): List<CompanyWorkingDto> {
    return companyWorkingRepository.findAll().map { it.toCompanyWorkingDto() }
  }

  override fun getCompanyWorkingById(id: Long): CompanyWorkingDto {
    val working = companyWorkingRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลวันทำงาน")
    }
    return working.toCompanyWorkingDto()
  }

}