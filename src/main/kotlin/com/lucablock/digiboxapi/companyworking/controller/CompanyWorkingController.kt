package com.lucablock.digiboxapi.companyworking.controller

import com.lucablock.digiboxapi.company.request.CompanyRequest
import com.lucablock.digiboxapi.companyworking.request.CompanyWorkingRequest
import com.lucablock.digiboxapi.companyworking.service.CompanyWorkingService
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/company-working")
class CompanyWorkingController @Autowired internal constructor(
  private val companyWorkingService: CompanyWorkingService
){
  private val logger: Logger = LoggerFactory.getLogger(companyWorkingService::class.java)

  @PreAuthorize("hasAnyRole('USER','ADMIN','SUPER_ADMIN')")
  @PutMapping("/")
  fun updateCompanyWorking(
    @Valid @RequestBody companyWorkingRequest: List<CompanyWorkingRequest>
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลวันทำงานสำเร็จ",
          companyWorkingService.updateCompanyWorking(companyWorkingRequest)
        )
      )
    }catch (e: NotFoundException){
      logger.error("update company error : ${e.message}")
      ResponseEntity.badRequest().body(HttpResponse(false, e.message?: "ไม่พบข้อมูลบริษัท"))
    }catch (e: Exception) {
      logger.error("update company error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลบริษัท กรุณาลองอีกครั้ง"
          )
        )
    }
  }
}