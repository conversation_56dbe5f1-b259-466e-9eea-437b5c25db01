package com.lucablock.digiboxapi.companyworking.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

class CompanyWorkingRequest {
  @NotNull(message = "กรุณาเลือกรหัสวัน")
  val id : Long = 0
  @NotBlank(message = "กรุณาเลือกวัน")
  val day : String = ""
  @NotNull(message = "กรุณาเลือกลำดับวัน")
  val sort : Int = 0
}