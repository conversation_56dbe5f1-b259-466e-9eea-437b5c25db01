package com.lucablock.digiboxapi.couponuser.controller

import com.lucablock.digiboxapi.couponuser.request.ReceiveCouponRequest
import com.lucablock.digiboxapi.couponuser.service.CouponUserService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
class CouponUserController @Autowired constructor(
  private val couponUserService: CouponUserService,
) {
  private val logger = LoggerFactory.getLogger(CouponUserController::class.java)

  @PostMapping("/web/coupon-user/receive")
  fun receiveCoupon(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody request: ReceiveCouponRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "รับคูปองสำเร็จ",
          data = couponUserService.receiveCoupon(userPrincipal, request)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("Error receiving coupon: ${e.message}", e)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ไม่สามารถรับคูปองได้")
      )
    } catch (e: NotFoundException) {
      logger.error("Error receiving coupon: ${e.message}", e)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบคูปอง")
      )
    } catch (e: Exception) {
      logger.error("Unexpected error: ${e.message}", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, "เกิดข้อผิดพลาดในการรับคูปอง")
      )
    }
  }

  @GetMapping("/web/coupon-user/page")
  fun getPageCouponByUserId(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestParam(defaultValue = "0") page: Int = 0,
    @RequestParam(defaultValue = "10") size: Int = 10,
    @RequestParam(required = false) isUsed: Boolean?,
    @RequestParam(required = false) search: String?,
    @RequestParam(defaultValue = "true") ascending: Boolean = true
  ): ResponseEntity<Any> {
    val pageable = PageRequest.of(page, size)
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลคูปองสำเร็จ",
          data = couponUserService.getPageCouponByUserId(userPrincipal, pageable, isUsed, search, ascending)
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching coupons: ${e.message}", e)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ไม่สามารถดึงข้อมูลคูปองได้")
      )
    }
  }

  @GetMapping("/web/coupon-user/list")
  fun getListCouponByUserId(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestParam(required = false) isUsed: Boolean?,
    @RequestParam(required = false) search: String?,
    @RequestParam(defaultValue = "true") ascending: Boolean = true
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลคูปองสำเร็จ",
          data = couponUserService.getListCouponUser(userPrincipal, isUsed, search, ascending)
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching coupons: ${e.message}", e)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ไม่สามารถดึงข้อมูลคูปองได้")
      )
    }
  }
}