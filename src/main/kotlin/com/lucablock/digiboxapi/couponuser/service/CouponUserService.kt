package com.lucablock.digiboxapi.couponuser.service

import com.lucablock.digiboxapi.couponuser.dto.CouponUserDto
import com.lucablock.digiboxapi.couponuser.request.ReceiveCouponRequest
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CouponUserService {
  fun receiveCoupon(userPrincipal: UserPrincipal, request: ReceiveCouponRequest): Boolean
  fun getPageCouponByUserId(
    userPrincipal: UserPrincipal,
    pageable: Pageable,
    isUsed: Boolean? = null,
    search: String? = null,
    ascending: Boolean
  ): Page<CouponUserDto>
  fun getListCouponUser(
    userPrincipal: UserPrincipal,
    isUsed: Boolean? = null,
    search: String? = null,
    ascending: Boolean
  ): List<CouponUserDto>
}