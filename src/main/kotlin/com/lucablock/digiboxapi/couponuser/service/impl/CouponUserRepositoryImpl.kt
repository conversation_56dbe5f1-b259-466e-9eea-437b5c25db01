package com.lucablock.digiboxapi.couponuser.service.impl

import com.lucablock.digiboxapi.couponuser.dto.CouponUserDto
import com.lucablock.digiboxapi.couponuser.repository.CouponUserRepositoryCustom
import com.lucablock.digiboxapi.entity.QCouponUser
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CouponUserRepositoryImpl : CouponUserRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qCouponUser = QCouponUser.couponUser

  override fun getPageCouponByUserId(
    userPrincipal: UserPrincipal,
    pageable: Pageable,
    isUsed: Boolean?,
    search: String?,
    ascending: Boolean
  ): Page<CouponUserDto> {
    val userId = userPrincipal.getUserId()

    var criteria = qCouponUser.userId.eq(userId)

    val orderBy = if (ascending) {
      qCouponUser.id.asc()
    } else {
      qCouponUser.id.desc()
    }

    if (isUsed != null) {
      criteria = criteria.and(qCouponUser.isUsed.eq(isUsed))
    }

    if (search != null && search.isNotBlank()) {
      criteria = criteria.and(qCouponUser.coupon.name.containsIgnoreCase(search))
    }

    val query = queryFactory
      .select(qCouponUser)
      .from(qCouponUser)
      .where(criteria)
      .orderBy(orderBy)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map { it.toCouponUserDto() }

    val total = queryFactory
      .select(qCouponUser.count())
      .from(qCouponUser)
      .where(criteria)
      .fetchOne() ?: 0L

    return PageImpl(query, pageable, total)
  }

  override fun getListCouponUser(
    userPrincipal: UserPrincipal,
    isUsed: Boolean?,
    search: String?,
    ascending: Boolean
  ): List<CouponUserDto> {
    val userId = userPrincipal.getUserId()

    var criteria = qCouponUser.userId.eq(userId)

    val orderBy = if (ascending) {
      qCouponUser.id.asc()
    } else {
      qCouponUser.id.desc()
    }

    if (isUsed != null) {
      criteria = criteria.and(qCouponUser.isUsed.eq(isUsed))
    }

    if (search != null && search.isNotBlank()) {
      criteria = criteria.and(qCouponUser.coupon.name.containsIgnoreCase(search))
    }

    return queryFactory
      .select(qCouponUser)
      .from(qCouponUser)
      .where(criteria)
      .orderBy(orderBy)
      .fetch().map { it.toCouponUserDto() }
  }
}