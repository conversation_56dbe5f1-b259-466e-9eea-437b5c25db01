package com.lucablock.digiboxapi.couponuser.service.impl

import com.lucablock.digiboxapi.coupon.repository.CouponRepository
import com.lucablock.digiboxapi.couponuser.dto.CouponUserDto
import com.lucablock.digiboxapi.couponuser.repository.CouponUserRepository
import com.lucablock.digiboxapi.couponuser.request.ReceiveCouponRequest
import com.lucablock.digiboxapi.couponuser.service.CouponUserService
import com.lucablock.digiboxapi.entity.CouponUser
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CouponUserServiceImpl @Autowired constructor(
  private val couponUserRepository: CouponUserRepository,
  private val couponRepository: CouponRepository,
): CouponUserService {
  override fun receiveCoupon(
    userPrincipal: UserPrincipal,
    request: ReceiveCouponRequest
  ): Boolean {
    val userId = userPrincipal.getUserId()
    val couponIds = request.ids

    val foundCoupons = couponRepository.findAllById(couponIds)
    val foundCouponIds = foundCoupons.map { it.id }.toSet()
    val missingCouponIds = couponIds.filter { it !in foundCouponIds }
    if (missingCouponIds.isNotEmpty()) {
      throw NotFoundException("ไม่พบคูปอง")
    }

    val existingCouponUsers = couponUserRepository.findAllByUserIdAndCouponIdIn(userId, couponIds)
    val alreadyReceivedCouponIds = existingCouponUsers.map { it.couponId }.toSet()
    if (alreadyReceivedCouponIds.isNotEmpty()) {
      throw BadRequestException("คุณได้รับคูปองนี้ไปแล้ว")
    }

    val newCouponUsers = couponIds.map { couponId ->
      CouponUser(
        couponId = couponId,
        userId = userId
      )
    }
    couponUserRepository.saveAll(newCouponUsers)

    return true
  }

  override fun getPageCouponByUserId(
    userPrincipal: UserPrincipal,
    pageable: Pageable,
    isUsed: Boolean?,
    search: String?,
    ascending: Boolean
  ): Page<CouponUserDto> {
    return couponUserRepository.getPageCouponByUserId(userPrincipal, pageable, isUsed, search, ascending)
  }

  override fun getListCouponUser(
    userPrincipal: UserPrincipal,
    isUsed: Boolean?,
    search: String?,
    ascending: Boolean
  ): List<CouponUserDto> {
    return couponUserRepository.getListCouponUser(userPrincipal, isUsed, search, ascending)
  }
}