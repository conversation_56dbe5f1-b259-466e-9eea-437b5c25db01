package com.lucablock.digiboxapi.couponuser.repository

import com.lucablock.digiboxapi.couponuser.dto.CouponUserDto
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CouponUserRepositoryCustom {
  fun getPageCouponByUserId(
    userPrincipal: UserPrincipal,
    pageable: Pageable,
    isUsed: Boolean? = null,
    search: String? = null,
    ascending: Boolean
  ): Page<CouponUserDto>
  fun getListCouponUser(
    userPrincipal: UserPrincipal,
    isUsed: Boolean? = null,
    search: String? = null,
    ascending: Boolean
  ): List<CouponUserDto>
}