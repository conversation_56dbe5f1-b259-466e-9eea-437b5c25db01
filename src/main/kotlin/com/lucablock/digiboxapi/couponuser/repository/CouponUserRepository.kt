package com.lucablock.digiboxapi.couponuser.repository

import com.lucablock.digiboxapi.entity.CouponUser
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface CouponUserRepository: JpaRepository<CouponUser, Long>, CouponUserRepositoryCustom {
  fun findCouponUserByUserIdAndCouponIdAndIsUsedFalse(
    userId: Long,
    couponId: Long
  ): Optional<CouponUser>
  fun findAllByUserIdAndCouponIdIn(
    userId: Long,
    couponIds: List<Long>
  ): List<CouponUser>
  fun findCouponUserByUserIdAndCouponId(userId: Long, couponId: Long): Optional<CouponUser>
}