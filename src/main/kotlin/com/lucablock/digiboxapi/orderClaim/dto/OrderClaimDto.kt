package com.lucablock.digiboxapi.orderClaim.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.order.dto.*
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.util.*

data class GetOrderClaimByIdDto
@QueryProjection
  constructor(
  val id: Int,
  val orderNumber: String,
  var amount: Int,
  var images: List<FilesDto>,
  var description: String? = null,
  var claimProblem: String? = null,
  var claimCause: String? = null,
  var auditDetail: String? = null,
  var auditDescription: String? = null,
  var status: Int,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date,
  var item: ItemDetailClaimDto,
  var shippingDetail: OrderItemShippingDto,
  ): Serializable

data class ItemDetailClaimDto
@QueryProjection
constructor(
  var id: Int,
  var width: Int,
  var length: Int,
  var height: Int,
  var printing: Int,
  var model: OrderItemModelDto?,
  var material: OrderItemMaterialDto?,
  var coating: OrderItemCoatingDto?,
  var specialTechnics: List<OrderItemSpecialTechnicDto?>,
  var isArtwork: Boolean,
  var artworkUrl: String? = null,
  var productDemo: Int,
  var description: String? = null,
  var amount: Int,
  var unitPrice: Double,
  var totalPrice: Double,
) : Serializable

data class FilesDto
@QueryProjection
constructor(
  var id: Int,
  var url: String,
) : Serializable

data class GetAdminOrderClaimDto
@QueryProjection
constructor(
  val id: Int,
  val orderNumber: String,
  var customer: String?,
  var claimAmount: Int,
  var description: String? = null,
  var status: Int,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date,
  var item: AdminItemDetailClaimDto,
): Serializable

data class AdminItemDetailClaimDto
@QueryProjection
constructor(
  var id: Int,
  var model: OrderItemModelDto?,
  var material: OrderItemMaterialDto?,
  var amount: Int,
  var totalPrice: Double,
) : Serializable

data class GetClaimShippingDto
@QueryProjection
constructor(
  val id: Int,
  val orderNumber: String,
  var amount: Int,
  var images: List<FilesDto>,
  var description: String? = null,
  var claimProblem: String? = null,
  var claimCause: String? = null,
  var auditDetail: String? = null,
  var auditDescription: String? = null,
  var status: Int,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date,
  var item: ItemDetailClaimDto,
  var taxDetail: OrderTaxDto,
  var shippingDetail: OrderItemShippingDto,
) : Serializable

data class CauseDto @QueryProjection
constructor(
  val id: Int,
  val name: String,
  val problem: ProblemDto,
  val createdDate: Date,
  val modifiedDate: Date,
): Serializable

data class CauseInProblemDto
@QueryProjection
constructor(
  val id: Int,
  val name: String,
)

data class ProblemDto @QueryProjection
constructor(
  val id: Int,
  val name: String,
  val createdDate: Date,
  val modifiedDate: Date,
  val cause: List<CauseInProblemDto> = emptyList(),
): Serializable