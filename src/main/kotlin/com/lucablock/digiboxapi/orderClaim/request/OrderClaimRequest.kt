package com.lucablock.digiboxapi.orderClaim.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.util.Date

data class OrderClaimRequest (
  val orderId: Int,
  val orderItemId: Int,
  val amount: Int,
  val files: List<String>,
  val description: String? = null
)

data class OrderClaimProblemUpdateRequest (
  val problemId: Int,
  val causeId: Int,
  val auditDetail: String,
)

data class OrderClaimShippingRequest (
  val deliveryDate: Date,
  val trackingNumber: String,
  val shippingName: String,
)

data class AdminConfirmOrderClaimReceivedRequest(
  val recipientName: String,
  val receivedDescription: String?,
)

data class CauseRequest(
  @NotBlank(message = "กรุณากรอกชื่อ")
  val name: String,
  @NotNull(message = "กรุณากรอกปัญหา")
  val problemId: Int,
)