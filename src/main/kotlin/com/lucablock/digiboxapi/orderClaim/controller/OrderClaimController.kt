package com.lucablock.digiboxapi.orderClaim.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.orderClaim.request.*
import com.lucablock.digiboxapi.orderClaim.service.OrderClaimService
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("api/order-claim")
class OrderClaimController {

}