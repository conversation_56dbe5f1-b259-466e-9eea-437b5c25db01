package com.lucablock.digiboxapi.orderClaim.service.impl

import com.lucablock.digiboxapi.entity.QClaimProblem
import com.lucablock.digiboxapi.orderClaim.dto.CauseDto
import com.lucablock.digiboxapi.orderClaim.dto.CauseInProblemDto
import com.lucablock.digiboxapi.orderClaim.dto.ProblemDto
import com.lucablock.digiboxapi.orderClaim.dto.QProblemDto
import com.lucablock.digiboxapi.orderClaim.repository.ClaimProblemRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ClaimProblemRepositoryImpl: ClaimProblemRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qClaimProblem = QClaimProblem.claimProblem

  override fun getClaimProblemsPage(pageable: Pageable, search: String?): Page<ProblemDto> {
    var criteria = qClaimProblem.id.isNotNull

    if (search != null) {
      criteria = criteria.and(qClaimProblem.name.containsIgnoreCase(search))
    }

    val firstQuery = queryFactory
      .select(qClaimProblem)
      .from(qClaimProblem)
      .where(criteria)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .orderBy(qClaimProblem.createdDate.desc())
      .fetch()

    val query = firstQuery.map {
      ProblemDto(
        it.id,
        it.name,
        it.createdDate,
        it.modifiedDate
      )
    }

    val totalQuery = queryFactory
      .select(qClaimProblem)
      .from(qClaimProblem)
      .where(criteria)
      .fetch().size.toLong()

    return PageImpl(query, pageable, totalQuery)
  }

  override fun getClaimProblems(pageable: Pageable, search: String?): Page<ProblemDto> {
    var criteria = qClaimProblem.id.isNotNull

    if (search != null) {
      criteria = criteria.and(qClaimProblem.name.containsIgnoreCase(search))
    }

    val firstQuery = queryFactory
      .select(qClaimProblem)
      .from(qClaimProblem)
      .where(criteria)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()

    val query = firstQuery.map {
      ProblemDto(
        it.id,
        it.name,
        it.createdDate,
        it.modifiedDate,
        it.cause!!.map { cause ->
          CauseInProblemDto(
            cause.id,
            cause.name
          )
        }
      )
    }

    val totalQuery = queryFactory
      .select(qClaimProblem)
      .from(qClaimProblem)
      .where(criteria)
      .fetch().size.toLong()

    return PageImpl(query, pageable, totalQuery)
  }

}