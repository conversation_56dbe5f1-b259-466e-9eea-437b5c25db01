package com.lucablock.digiboxapi.orderClaim.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.ClaimCause
import com.lucablock.digiboxapi.entity.ClaimProblem
import com.lucablock.digiboxapi.entity.FileClaim
import com.lucablock.digiboxapi.entity.OrderClaim
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.order.dto.GetOrderListDto
import com.lucablock.digiboxapi.order.repository.OrderItemRepository
import com.lucablock.digiboxapi.order.repository.OrderRepository
import com.lucablock.digiboxapi.orderClaim.dto.*
import com.lucablock.digiboxapi.orderClaim.repository.ClaimCauseRepository
import com.lucablock.digiboxapi.orderClaim.repository.ClaimProblemRepository
import com.lucablock.digiboxapi.orderClaim.repository.FileClaimRepository
import com.lucablock.digiboxapi.orderClaim.repository.OrderClaimRepository
import com.lucablock.digiboxapi.orderClaim.request.*
import com.lucablock.digiboxapi.orderClaim.service.OrderClaimService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.*

@Service
class OrderClaimServiceImpl @Autowired internal constructor(
  private val orderRepository: OrderRepository,
  private val orderItemRepository: OrderItemRepository,
  private val orderClaimRepository: OrderClaimRepository,
  private val fileClaimRepository: FileClaimRepository,
  private val claimProblemRepository: ClaimProblemRepository,
  private val claimCauseRepository: ClaimCauseRepository,
  private val s3Service: S3Service
) : OrderClaimService {

}