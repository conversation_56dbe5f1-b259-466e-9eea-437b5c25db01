package com.lucablock.digiboxapi.orderClaim.service.impl
import com.lucablock.digiboxapi.entity.QOrder
import com.lucablock.digiboxapi.entity.QOrderClaim
import com.lucablock.digiboxapi.order.dto.*
import com.lucablock.digiboxapi.orderClaim.dto.*
import com.lucablock.digiboxapi.orderClaim.repository.OrderClaimRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class OrderClaimRepositoryImpl: OrderClaimRepositoryCustom {

}

