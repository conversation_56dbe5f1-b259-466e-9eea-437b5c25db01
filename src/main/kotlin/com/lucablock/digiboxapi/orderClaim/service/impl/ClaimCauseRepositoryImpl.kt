package com.lucablock.digiboxapi.orderClaim.service.impl

import com.lucablock.digiboxapi.entity.QClaimCause
import com.lucablock.digiboxapi.orderClaim.dto.CauseDto
import com.lucablock.digiboxapi.orderClaim.dto.QCauseDto
import com.lucablock.digiboxapi.orderClaim.dto.QCauseInProblemDto
import com.lucablock.digiboxapi.orderClaim.dto.QProblemDto
import com.lucablock.digiboxapi.orderClaim.repository.ClaimCauseRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ClaimCauseRepositoryImpl: ClaimCauseRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qClaimCause = QClaimCause.claimCause

  override fun getClaimCausesPage(pageable: Pageable, search: String?): Page<CauseDto> {
    var criteria = qClaimCause.id.isNotNull

    if (search != null) {
      criteria = criteria.and(qClaimCause.name.containsIgnoreCase(search))
    }

    val firstQuery = queryFactory
      .select(qClaimCause)
      .from(qClaimCause)
      .where(criteria)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .orderBy(qClaimCause.createdDate.desc())
      .fetch()

    val query = firstQuery.map {
      CauseDto(
        it.id,
        it.name,
        it.claimProblem!!.toProblemDto(),
        it.createdDate,
        it.modifiedDate
      )
    }

    val totalQuery = queryFactory
      .select(qClaimCause)
      .from(qClaimCause)
      .where(criteria)
      .fetch().size.toLong()

    return PageImpl(query, pageable, totalQuery)
  }


}