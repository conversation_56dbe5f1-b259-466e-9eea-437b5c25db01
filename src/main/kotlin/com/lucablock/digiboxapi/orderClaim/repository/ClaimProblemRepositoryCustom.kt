package com.lucablock.digiboxapi.orderClaim.repository

import com.lucablock.digiboxapi.orderClaim.dto.ProblemDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface ClaimProblemRepositoryCustom {
  fun getClaimProblemsPage(pageable: Pageable, search: String?): Page<ProblemDto>
  fun getClaimProblems(pageable: Pageable, search: String?): Page<ProblemDto>
}