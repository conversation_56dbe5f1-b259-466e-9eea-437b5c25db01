package com.lucablock.digiboxapi.orderClaim.repository

import com.lucablock.digiboxapi.entity.ClaimCause
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query

interface ClaimCauseRepository: JpaRepository<ClaimCause, Int>, ClaimCauseRepositoryCustom {
  @Query("SELECT * FROM claim_cause WHERE name = :cause", nativeQuery = true)
  fun findByName(cause: String): ClaimCause?

  fun existsClaimProblemByNameAndIdNot(name: String, id: Int): <PERSON><PERSON><PERSON>
}