package com.lucablock.digiboxapi.orderClaim.repository

import com.lucablock.digiboxapi.entity.ClaimProblem
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query

interface ClaimProblemRepository: JpaRepository<ClaimProblem, Int>, ClaimProblemRepositoryCustom {
  @Query("SELECT * FROM claim_problem WHERE name = :problem", nativeQuery = true)
  fun findByName(problem: String): ClaimProblem?
}