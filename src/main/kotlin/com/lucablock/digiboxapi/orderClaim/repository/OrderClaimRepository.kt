package com.lucablock.digiboxapi.orderClaim.repository

import com.lucablock.digiboxapi.entity.OrderClaim
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface OrderClaimRepository: JpaRepository<OrderClaim, Int>, OrderClaimRepositoryCustom {
  @Query("select * from order_claim where order_item_id = :orderItemId", nativeQuery = true)
  fun findByOrderItemId(orderItemId: Int): OrderClaim?
}