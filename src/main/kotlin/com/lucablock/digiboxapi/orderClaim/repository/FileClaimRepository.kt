package com.lucablock.digiboxapi.orderClaim.repository

import com.lucablock.digiboxapi.entity.FileClaim
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface FileClaimRepository: JpaRepository<FileClaim, Int> {
  @Transactional
  @Modifying
  @Query("delete from file_claim  where order_claim_id = :orderClaimId", nativeQuery = true)
  fun deleteByOrderClaimId(orderClaimId: Int): Any

  @Query("select * from file_claim  where order_claim_id = :id", nativeQuery = true)
  fun findByOrderClaimId(id: Int): List<FileClaim>
}