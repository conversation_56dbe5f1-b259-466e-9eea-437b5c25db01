package com.lucablock.digiboxapi.productcategoryconfig.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import com.querydsl.core.annotations.QueryProjection
import jakarta.persistence.Column
import java.util.*

data class ProductCategoryConfigDto
@QueryProjection
constructor(
  val id: Long,
  val productId: Long,
  val productCategoryId: Long,
  val productCategoryName : String?
)

