package com.lucablock.digiboxapi.exception

import com.lucablock.digiboxapi.response.ErrorResponseMessage
import com.lucablock.digiboxapi.response.HttpResponse
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
class RestExceptionHandler() {

  //other exception handlers
  @ExceptionHandler(value = [(BadRequestException::class)])
  protected fun badRequestException(
    ex: BadRequestException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Bad Request")
    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError)
  }

  @ExceptionHandler(value = [(AccessDeniedException::class)])
  protected fun accessDeniedException(
    ex: AccessDeniedException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Access Denied")
    return ResponseEntity.status(HttpStatus.FORBIDDEN).body(apiError)
  }

  @ExceptionHandler(value = [(NotFoundException::class)])
  protected fun notFoundException(
    ex: NotFoundException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Notfound")
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(apiError)
  }

  @ExceptionHandler(value = [(UsernameNotFoundException::class)])
  protected fun usernameNotFoundException(
    ex: UsernameNotFoundException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Notfound")
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(apiError)
  }

  @ExceptionHandler(value = [(ConflictException::class)])
  protected fun conflictException(
    ex: ConflictException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Conflict")
    return ResponseEntity.status(HttpStatus.CONFLICT).body(apiError)
  }

  @ExceptionHandler(value = [(InternalServerException::class)])
  protected fun internalServerException(
    ex: InternalServerException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Internal Server Error")
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(apiError)
  }

  @ExceptionHandler(value = [(ForbiddenException::class)])
  protected fun forbiddenException(
    ex: ForbiddenException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "Access Dinied")
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(apiError)
  }

  @ExceptionHandler(value = [(Exception::class)])
  protected fun exception(
    ex: Exception
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message?.substringAfterLast(": ") ?: "Internal Server Error")
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(apiError)
  }

  @ExceptionHandler(value = [(NoContentException::class)])
  protected fun noContentException(
    ex: NoContentException
  ): ResponseEntity<Any> {
    val apiError = ErrorResponseMessage(errorMessage = ex.message ?: "No Content")
    return ResponseEntity.status(HttpStatus.NO_CONTENT).body(apiError)
  }

  @ExceptionHandler(value = [MethodArgumentNotValidException::class])
  protected fun handleMethodArgumentNotValid(
    ex: MethodArgumentNotValidException
  ): ResponseEntity<Any> {
    val errorMessages = ex.bindingResult
      .fieldErrors
      .mapNotNull { it.defaultMessage }

    val body = mutableMapOf<String, Any>(
//      "errors" to ex.bindingResult.fieldErrors.map { it.defaultMessage }
      "errors" to ex.bindingResult.fieldErrors.map { "${it.field}: ${it.defaultMessage}" }
    )

    val combinedErrorMessage = errorMessages.joinToString(separator = ", ")

    val firstErrorMessage = ex.bindingResult
      .fieldErrors
      .firstOrNull()
      ?.defaultMessage ?: "Invalid request"

    val res = HttpResponse(
      status = false,
      message = combinedErrorMessage,
      data = body
    )

    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(res)
  }
}