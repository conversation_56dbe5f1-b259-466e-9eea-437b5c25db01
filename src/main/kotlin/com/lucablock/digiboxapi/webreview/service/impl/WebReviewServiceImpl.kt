package com.lucablock.digiboxapi.webreview.service.impl

import com.lucablock.digiboxapi.entity.WebReview
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.repository.UserRepository
import com.lucablock.digiboxapi.webreview.dto.WebReviewSummaryDto
import com.lucablock.digiboxapi.webreview.repository.WebReviewRepository
import com.lucablock.digiboxapi.webreview.request.CreateWebReviewRequest
import com.lucablock.digiboxapi.webreview.service.WebReviewService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode

@Service
class WebReviewServiceImpl @Autowired internal constructor(
  private val webReviewRepository: WebReviewRepository,
  private val userRepository: UserRepository
) : WebReviewService {
  @CacheEvict(value = ["webReviewSummary"], key = "'summary'")
  override fun createWebReview(userPrincipal: UserPrincipal, request: CreateWebReviewRequest): WebReview {
    val user = userRepository.findById(userPrincipal.getUserId())
      .orElseThrow { IllegalArgumentException("User not found") }
    val webReview = WebReview(
      userId = user.id,
      rating = request.rating,
      comment = request.comment
    )
    return webReviewRepository.save(webReview)
  }

  override fun getWebReviews(userPrincipal: UserPrincipal, pageable: PageRequest): Page<WebReview> {
    return webReviewRepository.findAll(pageable)
  }

  @CacheEvict(value = ["webReviewSummary"], key = "'summary'")
  override fun deleteWebReview(userPrincipal: UserPrincipal, webReviewId: Long) {
    val webReview = webReviewRepository.findById(webReviewId)
      .orElseThrow { IllegalArgumentException("Web review not found") }
    webReviewRepository.delete(webReview)
  }
  
  @Cacheable(value = ["webReviewSummary"], key = "'summary'")
  override fun getWebReviewSummary(): WebReviewSummaryDto {
    val ratingDistribution = webReviewRepository.getRatingDistribution()
    
    val distributionMap = mutableMapOf<Int, Long>()
    val percentageMap = mutableMapOf<Int, Double>()
    
    for (i in 1..5) {
      distributionMap[i] = 0L
      percentageMap[i] = 0.0
    }
    
    var totalReviews = 0L
    var totalScore = 0L
    
    ratingDistribution.forEach { row ->
      val rating = (row[0] as Number).toInt()
      val count = (row[1] as Number).toLong()
      distributionMap[rating] = count
      totalReviews += count
      totalScore += rating * count
    }
    
    val averageRating = if (totalReviews > 0) {
      BigDecimal(totalScore.toDouble() / totalReviews)
        .setScale(1, RoundingMode.HALF_UP)
        .toDouble()
    } else {
      0.0
    }
    
    if (totalReviews > 0) {
      distributionMap.forEach { (rating, count) ->
        val percentage = BigDecimal((count.toDouble() / totalReviews) * 100)
          .setScale(2, RoundingMode.HALF_UP)
          .toDouble()
        percentageMap[rating] = percentage
      }
    }
    
    return WebReviewSummaryDto(
      averageRating = averageRating,
      totalReviews = totalReviews,
      ratingDistribution = distributionMap,
      percentageDistribution = percentageMap
    )
  }
}