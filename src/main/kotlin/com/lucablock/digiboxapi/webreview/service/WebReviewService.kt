package com.lucablock.digiboxapi.webreview.service

import com.lucablock.digiboxapi.entity.WebReview
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.webreview.dto.WebReviewSummaryDto
import com.lucablock.digiboxapi.webreview.request.CreateWebReviewRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest

interface WebReviewService {
  fun createWebReview(userPrincipal: UserPrincipal, request: CreateWebReviewRequest): WebReview
  fun getWebReviews(userPrincipal: UserPrincipal, pageable: PageRequest): Page<WebReview>
  fun deleteWebReview(userPrincipal: UserPrincipal, webReviewId: Long)
  fun getWebReviewSummary(): WebReviewSummaryDto
}