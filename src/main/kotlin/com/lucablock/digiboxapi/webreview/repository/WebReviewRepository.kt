package com.lucablock.digiboxapi.webreview.repository

import com.lucablock.digiboxapi.entity.WebReview
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface WebReviewRepository : JpaRepository<WebReview, Long> {
  
  @Query("""
    SELECT 
      AVG(CAST(w.rating AS double)) as averageRating,
      COUNT(w) as totalReviews,
      SUM(CASE WHEN w.rating = 5 THEN 1 ELSE 0 END) as fiveStar,
      SUM(CASE WHEN w.rating = 4 THEN 1 ELSE 0 END) as fourStar,
      SUM(CASE WHEN w.rating = 3 THEN 1 ELSE 0 END) as threeStar,
      SUM(CASE WHEN w.rating = 2 THEN 1 ELSE 0 END) as twoStar,
      SUM(CASE WHEN w.rating = 1 THEN 1 ELSE 0 END) as oneStar
    FROM WebReview w
  """)
  fun getReviewSummaryStatistics(): List<Any>
  
  @Query("SELECT w.rating, COUNT(w) FROM WebReview w GROUP BY w.rating")
  fun getRatingDistribution(): List<Array<Any>>
}