package com.lucablock.digiboxapi.webreview.controller

import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.webreview.request.CreateWebReviewRequest
import com.lucablock.digiboxapi.webreview.service.WebReviewService
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
class WebReviewController @Autowired internal constructor(
  private val webReviewService: WebReviewService
) {
  private val logger: Logger = LoggerFactory.getLogger(WebReviewController::class.java)

  @PostMapping("/web/web-reviews")
  fun createWebReview(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody request: CreateWebReviewRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ให้คะแนนเว็บไซต์สำเร็จ",
          webReviewService.createWebReview(userPrincipal, request)
        )
      )
    } catch (e: IllegalArgumentException) {
      logger.error("Error creating web review: ${e.message}")
      ResponseEntity.badRequest().body(
        HttpResponse(
          false,
          e.message ?: "ไม่สามารถให้คะแนนเว็บไซต์ กรุณาลองอีกครั้ง",
          null
        )
      )
    } catch (e: Exception) {
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "ไม่สามารถให้คะแนนเว็บไซต์ กรุณาลองอีกครั้ง",
          null
        )
      )
    }
  }

  @GetMapping("/web/web-reviews")
  fun getWebReviews(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestParam(value = "page", defaultValue = "0") page: Int,
    @RequestParam(value = "size", defaultValue = "10") size: Int
  ): ResponseEntity<Any> {
    return try {
      val pageable = PageRequest.of(page, size)
      val reviews = webReviewService.getWebReviews(userPrincipal, pageable)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ดึงข้อมูลรีวิวเว็บไซต์สำเร็จ",
          reviews
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching web reviews: ${e.message}")
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "ไม่สามารถดึงข้อมูลรีวิวเว็บไซต์ กรุณาลองอีกครั้ง",
          null
        )
      )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/web-reviews/{webReviewId}")
  fun deleteWebReview(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @PathVariable webReviewId: Long
  ): ResponseEntity<Any> {
    return try {
      webReviewService.deleteWebReview(userPrincipal, webReviewId)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบรีวิวเว็บไซต์สำเร็จ",
          null
        )
      )
    } catch (e: IllegalArgumentException) {
      logger.error("Error deleting web review: ${e.message}")
      ResponseEntity.badRequest().body(
        HttpResponse(
          false,
          e.message ?: "ไม่สามารถลบรีวิวเว็บไซต์ กรุณาลองอีกครั้ง",
          null
        )
      )
    } catch (e: Exception) {
      logger.error("Error deleting web review: ${e.message}")
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "ไม่สามารถลบรีวิวเว็บไซต์ กรุณาลองอีกครั้ง",
          null
        )
      )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @GetMapping("/admin/web-reviews/summary")
  fun getWebReviewSummary(): ResponseEntity<Any> {
    return try {
      val summary = webReviewService.getWebReviewSummary()
      ResponseEntity.ok(
        HttpResponse(
          true,
          "Success",
          summary
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching web review summary: ${e.message}")
      ResponseEntity.status(500).body(
        HttpResponse(
          false,
          "Failed to fetch review summary",
          null
        )
      )
    }
  }
}