package com.lucablock.digiboxapi.token.dto

import jakarta.validation.constraints.NotNull
import org.springframework.data.annotation.Id
import org.springframework.data.redis.core.RedisHash

@RedisHash(value = "token", timeToLive = 864000)
data class TokenDto(
  @Id
  val userId: String,
  @NotNull
  val token: String,
  val multipleLogin: Boolean = false
)
@RedisHash(value = "token-forgot", timeToLive = 864000)
data class ForgotTokenDto(
  @Id
  val userId: String,
  @NotNull
  val token: String,
)