package com.lucablock.digiboxapi.token.service.impl

import com.lucablock.digiboxapi.token.dto.ForgotTokenDto
import com.lucablock.digiboxapi.token.dto.TokenDto
import com.lucablock.digiboxapi.token.repository.ForgotTokenRepository
import com.lucablock.digiboxapi.token.repository.TokenRepository
import com.lucablock.digiboxapi.token.service.TokenService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class TokenServiceImpl @Autowired internal constructor(
  private val tokenRepository: TokenRepository,
  private val forgotTokenRepository: ForgotTokenRepository
) : TokenService {

  override fun cacheToken(tokenDto: TokenDto) {
    tokenRepository.save(tokenDto)
  }

  override fun cacheForgotToken(forgotTokenDto: ForgotTokenDto) {
    forgotTokenRepository.save(forgotTokenDto)
  }

  override fun isValidToken(tokenDto: TokenDto): Boolean {
    val tokenRes = tokenRepository.findById(tokenDto.userId)
    if (tokenRes.isPresent) {
      return tokenRes.get().multipleLogin || tokenRes.get().token == tokenDto.token
    }
    return false
  }

  override fun revokeToken(userId: Long) {
    tokenRepository.deleteById(userId.toString())
  }

  override fun revokeForgotToken(userId: Int) {
    forgotTokenRepository.deleteById(userId.toString())
  }
}