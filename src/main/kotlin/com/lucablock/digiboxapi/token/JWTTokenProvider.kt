package com.lucablock.digiboxapi.token

import com.fasterxml.jackson.databind.ObjectMapper
import com.lucablock.digiboxapi.config.AppProperties
import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.user.dto.ForgotJwt
import com.lucablock.digiboxapi.user.dto.UserJwt
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.jsonwebtoken.*

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component
import java.util.*


@Component
class JWTTokenProvider {
  @Value("\${jwt.secret}")
  private val jwtSecret: String? = null

  @Value("\${jwt.expirationInMs}")
  private val jwtExpirationInMs = 0

  @Autowired
  lateinit var objectMapper: ObjectMapper

  private val appProperties: AppProperties? = null

  fun generateToken(userPrincipal: UserPrincipal): String? {
    val userJwt = UserJwt(
      uid = userPrincipal.getUserId(),
      email = userPrincipal.email,
      username = userPrincipal.username,
      cid = userPrincipal.getCompanyId(),
      userType = userPrincipal.getUserType(),
      userTypeId = userPrincipal.getUserTypeId()
    )

    val payload = objectMapper.writeValueAsString(userJwt)

    return Jwts.builder().setIssuer("LUCABLOCK")
      .setIssuedAt(Date())
      .setExpiration(Date(Date().time + jwtExpirationInMs))
      .setSubject(userPrincipal.email)
//     .setSubject(userPrincipal.username) //username
      .setId(userPrincipal.getUserId().toString())  //userId
//    .setAudience(userPrincipal.getUserType()) //userRole
      .claim("user", payload)
      .signWith(SignatureAlgorithm.HS512, jwtSecret).compact()
  }
  fun generateTokenForgotEmail(user: User): String? {
    val forgotJwt = ForgotJwt(
      uid = user.id.toInt(),
      email = user.email!!,
    )
    val payload = objectMapper.writeValueAsString(forgotJwt)

    return Jwts.builder().setIssuer("LUCABLOCK")
      .setIssuedAt(Date())
      .setExpiration(Date(Date().time + jwtExpirationInMs))
      .setId(user.id.toString())
      .claim("user", payload)
      .signWith(SignatureAlgorithm.HS512, jwtSecret).compact()
  }

  fun createToken(authentication: Authentication): String? {
    val userPrincipal = authentication.principal as UserPrincipal
    val userJwt = UserJwt(
      uid = userPrincipal.getUserId(),
      email = userPrincipal.email,
      username = userPrincipal.username,
      cid = userPrincipal.getCompanyId(),
      userType = userPrincipal.getUserType(),
      userTypeId = userPrincipal.getUserTypeId()
    )
    val payload = objectMapper.writeValueAsString(userJwt)
    return Jwts.builder().setIssuer("LUCABLOCK")
      .setIssuedAt(Date())
      .setExpiration(Date(Date().time + jwtExpirationInMs))
//                .setSubject(userPrincipal.username) //username
      .setId(userPrincipal.getUserId().toString())  //userId
//                .setAudience(userPrincipal.getUserType()) //userRole
      .claim("user", payload)
      .signWith(SignatureAlgorithm.HS512, jwtSecret).compact()
  }

  fun validateToken(jwt: String): Boolean {
    try {
      Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(jwt)
      return true
    } catch (ex: SignatureException) {
//            ex.printStackTrace()
//            log.error("Invalid JWT signature")
    } catch (ex: MalformedJwtException) {
//            ex.printStackTrace()
//            log.error("Invalid JWT token")
    } catch (ex: ExpiredJwtException) {
//            ex.printStackTrace()
//            log.error("Expired JWT token")
    } catch (ex: UnsupportedJwtException) {
//            ex.printStackTrace()
//            log.error("Unsupported JWT token")
    } catch (ex: IllegalArgumentException) {
//            ex.printStackTrace()
//            log.error("JWT claims string is empty.")
    }
    return false
  }

  fun getUserNameFromToken(token: String): String {
    return Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).body.subject
  }

  fun getUserId(token: String): Int {
    return Integer.parseInt(
      Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token.replace("Bearer ", "")).body.id
    )
  }

  fun getClaims(token: String): Claims {
    return Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token.replace("Bearer ", "")).body
  }

  fun getPayload(token: String): String {
    return Jwts.parser().setSigningKey(jwtSecret).parse(token.replace("Bearer ", "")).toString()
  }
}