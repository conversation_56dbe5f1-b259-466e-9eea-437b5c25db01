package com.lucablock.digiboxapi.user.service.impl

import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.service.UserPrincipalFactory
import org.springframework.stereotype.Service

@Service
class DefaultUserPrincipalFactory : UserPrincipalFactory {
  override fun create(user: User, userRole: String): UserPrincipal {
    return UserPrincipal().apply {
      setUserId(user.id)
      setEmail(user.email)
      setUsername(user.username)
      setPassword(user.password)
      setUserTypeId(user.userTypeId)
      setEmailVerify(user.emailVerified)
      setUserType(userRole)
    }
  }
}