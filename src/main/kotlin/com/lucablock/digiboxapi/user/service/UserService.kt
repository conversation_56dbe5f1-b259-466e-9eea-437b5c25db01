package com.lucablock.digiboxapi.user.service

import com.lucablock.digiboxapi.entity.Address
import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.user.dto.*
import com.lucablock.digiboxapi.user.request.CreateUserAddressRequest
import com.lucablock.digiboxapi.user.request.CreateUserRequest
import com.lucablock.digiboxapi.user.request.UpdateUserAddressRequest
import org.springframework.web.multipart.MultipartFile

interface UserService {
  fun createUser(createUserRequest: CreateUserRequest, userType: Long, host: String)
  fun findUserById(userId: Long): User
  fun getUserById(userId: Long): UserMeDto
  fun getUserDetailById(userId: Long): UserDetailDto
  fun updateUserMe(userId: Long, updateProfileRequest: String, file: MultipartFile?)
  fun updatePassword(id: Int, updatePasswordDto: UpdatePasswordDto)
  fun emailForgotPassword(emailForgotPasswordDto: EmailForgotPasswordDto)
  fun forgotPassword(forgotPasswordDto: ForgotPasswordDto)
  fun verifyEmail(userPrincipal: UserPrincipal, host: String)
  fun verifyByEmail(token: String)
  fun createAddress(userId: Int, createUserAddressRequest: CreateUserAddressRequest)
  fun findAllAddressByUserId(userId: Int, isTax: Boolean): List<Address>
  fun updateAddress(userId: Int, id: Int, updateUserAddressRequest: UpdateUserAddressRequest)
  fun deleteAddress(userId: Int, id: Int)
  fun deleteAccount(userId: Long)
}