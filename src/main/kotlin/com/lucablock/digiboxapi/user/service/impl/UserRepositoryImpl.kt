package com.lucablock.digiboxapi.user.service.impl

import com.lucablock.digiboxapi.entity.QUser
import com.lucablock.digiboxapi.entity.QUserType
import com.lucablock.digiboxapi.user.dto.*
import com.lucablock.digiboxapi.user.repository.UserRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.stereotype.Service

@Service
class UserRepositoryImpl : UserRepositoryCustom {

  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  private val qUser = QUser.user
  private val qUserType = QUserType.userType

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  override fun getUserById(userId: Long): UserMeDto {
    val criteria = qUser.id.eq(userId)
    val query = queryFactory.select(qUser)
      .from(qUser)
      .leftJoin(qUserType).on(qUserType.id.eq(qUser.userTypeId))
      .where(criteria)
    return query.fetchOne()?.let { user ->
      UserMeDto(
        user.id,
        user.fullName,
        user.email,
        user.phoneNumber,
        user.imageUrl,
        user.address.filter({ it?.isTax == false }).map {
          ShippingAddressDto(
            it!!.id,
            it.name,
            it.phoneNumber,
            it.address,
            it.zipCode,
            it.province,
            it.district,
            it.subDistrict,
            it.email?:"",
            it.isDefault?:false
          )
        },
        user.address.filter({ it?.isTax == true }).map {
          TaxAddressDto(
            it!!.id,
            it.name,
            it.phoneNumber,
            it.address,
            it.zipCode,
            it.province,
            it.district,
            it.subDistrict,
            it.taxPayerType?: 1,
            it.taxId?:"",
            it.email?:"",
            it.isDefault?:false
          )
        },
        user.userType?.isAdmin ?: false,
        user.userType?.isAnonymous ?: false,
        user.userType?.isUser ?: false,
        user.userType?.isSuperAdmin ?: false,
        user.emailVerified,
        user.provider?:""
      )
    } ?: throw Exception("User not found")
  }

  override fun getUserDetailById(userId: Long): UserDetailDto {
    val criteria = qUser.id.eq(userId)
    val query = queryFactory.select(
      QUserDetailDto(
        qUser.id,
        qUser.firstName?.concat(" ")?.concat(qUser.lastName),
        qUser.email,
        qUserType.isAdmin,
        qUserType.isAnonymous,
        qUserType.isUser,
        qUserType.isSuperAdmin,
        qUser.emailVerified
      )
    ).from(qUser)
      .leftJoin(qUserType).on(qUserType.id.eq(qUser.userTypeId))
      .where(criteria)
      .groupBy(
        qUser.id,
        qUser.createdDate,
        qUser.firstName,
        qUser.email,
        qUserType.isAdmin,
        qUserType.isAnonymous,
        qUserType.isUser,
        qUserType.isSuperAdmin
      )
    return query.fetchFirst()
  }
}