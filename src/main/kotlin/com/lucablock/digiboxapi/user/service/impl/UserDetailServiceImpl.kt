package com.lucablock.digiboxapi.user.service.impl

import com.lucablock.digiboxapi.entity.UserRoleEnum
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.repository.UserRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.stereotype.Service

@Service
class UserDetailServiceImpl : UserDetailsService {

  @Autowired
  lateinit var userRepository: UserRepository

  override fun loadUserByUsername(username: String): UserDetails {
    val userOpt = userRepository.findByUsernameOrEmail(username)
    if (userOpt.isPresent) {
      val user = userOpt.get()
      val userPrincipal = UserPrincipal()
      userPrincipal.setUserId(user.id)
      userPrincipal.setEmail(user.email)
      userPrincipal.setUsername(user.username)
      userPrincipal.setPassword(user.password)
      userPrincipal.setUserTypeId(user.userTypeId)
      userPrincipal.setEmailVerify(user.emailVerified)
      val userType = when {
        user.userType == null -> {
          UserRoleEnum.ROLE_GUEST.name
        }

        user.userType!!.isSuperAdmin -> {
          UserRoleEnum.ROLE_SUPER_ADMIN.name
        }

        user.userType!!.isAdmin -> {
          UserRoleEnum.ROLE_ADMIN.name
        }

        else -> {
          UserRoleEnum.ROLE_USER.name
        }
      }
      userPrincipal.setUserType(userType)
      return userPrincipal
    }

    throw UsernameNotFoundException("ไม่สามารถเข้าสู่ระบบได้ อีเมลหรือรหัสผ่านไม่ถูกต้อง")
  }
}