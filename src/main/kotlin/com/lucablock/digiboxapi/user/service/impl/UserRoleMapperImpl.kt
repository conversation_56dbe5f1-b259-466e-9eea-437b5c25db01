package com.lucablock.digiboxapi.user.service.impl

import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.entity.UserRoleEnum
import com.lucablock.digiboxapi.user.service.UserRoleMapper
import org.springframework.stereotype.Service

@Service
class DefaultUserRoleMapper : UserRoleMapper {
  override fun mapUserToRole(user: User): String {
    return when {
      user.userType == null -> UserRoleEnum.ROLE_GUEST.name
      user.userType!!.isSuperAdmin -> UserRoleEnum.ROLE_SUPER_ADMIN.name
      user.userType!!.isAdmin -> UserRoleEnum.ROLE_ADMIN.name
      else -> UserRoleEnum.ROLE_USER.name
    }
  }
}