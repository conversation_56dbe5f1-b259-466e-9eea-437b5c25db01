package com.lucablock.digiboxapi.user.service.impl

import com.lucablock.digiboxapi.app.utility.decodeThailandCharacters
import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.Address
import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.entity.UserRoleEnum
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NoContentException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.oauth2.dto.AuthProvider
import com.lucablock.digiboxapi.token.JWTTokenProvider
import com.lucablock.digiboxapi.token.dto.ForgotTokenDto
import com.lucablock.digiboxapi.token.dto.TokenDto
import com.lucablock.digiboxapi.token.service.TokenService
import com.lucablock.digiboxapi.user.dto.*
import com.lucablock.digiboxapi.user.repository.AddressRepository
import com.lucablock.digiboxapi.user.repository.UserRepository
import com.lucablock.digiboxapi.user.request.CreateUserAddressRequest
import com.lucablock.digiboxapi.user.request.CreateUserRequest
import com.lucablock.digiboxapi.user.request.UpdateProfileRequest
import com.lucablock.digiboxapi.user.request.UpdateUserAddressRequest
import com.lucablock.digiboxapi.user.service.UserService
import com.nimbusds.jose.shaded.gson.Gson
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import org.thymeleaf.context.Context
import org.thymeleaf.spring6.SpringTemplateEngine
import java.nio.charset.StandardCharsets
import java.util.*
import java.util.regex.Pattern

@Service
class UserServiceImpl @Autowired internal constructor(
  private val authenticationManager: AuthenticationManager,
  private val userRepository: UserRepository,
  private val bCryptPasswordEncoder: BCryptPasswordEncoder,
  private val jwtTokenProvider: JWTTokenProvider,
  private val tokenService: TokenService,
  private val addressRepository: AddressRepository,
  private val emailSender: JavaMailSender,
  private val templateEngine: SpringTemplateEngine,
  private val s3Service: S3Service
) : UserService {

  private final val gson: Gson = Gson()

  @Value("\${appUrl}")
  private val appUrl: String? = null


  override fun createUser(
    createUserRequest: CreateUserRequest,
    userType: Long,
    host: String
  ) {
    try {
      if (!isValidEmail(createUserRequest.email)) {
        throw BadRequestException("สร้างบัญชีไม่สำเร็จ รูปแบบอีเมลไม่ถูกต้อง")
      }
      val user = userRepository.findByEmail(createUserRequest.email)
      if (user.isPresent) {
        throw BadRequestException("สร้างบัญชีไม่สำเร็จ อีเมลนี้ถูกใช้แล้ว")
      }
      if (createUserRequest.password.isEmpty()) {
        throw BadRequestException("สร้างบัญชีไม่สำเร็จ ไม่ได้กำหนดรหัสผ่าน")
      }

      val userData = userRepository.save(
        User(
          email = createUserRequest.email,
          password = bCryptPasswordEncoder.encode(createUserRequest.password),
          userTypeId = userType,
          provider = AuthProvider.local.toString(),
          emailVerified = false,
          phoneVerified = false,
          firstName = createUserRequest.firstname,
          lastName = createUserRequest.lastname,
          fullName = createUserRequest.firstname + " " + createUserRequest.lastname,
          username = createUserRequest.username,
        )
      )

      val authentication = authenticationManager.authenticate(
        UsernamePasswordAuthenticationToken(
          createUserRequest.email,
          createUserRequest.password
        )
      )
      val userPrincipal = authentication.principal as UserPrincipal
      userPrincipal.setUserType(UserRoleEnum.ROLE_USER.name)

      val token = jwtTokenProvider.generateToken(userPrincipal)
      if (token != null) {
        tokenService.cacheToken(
          TokenDto(
            userId = userData.id.toString(),
            token = token,
            multipleLogin = true //login ซ้อนพร้อมกัน
          )
        )

        val userTypeData = getUserDetailById(userData.id)
        if (userTypeData.isUser) {
          this.verifyEmail(userPrincipal, host)
        }
      }
    } catch (e: Exception) {
      throw Exception(e.message)
    }
  }

  override fun findUserById(userId: Long): User {
    return userRepository.findById(userId).orElseThrow { NotFoundException("ไม่มีบัญชีผู้ใช้งาน") }
  }

  override fun getUserById(userId: Long): UserMeDto {
    return userRepository.getUserById(userId)
  }

  override fun getUserDetailById(userId: Long): UserDetailDto {
    return userRepository.getUserDetailById(userId)
  }

  override fun updateUserMe(userId: Long, updateProfileRequest: String, file: MultipartFile?) {
    val user =
      userRepository.findById(userId).orElseThrow { NotFoundException("ไม่มีบัญชีผู้ใช้งาน") }
    val request = gson.fromJson(
      updateProfileRequest.decodeThailandCharacters(),
      UpdateProfileRequest::class.java
    )

    user.firstName = request.firstname
    user.lastName = request.lastname
    user.fullName = request.firstname + " " + request.lastname
    user.phoneNumber = request.phoneNumber

    val oldImage = user.imageUrl
    if (file != null) {
      if (oldImage != null && user.provider == "local") {
        s3Service.deleteFile(oldImage)
      }

      val createFile = s3Service.uploadFile(file)
      user.imageUrl = createFile.url
    }

    userRepository.save(user)
  }

  override fun updatePassword(id: Int, updatePasswordDto: UpdatePasswordDto) {
    val user =
      userRepository.findById(id.toLong()).orElseThrow { NotFoundException("ไม่มีบัญชีผู้ใช้งาน") }
    if (bCryptPasswordEncoder.matches(updatePasswordDto.oldPassword, user.password)) {
      user.password = bCryptPasswordEncoder.encode(updatePasswordDto.newPassword)
      userRepository.save(user)
      tokenService.revokeToken(id.toLong())
    } else {
      throw BadRequestException("รหัสผ่านเดิมไม่ถูกต้อง")
    }
  }

  override fun emailForgotPassword(emailForgotPasswordDto: EmailForgotPasswordDto) {
    val user = userRepository.findByEmail(emailForgotPasswordDto.email)
      .orElseThrow { NotFoundException("ไม่พบอีเมล") }
    val token = jwtTokenProvider.generateTokenForgotEmail(user)
    if (token != null) {
      tokenService.cacheForgotToken(
        ForgotTokenDto(
          userId = user.id.toString(),
          token = token,
        )
      )
    }

    val message = emailSender.createMimeMessage()
    val helper = MimeMessageHelper(
      message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
      StandardCharsets.UTF_8.name()
    )
    val context = Context()
    context.setVariable("name", user.username)
    context.setVariable("link", "$appUrl/reset-password?token=$token")
    val html = templateEngine.process("forgot_template.html", context)
    helper.setSubject("Digiboxs:Please reset your password.")
    helper.setTo(user.email!!)
    helper.setText(html, true)
    emailSender.send(message)
  }

  override fun forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    val userId = jwtTokenProvider.getUserId(forgotPasswordDto.token)
    val user = userRepository.findById(userId.toLong())
      .orElseThrow { NotFoundException("ไม่มีบัญชีผู้ใช้งาน") }
    user.password = bCryptPasswordEncoder.encode(forgotPasswordDto.newPassword)
    userRepository.save(user)

    tokenService.revokeForgotToken(userId)
  }

  override fun verifyEmail(userPrincipal: UserPrincipal, host: String) {
    val user = userRepository.findById(userPrincipal.getUserId())
      .orElseThrow { NotFoundException("ไม่มีบัญชีผู้ใช้งาน") }
    val token = jwtTokenProvider.generateToken(userPrincipal)
    val message = emailSender.createMimeMessage()
    val helper = MimeMessageHelper(
      message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
      StandardCharsets.UTF_8.name()
    )
    val context = Context()
    context.setVariable("link", "https://$host/api/user/verifybyemail/$token")
    val html = templateEngine.process("register_template.html", context)
    helper.setSubject("Digiboxs: กรุณายืนยันอีเมลของคุณ")
    helper.setTo(user.email!!)
    helper.setText(html, true)
    emailSender.send(message)
  }

  override fun verifyByEmail(token: String) {
    val userId = jwtTokenProvider.getUserId(token)
    val user = userRepository.findById(userId.toLong())
      .orElseThrow { NotFoundException("ไม่มีบัญชีผู้ใช้งาน") }
    user.emailVerified = true
    userRepository.save(user)
  }

  override fun createAddress(userId: Int, createUserAddressRequest: CreateUserAddressRequest) {
    val address = Address(
      userId = userId,
      name = createUserAddressRequest.name,
      phoneNumber = createUserAddressRequest.phoneNumber,
      address = createUserAddressRequest.address,
      zipCode = createUserAddressRequest.zipCode,
      province = createUserAddressRequest.province,
      district = createUserAddressRequest.district,
      subDistrict = createUserAddressRequest.subDistrict,
      isTax = createUserAddressRequest.isTax,
      taxPayerType = createUserAddressRequest.taxPayerType,
      taxId = createUserAddressRequest.taxId,
      email = createUserAddressRequest.email,
      isDefault = createUserAddressRequest.isDefault
    )

    if (createUserAddressRequest.isDefault) {
      addressRepository.resetAddressDefaultByUserId(userId, address.isTax)
    } else {
      val findDefault =
        addressRepository.findDefaultAddressByUserId(userId, createUserAddressRequest.isTax)
      if (findDefault == null) {
        address.isDefault = true
      }
    }

    addressRepository.save(address)
  }

  override fun findAllAddressByUserId(userId: Int, isTax: Boolean): List<Address> {
    return addressRepository.findAllByUserId(userId, isTax)
  }

  override fun updateAddress(
    userId: Int,
    id: Int,
    updateUserAddressRequest: UpdateUserAddressRequest
  ) {
    val address = addressRepository.findById(id)
      .orElseThrow { throw NotFoundException("ไม่มีข้อมูลการจัดส่ง หรือ ข้อมูลใบกำกับภาษี") }
    address.name = updateUserAddressRequest.name
    address.phoneNumber = updateUserAddressRequest.phoneNumber
    address.address = updateUserAddressRequest.address
    address.zipCode = updateUserAddressRequest.zipCode
    address.province = updateUserAddressRequest.province
    address.district = updateUserAddressRequest.district
    address.subDistrict = updateUserAddressRequest.subDistrict
    address.isTax = updateUserAddressRequest.isTax
    address.taxPayerType = updateUserAddressRequest.taxPayerType
    address.taxId = updateUserAddressRequest.taxId
    address.email = updateUserAddressRequest.email
    address.modifiedDate = Date()

    if (updateUserAddressRequest.isDefault) {
      addressRepository.resetAddressDefaultByUserId(userId, updateUserAddressRequest.isTax)
      address.isDefault = updateUserAddressRequest.isDefault
    }

    addressRepository.save(address)
  }

  override fun deleteAddress(userId: Int, id: Int) {
    val address = addressRepository.findById(id)
      .orElseThrow { throw NoContentException("ไม่มีข้อมูลการจัดส่ง") }

    if (address.isDefault) {
      val findAllAddress = addressRepository.findNotDefaultByUserId(userId, address.isTax)
      if (findAllAddress.isNotEmpty()) {
        val setDefaultAddress = addressRepository.findById(findAllAddress[0]!!.id)
          .orElseThrow { throw NotFoundException("ไม่มีข้อมูลการจัดส่ง") }
        setDefaultAddress.isDefault = true
        addressRepository.save(setDefaultAddress)
      }
    }

    addressRepository.deleteById(id)
  }

  override fun deleteAccount(userId: Long) {
    val user =
      userRepository.findById(userId).orElseThrow { throw NotFoundException("ไม่มีบัญชีผู้ใช้งาน") }
    user.isDeleted = true
    userRepository.save(user)
  }

  fun isValidEmail(str: String): Boolean {
    return Pattern.compile(
      "[a-zA-Z0-9\\+\\.\\_\\%\\-\\+]{1,256}" +
          "\\@" +
          "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
          "(" +
          "\\." +
          "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
          ")+"
    ).matcher(str).matches()
  }
}