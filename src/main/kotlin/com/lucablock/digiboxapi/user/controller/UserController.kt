package com.lucablock.digiboxapi.user.controller

import com.lucablock.digiboxapi.entity.UserRoleEnum
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NoContentException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.token.JWTTokenProvider
import com.lucablock.digiboxapi.token.dto.TokenDto
import com.lucablock.digiboxapi.token.service.TokenService
import com.lucablock.digiboxapi.user.dto.EmailForgotPasswordDto
import com.lucablock.digiboxapi.user.dto.ForgotPasswordDto
import com.lucablock.digiboxapi.user.dto.UpdatePasswordDto
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.lucablock.digiboxapi.user.repository.UserRepository
import com.lucablock.digiboxapi.user.request.CreateUserAddressRequest
import com.lucablock.digiboxapi.user.request.CreateUserRequest
import com.lucablock.digiboxapi.user.request.LoginRequest
import com.lucablock.digiboxapi.user.request.UpdateUserAddressRequest
import com.lucablock.digiboxapi.user.service.UserService
import io.swagger.v3.oas.annotations.Parameter
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping("/api/user")
class UserController {
  private val logger: Logger = LoggerFactory.getLogger(UserController::class.java)

  @Autowired
  lateinit var userService: UserService

  @Autowired
  lateinit var authenticationManager: AuthenticationManager

  @Autowired
  lateinit var jwtTokenProvider: JWTTokenProvider

  @Autowired
  lateinit var tokenService: TokenService

  @Autowired
  lateinit var userRepository: UserRepository

  @Value("\${appUrl}")
  private var appUrl: String = ""

  @PostMapping("/login")
  fun login(
    @Valid @RequestBody loginRequest: LoginRequest,
    httpServletRequest: HttpServletRequest,
    httpServletResponse: HttpServletResponse,
  ): ResponseEntity<Any> {
    try {
      val authentication = authenticationManager.authenticate(
        UsernamePasswordAuthenticationToken(
          loginRequest.username,
          loginRequest.password
        )
      )
      if (authentication.isAuthenticated) {
        val userPrincipal = authentication.principal as UserPrincipal
        if (!userPrincipal.getEmailVerify()!!) {
          return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(
              HttpResponse(
                status = false,
                message = "กรุณายืนยันอีเมลของคุณ ก่อนเข้าสู่ระบบ!"
              )
            )
        }
        val token = jwtTokenProvider.generateToken(userPrincipal)
        token?.let {
          tokenService.cacheToken(
            TokenDto(
              userId = userPrincipal.getUserId().toString(),
              token = token,
              multipleLogin = true //login ซ้อนพร้อมกัน
            )
          )
          return ResponseEntity.ok().body(
            HttpResponse(
              true,
              "เข้าสู่ระบบสำเร็จ",
              token,
            )
          )
        }
      }
      return ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(
          HttpResponse(
            status = false,
            message = "ไม่สามารถเข้าสู่ระบบได้ อีเมลหรือรหัสผ่านไม่ถูกต้อง"
          )
        )
    } catch (e: BadCredentialsException) {
      logger.error("login error : ${e.message}")
      return ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถเข้าสู่ระบบได้ อีเมลหรือรหัสผ่านไม่ถูกต้อง"
          )
        )
    } catch (e: Exception) {
      logger.error("login error : ${e.message}")
      e.printStackTrace()
      return ResponseEntity.status(HttpStatus.FORBIDDEN)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถเข้าสู่ระบบได้ คุณไม่มีสิทธิ์การเข้าถึงข้อมูลในหน้านี้"
          )
        )
    }
  }

  @PostMapping("/logout")
  fun logout(@AuthenticationPrincipal userPrincipal: UserPrincipal): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ออกจากระบบสำเร็จ",
          tokenService.revokeToken(userPrincipal.getUserId())
        )
      )
    } catch (e: Exception) {
      logger.error("logout error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/me")
  fun getUser(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลผู้ใช้สำเร็จ",
          userService.getUserById(userPrincipal.getUserId())
        )
      )
    } catch (e: Exception) {
      logger.error("get user error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @PostMapping("/register")
  fun createUser(
    @RequestBody createUserRequest: CreateUserRequest,
    @RequestHeader(required = false) host: String,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างบัญชีสำเร็จ",
          userService.createUser(createUserRequest, UserRoleEnum.ROLE_USER.value, host)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create user error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "สร้างบัญชีไม่สำเร็จ"))
    } catch (e: Exception) {
      logger.error("create user error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/verify")
  fun verify(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestHeader(required = false) host: String,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สำเร็จ ส่งข้อมูลยืนยันตัวตน และลิงก์ไปยังอีเมลของคุณแล้ว",
          userService.verifyEmail(userPrincipal, host)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("verify email error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีบัญชีผู้ใช้งาน"))
    } catch (e: Exception) {
      logger.error("verify email error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาด ไม่สามารถส่งข้อมูลไปยังอีเมลของคุณ"))
    }
  }

  @GetMapping("/verifybyemail/{token}")
  fun verifyByEmail(
    @PathVariable("token") token: String,
    httpServletResponse: HttpServletResponse,
  ) {
    try {
      userService.verifyByEmail(token)
      httpServletResponse.setHeader("Location", "$appUrl/login")
      httpServletResponse.status = 302
    } catch (e: NotFoundException) {
      logger.error("verify email error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาด ไม่สามารถยืนยันตัวตนได้"))
    } catch (e: Exception) {
      logger.error("verify by email error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PutMapping("/password")
  fun updateUserPassword(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestBody updatePasswordDto: UpdatePasswordDto,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เปลี่ยนรหัสผ่านสำเร็จ ได้อัปเดตรหัสผ่านของคุณแล้ว",
          userService.updatePassword(userPrincipal.getUserId().toInt(), updatePasswordDto)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("verify email error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาด ขั้นตอนการเปลี่ยนรหัสผ่านไม่สำเร็จ"))
    } catch (e: BadRequestException) {
      logger.error("verify email error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาด รหัสผ่านเดิมไม่ถูกต้อง"))
    } catch (e: Exception) {
      logger.error("verify by email error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PostMapping("/sendEmailForgotPassword")
  fun sendEmailForgotPassword(
    @RequestBody emailForgotPasswordDto: EmailForgotPasswordDto,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สำเร็จ ส่งข้อมูลยืนยันตัวตน และลิงก์เปลี่ยนรหัสผ่านไปยังอีเมลของคุณแล้ว",
          userService.emailForgotPassword(emailForgotPasswordDto)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("send email forgot password error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาด ไม่สามารถส่งข้อมูลไปยังอีเมลของคุณ"))
    } catch (e: Exception) {
      logger.error("send email forgot password error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PutMapping("/forgotPassword")
  fun forgotPassword(
    @RequestBody forgotPasswordDto: ForgotPasswordDto,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เปลี่ยนรหัสผ่านสำเร็จ ได้อัปเดตรหัสผ่านของคุณแล้ว",
          userService.forgotPassword(forgotPasswordDto)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("forgot password error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "เกิดข้อผิดพลาด ไม่สามารถเปลี่ยนรหัสผ่านได้"))
    } catch (e: Exception) {
      logger.error("forgot password error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PostMapping("/address")
  fun createAddress(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestBody createUserAddressRequest: CreateUserAddressRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เพิ่มข้อมูลการจัดส่งสินค้า หรือ ข้อมูลใบกำกับภาษี สำเร็จ",
          userService.createAddress(userPrincipal.getUserId().toInt(), createUserAddressRequest)
        )
      )
    } catch (e: Exception) {
      logger.error("create address error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message
              ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @GetMapping("/address/{isTax}")
  fun findAllAddress(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @PathVariable isTax: Boolean,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลการจัดส่งสินค้า หรือ ข้อมูลใบกำกับภาษี สำเร็จ",
          userService.findAllAddressByUserId(userPrincipal.getUserId().toInt(), isTax)
        )
      )
    } catch (e: Exception) {
      logger.error("get address error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message
              ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @PutMapping(
    "/updateProfile",
    consumes = [MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE]
  )
  fun updateProfile(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestPart("updateUserRequest", required = true) updateUserRequest: String,
    @RequestPart("file", required = false) file: MultipartFile?
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "อัปโหลรูปโปรไฟล์ หรือบันทึกข้อมูลการแก้ไขโปรไฟล์สำเร็จ",
          userService.updateUserMe(userPrincipal.getUserId(), updateUserRequest, file)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update user error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(
          HttpResponse(
            false,
            e.message ?: "เกิดข้อผิดพลาด ไม่สามารถบันทึกข้อมูลการแก้ไขโปรไฟล์ได้"
          )
        )
    } catch (e: Exception) {
      logger.error("update user error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PutMapping("/address/{id}")
  fun updateAddress(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @PathVariable id: Int,
    @RequestBody updateUserAddressRequest: UpdateUserAddressRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลการแก้ไขการจัดส่งสินค้าสำเร็จ",
          userService.updateAddress(userPrincipal.getUserId().toInt(), id, updateUserAddressRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update address error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลการจัดส่ง"))
    } catch (e: Exception) {
      logger.error("update address error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @DeleteMapping("/address/{id}")
  fun deleteAddress(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @PathVariable id: Int
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลการจัดส่งสินค้าสำเร็จ",
          userService.deleteAddress(userPrincipal.getUserId().toInt(), id)
        )
      )
    } catch (e: NoContentException) {
      logger.error("delete address error : ${e.message}")
      return ResponseEntity.status(HttpStatus.NO_CONTENT)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลการจัดส่ง"))
    } catch (e: Exception) {
      logger.error("delete address error : ${e.message}")
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @DeleteMapping("/deleteAccount")
  fun deleteAccount(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบบัญชีของคุณแนื้อเนื้อหาทั้งหมดของคุณสำเร็จ",
          userService.deleteAccount(userPrincipal.getUserId())
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete account error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีบัญชีผู้ใช้งาน"))
    } catch (e: Exception) {
      logger.error("delete account error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }
}