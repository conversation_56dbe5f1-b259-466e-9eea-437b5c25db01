package com.lucablock.digiboxapi.user.request

data class CreateUserAddressRequest(
  val name: String,
  val phoneNumber: String,
  val address: String,
  val zipCode: String,
  val province: String,
  val district: String,
  val subDistrict: String,
  val isTax: Boolean,
  val taxPayerType: Int? = null,
  val taxId: String? = null,
  val email: String? = null,
  val isDefault: Boolean,
)

data class UpdateUserAddressRequest(
  val name: String,
  val phoneNumber: String,
  val address: String,
  val zipCode: String,
  val province: String,
  val district: String,
  val subDistrict: String,
  val isTax: Boolean,
  val taxPayerType: Int? = null,
  val taxId: String? = null,
  val email: String? = null,
  val isDefault: Boolean,
)


