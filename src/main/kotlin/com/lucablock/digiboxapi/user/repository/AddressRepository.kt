package com.lucablock.digiboxapi.user.repository

import com.lucablock.digiboxapi.entity.Address
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface AddressRepository: JpaRepository<Address, Int>{
  @Query("SELECT * from address where user_id = :userId and is_tax = :isTax", nativeQuery = true)
  fun findAllByUserId(userId: Int, isTax: Boolean): List<Address>

  @Query("SELECT * FROM  address where is_default = true and user_id = :userId and is_tax = :isTax", nativeQuery = true)
  fun findDefaultAddressByUserId(userId: Int, isTax: Boolean): Address?

  @Query("SELECT * FROM  address where is_default = false and user_id = :userId and is_tax = :isTax", nativeQuery = true)
  fun findNotDefaultByUserId(userId: Int, isTax: Boolean): List<Address?>

  @Modifying
  @Transactional
  @Query("UPDATE address SET is_default = false where user_id = :userId and is_tax = :isTax", nativeQuery = true)
  fun resetAddressDefaultByUserId(userId: Int, isTax: Boolean)
}