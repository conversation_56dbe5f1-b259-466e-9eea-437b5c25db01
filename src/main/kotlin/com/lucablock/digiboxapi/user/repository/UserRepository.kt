package com.lucablock.digiboxapi.user.repository

import com.lucablock.digiboxapi.entity.User
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface UserRepository : JpaRepository<User, Long>, UserRepositoryCustom {
  fun findByEmail(username: String): Optional<User>
  fun findByEmailContainsIgnoreCase(username: String): Optional<User>
  @Query("SELECT * from users where username = :username or email = :username and is_deleted = false", nativeQuery = true)
  fun findByUsernameOrEmail(username: String): Optional<User>
  fun existsByCustomerLevelId(customerLevelId: Long): Boolean
  fun findByCustomerLevelId(customerLevelId: Long): MutableList<User>
  fun existsByIdAndCustomerLevelId(id: Long, customerLevelId: Long): Boolean
}