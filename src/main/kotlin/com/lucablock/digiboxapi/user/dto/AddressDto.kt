package com.lucablock.digiboxapi.user.dto

data class CreateAddressDto(
  val name: String,
  val phoneNumber: String,
  val address: String,
  val zipCode: String,
  val province: String,
  val district: String,
  val subDistrict: String,
  val isTax: Boolean,
  val taxPayerType: Int? = null,
  val taxId: String? = null,
  val email: String? = null,
)

data class AddressDto(
  val id: Int,
  val name: String,
  val taxId: String? = null,
  val phoneNumber: String,
  val email: String? = null,
  val address: String,
  val province: String,
  val district: String,
  val subDistrict: String,
  val zipCode: String,
  val isTax: Boolean,
)
