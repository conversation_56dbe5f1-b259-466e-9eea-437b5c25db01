package com.lucablock.digiboxapi.user.dto

import com.querydsl.core.annotations.QueryProjection
import jakarta.persistence.Column
import java.io.Serializable

data class UserDetailDto
@QueryProjection
constructor(
  val id: Long,
  var name: String?,
  var email: String?,
  var isAdmin: Boolean,
  var isAnonymous: Boolean,
  var isUser: Boolean,
  var isSuperAdmin: Boolean,
  var emailVerified: Boolean
) : Serializable

data class UserMeDto
@QueryProjection
constructor(
  val id: Long,
  var name: String?,
  var email: String?,
  var phoneNumber: String?,
  var imageUrl: String?,
  var shippingAddress: List<ShippingAddressDto?>,
  var taxAddress: List<TaxAddressDto?>,
  var isAdmin: Boolean,
  var isAnonymous: Boolean,
  var isUser: Boolean,
  var isSuperAdmin: Boolean,
  var emailVerified: Boolean,
  var provider: String
) : Serializable

data class UpdateMeDto(
  var firstName: String,
  var lastName: String,
  var phoneNumber: String
)

data class UpdatePasswordDto(
  var oldPassword: String,
  var newPassword: String,
)
data class EmailForgotPasswordDto(
  var email: String,
)
data class ForgotPasswordDto(
  var token: String,
  var newPassword: String,
)

data class ShippingAddressDto
@QueryProjection
constructor(
  val id: Int,
  var name: String,
  var phoneNumber: String,
  var address: String,
  var zipCode: String,
  var province: String,
  var district: String,
  var subDistrict: String,
  var email: String,
  var isDefault: Boolean,
) : Serializable

data class TaxAddressDto
@QueryProjection
constructor(
  val id: Int,
  var name: String,
  var phoneNumber: String,
  var address: String,
  var zipCode: String,
  var province: String,
  var district: String,
  var subDistrict: String,
  var taxPayerType: Int,
  var taxId: String,
  var email: String,
  var isDefault: Boolean,
) : Serializable