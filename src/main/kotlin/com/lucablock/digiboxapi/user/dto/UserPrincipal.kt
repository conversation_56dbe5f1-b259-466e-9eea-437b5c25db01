package com.lucablock.digiboxapi.user.dto

import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.oauth2.core.oidc.OidcIdToken
import org.springframework.security.oauth2.core.oidc.OidcUserInfo
import org.springframework.security.oauth2.core.oidc.user.OidcUser
import org.springframework.security.oauth2.core.user.OAuth2User

class UserPrincipal : UserDetails, OAuth2User, OidcUser {
  private var userId: Long = 0
  private var username: String? = null
  private var password: String? = null
  private var companyId: Long? = 0
  private var userType: String? = null
  private var name: String? = null
  private var attributes: MutableMap<String, Any>? = null
  private var userTypeId: Long? = null
  private var path: String? = null
  private var email: String? = null
  private var claims: MutableMap<String, Any>? = null
  private var userInfo: OidcUserInfo? = null
  private var idToken: OidcIdToken? = null
  private var emailVerify: Boolean? = false

  override fun getName(): String? {
    return this.name
  }

  override fun getAttributes(): MutableMap<String, Any>? {
    return this.attributes
  }

  fun setAttributes(attributes: MutableMap<String, Any>?) {
    this.attributes = attributes
  }

  override fun getAuthorities(): Collection<GrantedAuthority> {
    return arrayListOf()
  }

  override fun getClaims(): MutableMap<String, Any> {
    return this.claims!!
  }

  fun getUserId(): Long {
    return userId
  }

  fun setUserId(userId: Long?) {
    if (userId != null) {
      this.userId = userId
    }
  }

  fun getCompanyId(): Long? {
    return this.companyId
  }

  fun setCompanyId(companyId: Long?) {
    this.companyId = companyId
  }

  fun getUserType(): String {
    return userType!!
  }

  fun setUserType(userType: String?) {
    this.userType = userType
  }

  fun getUserTypeId(): Long {
    return userTypeId!!
  }

  fun setUserTypeId(userTypeId: Long?) {
    this.userTypeId = userTypeId
  }

  override fun getPassword(): String {
    return password!!
  }

  override fun getUsername(): String? {
    return username
  }

  override fun isAccountNonExpired(): Boolean {
    return true
  }

  override fun isAccountNonLocked(): Boolean {
    return true
  }

  override fun isCredentialsNonExpired(): Boolean {
    return true
  }

  override fun isEnabled(): Boolean {
    return true
  }

  fun setUsername(username: String?) {
    this.username = username
  }

  fun setPassword(password: String?) {
    this.password = password
  }

  fun setEmail(email: String?) {
    this.email = email
  }

  override fun getEmail(): String? {
    return this.email
  }

  fun setPath(path: String?) {
    this.path = path
  }

  fun getPath(): String? {
    return this.path
  }

  override fun getUserInfo(): OidcUserInfo? {
    return this.userInfo
  }

  override fun getIdToken(): OidcIdToken? {
    return this.idToken
  }

  fun getEmailVerify(): Boolean? {
    return this.emailVerify
  }

  fun setEmailVerify(status: Boolean) {
    this.emailVerify = status
  }
}