package com.lucablock.digiboxapi.productCategory.repository

import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface ProductCategoryRepositoryCustom {
  fun findPageProductCategories(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?,
  ): Page<ProductCategoryDto>
}