package com.lucablock.digiboxapi.productCategory.repository

import com.lucablock.digiboxapi.entity.ProductCategory
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ProductCategoryRepository : JpaRepository<ProductCategory, Long> , ProductCategoryRepositoryCustom
{
  fun existsByNameIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  fun existsByNameIgnoreCaseAndIdNot(name: String, id: Long): Boolean
  fun findAllByOrderByIdAsc(pageable: Pageable): Page<ProductCategoryDto>
  fun existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String, id: Long): Boolean
}