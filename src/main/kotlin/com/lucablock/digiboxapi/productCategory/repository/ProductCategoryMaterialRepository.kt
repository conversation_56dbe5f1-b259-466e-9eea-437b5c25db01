package com.lucablock.digiboxapi.productCategory.repository

import com.lucablock.digiboxapi.entity.ProductCategoryMaterial
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ProductCategoryMaterialRepository : JpaRepository<ProductCategoryMaterial, Long> {
  @Query(
    "SELECT COUNT(*) > 0 FROM product_category_material WHERE product_category_id = :productCategoryId",
    nativeQuery = true
  )
  fun existsByProductCategoryId(productCategoryId: Long): Boolean

  @Modifying
  @Transactional
  @Query(
    "DELETE from product_category_material where material_id = :materialId",
    nativeQuery = true
  )
  fun deleteAllByMaterialId(materialId: Long)
}