package com.lucablock.digiboxapi.productCategory.repository

import com.lucablock.digiboxapi.entity.ProductCategorySpecialTechnic
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ProductCategorySpecialTechnicRepository :
  JpaRepository<ProductCategorySpecialTechnic, Long> {
  @Query(
    "SELECT COUNT(*) > 0 FROM product_category_special_technic WHERE product_category_id = :productCategoryId",
    nativeQuery = true
  )
  fun existsByProductCategoryId(productCategoryId: Long): Boolean

  @Modifying
  @Transactional
  @Query(
    "DELETE FROM product_category_special_technic WHERE special_technic_id = :specialTechnicId",
    nativeQuery = true
  )
  fun deleteAllBySpecialTechnicId(specialTechnicId: Long)
}