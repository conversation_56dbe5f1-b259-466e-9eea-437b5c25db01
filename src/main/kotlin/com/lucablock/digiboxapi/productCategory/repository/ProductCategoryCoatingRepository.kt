package com.lucablock.digiboxapi.productCategory.repository

import com.lucablock.digiboxapi.entity.ProductCategoryCoating
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ProductCategoryCoatingRepository : JpaRepository<ProductCategoryCoating, Long> {
  @Query(
    "SELECT COUNT(*) > 0 FROM product_category_coating WHERE product_category_id = :productCategoryId",
    nativeQuery = true
  )
  fun existsByProductCategoryId(productCategoryId: Long): Bo<PERSON>an

  @Modifying
  @Transactional
  @Query("DELETE FROM product_category_coating WHERE coating_id = :coatingId", nativeQuery = true)
  fun deleteAllByCoatingId(coatingId: Long)

}