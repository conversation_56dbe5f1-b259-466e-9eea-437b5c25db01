package com.lucablock.digiboxapi.productCategory.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class ProductCategoryDto
@QueryProjection
constructor(
  val id: Long,
  val name: String,
  val imageUrl: String,
  val isActive: Boolean = true,
  val isDeleted: Boolean = false,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
)
