package com.lucablock.digiboxapi.productCategory.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.ProductCategory
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.product.repository.ProductRepository
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryCoatingRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryMaterialRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategorySpecialTechnicRepository
import com.lucablock.digiboxapi.productCategory.request.ProductCategoryRequest
import com.lucablock.digiboxapi.productCategory.service.ProductCategoryService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service


@Service
class ProductCategoryServiceImpl @Autowired internal constructor(
  private val productCategoryRepository: ProductCategoryRepository,
  private val productRepository: ProductRepository,
  private val productCategoryMaterialRepository: ProductCategoryMaterialRepository,
  private val productCategoryCoatingRepository: ProductCategoryCoatingRepository,
  private val productCategorySpecialTechnicRepository: ProductCategorySpecialTechnicRepository,
  private val s3Service: S3Service
) : ProductCategoryService {

  @Transactional
  override fun createProductCategory(productCategoryRequest: ProductCategoryRequest): ProductCategoryDto {
    productCategoryRepository.existsByNameIgnoreCaseAndIsDeletedFalse(productCategoryRequest.name)
      .let { if (it) throw BadRequestException("ชื่อหมวดหมู่ซ้ำ") }

    val productCategory = ProductCategory(
        name = productCategoryRequest.name,
        imageUrl = productCategoryRequest.imageUrl,
      )

    val urlFile = s3Service.moveFile(productCategoryRequest.imageUrl).url
    productCategory.imageUrl = urlFile
    val updateProductCategory = productCategoryRepository.save(productCategory)

    return updateProductCategory.toProductCategoryDto()
  }

  override fun findPageProductCategories(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<ProductCategoryDto> {
    return productCategoryRepository.findPageProductCategories(
      pageable, ascending, search, isActive
    )
  }

  override fun findProductCategoryById(id: Long): ProductCategoryDto {
    return productCategoryRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบหมวดหมู่") }.toProductCategoryDto()
  }

  override fun updateProductCategory(
    id: Long,
    productCategoryRequest: ProductCategoryRequest
  ): ProductCategoryDto {
    val productCategory = productCategoryRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบหมวดหมู่") }

    productCategoryRepository.existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(productCategoryRequest.name, id)
      .let {
        if (it) {
          throw BadRequestException("ชื่อหมวดหมู่ซ้ำ")
        }
      }

    productCategory.name = productCategoryRequest.name
    productCategory.isActive = productCategoryRequest.isActive

    if (productCategoryRequest.imageUrl != productCategory.imageUrl) {
      s3Service.deleteFile(productCategory.imageUrl)
      val newFileUrl = s3Service.moveFile(productCategoryRequest.imageUrl).url
      productCategory.imageUrl = newFileUrl
    }

    val savedProductCategory = productCategoryRepository.save(productCategory)

    return savedProductCategory.toProductCategoryDto()
  }

  @Transactional
  override fun deleteProductCategory(id: Long) {
    val productCategory = productCategoryRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่พบหมวดหมู่") }

//    productRepository.existsByProductCategoryId(id)
//      .let { if (it) throw BadRequestException("ไม่สามารถลบหมวดหมู่ผลิตภัณฑ์ได้ มีการใช้งานกับผลิตภัณฑ์") }
//
//    productCategoryMaterialRepository.existsByProductCategoryId(id)
//      .let { if (it) throw BadRequestException("ไม่สามารถลบหมวดหมู่ผลิตภัณฑ์ได้ มีการใช้งานกับวัสดุ") }
//
//    productCategoryCoatingRepository.existsByProductCategoryId(id)
//      .let { if (it) throw BadRequestException("ไม่สามารถลบหมวดหมู่ผลิตภัณฑ์ได้ มีการใช้งานกับการเคลือบ") }
//
//    productCategorySpecialTechnicRepository.existsByProductCategoryId(id)
//      .let { if (it) throw BadRequestException("ไม่สามารถลบหมวดหมู่ผลิตภัณฑ์ได้ มีการใช้งานกับเทคนิคพิเศษ") }

//    s3Service.deleteFile(productCategory.imageUrl)
    productCategory.isDeleted = true
    productCategoryRepository.save(productCategory)
  }
}