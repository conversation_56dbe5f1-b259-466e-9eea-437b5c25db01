package com.lucablock.digiboxapi.productCategory.service

import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import com.lucablock.digiboxapi.productCategory.request.ProductCategoryRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface ProductCategoryService {
  fun createProductCategory(productCategoryRequest: ProductCategoryRequest): ProductCategoryDto
  fun findPageProductCategories(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?
  ): Page<ProductCategoryDto>

  fun findProductCategoryById(id: Long): ProductCategoryDto
  fun updateProductCategory(
    id: Long,
    productCategoryRequest: ProductCategoryRequest
  ): ProductCategoryDto

  fun deleteProductCategory(id: Long)
}