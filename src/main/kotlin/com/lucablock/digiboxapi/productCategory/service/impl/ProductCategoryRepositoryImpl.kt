package com.lucablock.digiboxapi.productCategory.service.impl

import com.lucablock.digiboxapi.entity.QProductCategory
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProductCategoryRepositoryImpl : ProductCategoryRepositoryCustom{
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }
  private val qProductCategory = QProductCategory.productCategory

  override fun findPageProductCategories(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<ProductCategoryDto> {
    var criteria = qProductCategory.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qProductCategory.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qProductCategory.name.containsIgnoreCase(search))
    }

    val sort = if (ascending) {
      qProductCategory.id.desc()
    } else {
      qProductCategory.id.asc()
    }

    val query = queryFactory
      .select(qProductCategory)
      .from(qProductCategory)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toProductCategoryDto()
      }

    val total = queryFactory
      .select(qProductCategory)
      .from(qProductCategory)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }

}