package com.lucablock.digiboxapi.productCategory.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.productCategory.request.ProductCategoryRequest
import com.lucablock.digiboxapi.productCategory.service.ProductCategoryService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class ProductCategoryController @Autowired internal constructor(
  private val productCategoryService: ProductCategoryService
) {
  private val logger: Logger = LoggerFactory.getLogger(ProductCategoryController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/product-category")
  fun createProductCategory(
    @Valid @RequestBody productCategoryRequest: ProductCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างข้อมูลหมวดหมู่สำเร็จ",
          data = productCategoryService.createProductCategory(productCategoryRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ชื่อหมวดหมู่ซ้ำ"))
    } catch (e: Exception) {
      logger.error("create product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลหมวดหมู่ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-category/page")
  fun findAllProductCategory(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = productCategoryService.findPageProductCategories(
            pageable,
            ascending,
            search,
            isActive,
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-category/{id}")
  fun findProductCategoryById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = productCategoryService.findProductCategoryById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find product category by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบหมวดหมู่"))
    } catch (e: Exception) {
      logger.error("find product category by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/product-category/{id}")
  fun updateProductCategory(
    @PathVariable id: Long,
    @Valid @RequestBody productCategoryRequest: ProductCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "บันทึกข้อมูลหมวดหมู่สินค้าสำเร็จ",
          data = productCategoryService.updateProductCategory(id, productCategoryRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบหมวดหมู่"))
    } catch (e: BadRequestException) {
      logger.error("update product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ชื่อหมวดหมู่ซ้ำ"))
    } catch (e: Exception) {
      logger.error("update product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลหมวดหมู่ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/product-category/{id}")
  fun deleteProductCategory(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบข้อมูลสำเร็จ",
          data = productCategoryService.deleteProductCategory(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบหมวดหมู่"))
    } catch (e: BadRequestException) {
      logger.error("delete product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบหมวดหมู่ได้ มีการใช้งานอยู่"))
    } catch (e: Exception) {
      logger.error("delete product category error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}