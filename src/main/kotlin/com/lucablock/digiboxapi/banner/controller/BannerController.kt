package com.lucablock.digiboxapi.banner.controller

import com.lucablock.digiboxapi.banner.request.CreateBannerRequest
import com.lucablock.digiboxapi.banner.request.UpdateBannerRequest
import com.lucablock.digiboxapi.banner.service.BannerService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController
@RequestMapping("/api")
class BannerController @Autowired constructor(
  private val bannerService: BannerService
) {
  private val logger = LoggerFactory.getLogger(BannerController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/banner")
  fun createBanner(
    @Valid @RequestBody createBannerRequest: UpdateBannerRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "สร้างแบนเนอร์สำเร็จ",
          data = bannerService.createBanner(createBannerRequest)
        )
      )
    } catch (e: Exception) {
      logger.error("Error creating banner: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ไม่สามารถสร้างแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/banner/{id}")
  fun updateBanner(
    @PathVariable id: Long,
    @Valid @RequestBody updateBannerRequest: UpdateBannerRequest,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "อัปเดตแบนเนอร์สำเร็จ",
          data = bannerService.updateBanner(id, updateBannerRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("Error updating banner: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ไม่สามารถอัปเดตแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    } catch (e: Exception) {
      logger.error("Error updating banner: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ไม่สามารถอัปเดตแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/banner/{id}")
  fun deleteBanner(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ลบแบนเนอร์สำเร็จ",
          data = bannerService.deleteBanner(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Error deleting banner: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบข้อมูลแบนเนอร์ที่ต้องการลบ")
      )
    } catch (e: Exception) {
      logger.error("Error deleting banner: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ไม่สามารถลบแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @GetMapping("/admin/banner/page")
  fun getPaginationBanner(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
  ): ResponseEntity<Any> {
    val pageable: Pageable = PageRequest.of(page, size)

    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลแบนเนอร์สำเร็จ",
          data = bannerService.getPaginationBanner(pageable, ascending)
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching banner list: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ไม่สามารถดึงข้อมูลแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/banner/status/{id}")
  fun updateStatusBanner(
    @PathVariable id: Long,
    @RequestParam status: Boolean,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "อัปเดตสถานะแบนเนอร์สำเร็จ",
          data = bannerService.updateStatusBanner(id, status)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("Error updating banner status: ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ไม่สามารถอัปเดตสถานะแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    } catch (e: NotFoundException) {
      logger.error("Error updating banner status: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบข้อมูลแบนเนอร์ที่ต้องการอัปเดตสถานะ")
      )
    } catch (e: Exception) {
      logger.error("Error updating banner status: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ไม่สามารถอัปเดตสถานะแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    }
  }

  @GetMapping("/web/banner/list")
  fun getBannerListByDateTime(
    @RequestParam("startDateTime") startDateTime: Long?,
    @RequestParam("endDateTime") endDateTime: Long?,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
  ): ResponseEntity<Any> {
    return try {
      val start = startDateTime?.let { Date(it) }
      val end = endDateTime?.let { Date(it) }

      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลแบนเนอร์ตามช่วงเวลา",
          data = bannerService.getBannerListByDateTime(start, end, ascending)
        )
      )
    } catch (e: Exception) {
      logger.error("Error fetching banner list by date: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ไม่สามารถดึงข้อมูลแบนเนอร์ตามช่วงเวลา กรุณาลองอีกครั้ง")
      )
    }
  }

  @GetMapping("/web/banner/{id}")
  fun getBannerById(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลแบนเนอร์สำเร็จ",
          data = bannerService.getBannerById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Error fetching banner by ID: ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบข้อมูลแบนเนอร์ที่ต้องการดึง")
      )
    } catch (e: Exception) {
      logger.error("Error fetching banner by ID: ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ไม่สามารถดึงข้อมูลแบนเนอร์ กรุณาลองอีกครั้ง")
      )
    }
  }
}