package com.lucablock.digiboxapi.banner.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.banner.dto.BannerDto
import com.lucablock.digiboxapi.banner.repository.BannerRepository
import com.lucablock.digiboxapi.banner.request.CreateBannerRequest
import com.lucablock.digiboxapi.banner.request.UpdateBannerRequest
import com.lucablock.digiboxapi.banner.service.BannerService
import com.lucablock.digiboxapi.entity.Banner
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.util.*

@Service
class BannerServiceImpl @Autowired constructor(
  private val bannerRepository: BannerRepository,
  private val s3Service: S3Service
) : BannerService {

  override fun createBanner(
    request: UpdateBannerRequest,
  ): BannerDto {

    val newBanner = Banner(
      imageUrl = request.imageUrl,
      description = request.description,
      startDateTime = request.startDateTime,
      endDateTime = request.endDateTime,
    )

    val newFileUrl = s3Service.moveFile(request.imageUrl).url
    newBanner.imageUrl = newFileUrl
    val saveBanner = bannerRepository.save(newBanner)

    return saveBanner.bannerToDto()
  }

  @Transactional
  override fun updateBanner(
    id: Long,
    request: UpdateBannerRequest,
  ): BannerDto {
    val banner = bannerRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลแบนเนอร์")
    }
    val oldImg = banner.imageUrl

    banner.description = request.description
    banner.startDateTime = request.startDateTime
    banner.endDateTime = request.endDateTime
    val updatedBanner = bannerRepository.save(banner)

    if (request.imageUrl != oldImg) {
      s3Service.deleteFile(oldImg)

      val newFileUrl = s3Service.moveFile(request.imageUrl).url
      updatedBanner.imageUrl = newFileUrl
    }
    val updateFileBanner = bannerRepository.save(updatedBanner)

    return updateFileBanner.bannerToDto()
  }

  @Transactional
  override fun deleteBanner(id: Long): Boolean {
    val banner = bannerRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลแบนเนอร์")
    }

    s3Service.deleteFile(banner.imageUrl)

    bannerRepository.delete(banner)
    return true
  }

  override fun getPaginationBanner(pageable: Pageable, ascending: Boolean): Page<BannerDto> {
    return bannerRepository.getPaginationBanner(pageable, ascending)
  }

  override fun updateStatusBanner(
    id: Long,
    status: Boolean
  ): BannerDto {
    val banner = bannerRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลแบนเนอร์")
    }

    if (status) {
      val statusCount = bannerRepository.countByStatusTrue()
      if (statusCount >= 5) {
        throw BadRequestException("ไม่สามารถเปิดแบนเนอร์ได้เกิน 5 แบนเนอร์")
      }
    }

    banner.status = status
    val updatedBanner = bannerRepository.save(banner)

    return updatedBanner.bannerToDto()
  }

  override fun getBannerListByDateTime(
    startDateTime: Date?,
    endDateTime: Date?,
    ascending: Boolean
  ): List<BannerDto> {
    return bannerRepository.getListByDateTime(startDateTime, endDateTime, ascending)
  }

  override fun getBannerById(id: Long): BannerDto {
    val banner = bannerRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลแบนเนอร์")
    }
    return banner.bannerToDto()
  }
}