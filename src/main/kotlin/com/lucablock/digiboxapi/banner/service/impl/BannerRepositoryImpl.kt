package com.lucablock.digiboxapi.banner.service.impl

import com.lucablock.digiboxapi.banner.dto.BannerDto
import com.lucablock.digiboxapi.banner.repository.BannerRepositoryCustom
import com.lucablock.digiboxapi.entity.QBanner
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.util.Date

@Service
class BannerRepositoryImpl: BannerRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qBanner = QBanner.banner

  override fun getPaginationBanner(pageable: Pageable, ascending: Boolean): Page<BannerDto> {
    val criteria = qBanner.isNotNull

    val orderBy = if (ascending) {
      qBanner.id.asc()
    } else {
      qBanner.id.desc()
    }

    val query = queryFactory
      .select(qBanner)
      .from(qBanner)
      .where(criteria)
      .orderBy(orderBy)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())

    val total = queryFactory
      .select(qBanner)
      .from(qBanner)
      .where(criteria)
      .fetch()

    val banners = query.fetch().map { it.bannerToDto() }

    return PageImpl(banners, pageable, total.size.toLong())
  }

  override fun getListByDateTime(
    startDateTime: Date?,
    endDateTime: Date?,
    ascending: Boolean
  ): List<BannerDto> {
    var criteria = qBanner.isNotNull

    if (startDateTime != null) {
      criteria = criteria.and(qBanner.startDateTime.goe(startDateTime))
    }

    if (endDateTime != null) {
      criteria = criteria.and(qBanner.endDateTime.loe(endDateTime))
    }

    val orderBy = if (ascending) {
      qBanner.id.asc()
    } else {
      qBanner.id.desc()
    }

    val query = queryFactory
      .select(qBanner)
      .from(qBanner)
      .where(criteria)
      .orderBy(orderBy)
    return query.fetch().map { it.bannerToDto() }
  }
}