package com.lucablock.digiboxapi.banner.service

import com.lucablock.digiboxapi.banner.dto.BannerDto
import com.lucablock.digiboxapi.banner.request.CreateBannerRequest
import com.lucablock.digiboxapi.banner.request.UpdateBannerRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.util.Date

interface BannerService {
  fun createBanner(request: UpdateBannerRequest): BannerDto
  fun updateBanner(
    id: Long,
    request: UpdateBannerRequest,
  ): BannerDto
  fun deleteBanner(id: Long): Boolean
  fun getPaginationBanner(pageable: Pageable, ascending: Boolean): Page<BannerDto>
  fun updateStatusBanner(
    id: Long,
    status: Boolean,
  ): BannerDto
  fun getBannerListByDateTime(
    startDateTime: Date?,
    endDateTime: Date?,
    ascending: Boolean
  ): List<BannerDto>
  fun getBannerById(id: Long): BannerDto
}