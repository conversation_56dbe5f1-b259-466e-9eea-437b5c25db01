package com.lucablock.digiboxapi.banner.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.util.Date

data class BannerDto
@QueryProjection
constructor(
  val id: Long,
  val imageUrl: String,
  val description: String? = null,
  val startDateTime: Date,
  val endDateTime: Date,
  val status: <PERSON><PERSON><PERSON>,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val modifiedDate: Date
)
