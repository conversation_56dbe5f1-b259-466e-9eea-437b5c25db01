package com.lucablock.digiboxapi.banner.repository

import com.lucablock.digiboxapi.banner.dto.BannerDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.util.Date

interface BannerRepositoryCustom {
  fun getPaginationBanner(pageable: Pageable, ascending: Boolean): Page<BannerDto>
  fun getListByDateTime(
    startDateTime: Date?,
    endDateTime: Date?,
    ascending: Boolean
  ): List<BannerDto>
}