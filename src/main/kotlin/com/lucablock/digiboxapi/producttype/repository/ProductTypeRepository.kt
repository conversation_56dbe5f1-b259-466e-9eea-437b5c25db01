package com.lucablock.digiboxapi.producttype.repository

import com.lucablock.digiboxapi.entity.ProductType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ProductTypeRepository : JpaRepository<ProductType, Long> , ProductTypeRepositoryCustom{
  fun existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String,id:Long): Boolean
  fun existsByNameIgnoreCaseAndIsDeletedFalse(name: String): <PERSON><PERSON><PERSON>
}