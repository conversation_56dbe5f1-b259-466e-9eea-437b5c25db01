package com.lucablock.digiboxapi.producttype.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank

class ProductTypeRequest {
  @NotBlank(message = "กรุณากรอกชื่อประเภท")
  val name: String = ""
  @NotBlank(message = "กรุณาระบุ URL รูปภาพ")
  val imageUrl: String = ""
  @JsonProperty("isActive")
  val isActive : Boolean = true
  @JsonProperty("isDeleted")
  val isDeleted : Boolean = false
}