package com.lucablock.digiboxapi.producttype.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.producttag.request.ProductTagRequest
import com.lucablock.digiboxapi.producttype.request.ProductTypeRequest
import com.lucablock.digiboxapi.producttype.service.ProductTypeService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class ProductTypeController @Autowired internal constructor(
  private val productTypeService: ProductTypeService
) {
  private val logger: Logger = LoggerFactory.getLogger(ProductTypeController::class.java)

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/product-type")
  fun createProductType(
    @Valid @RequestBody productTypeRequest: ProductTypeRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "สร้างข้อมูลประเภทสินค้าสำเร็จ",
          data = productTypeService.createProductType(productTypeRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลข้อมูลประเภทสินค้าซ้ำ"))
    } catch (e: Exception) {
      logger.error("create product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลประเภทสินค้า กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/product-type/{id}")
  fun updateProductType(
    @PathVariable id: Long,
    @Valid @RequestBody productTypeRequest: ProductTypeRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "บันทึกข้อมูลประเภทสินค้าสำเร็จ",
          data = productTypeService.updateProductType(id, productTypeRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลประเภทสินค้า"))
    } catch (e: BadRequestException) {
      logger.error("update product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลประเภทสินค้าซ้ำ"))
    } catch (e: Exception) {
      logger.error("update product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถบันทึกข้อมูลประเภทสินค้า กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-type/page")
  fun findAllProductType(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงรายการข้อมูลประเภทสินค้าสำเร็จ",
          data = productTypeService.findPageProductType(
            pageable,
            ascending,
            search,
            isActive
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/product-type/{id}")
  fun findProductTypeById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลประเภทสินค้าสำเร็จ",
          data = productTypeService.findProductTypeById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find product type by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลประเภทสินค้า"))
    } catch (e: Exception) {
      logger.error("find product type by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/product-type/{id}")
  fun deleteProductType(
    @PathVariable id: Long,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบข้อมูลประเภทสินค้าสำเร็จ",
          data = productTypeService.deleteProductType(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลประเภทสินค้า"))
    } catch (e: BadRequestException) {
      logger.error("delete product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลประเภทสินค้าได้ มีการใช้งานอยู่"))
    } catch (e: Exception) {
      logger.error("delete product type error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

}
