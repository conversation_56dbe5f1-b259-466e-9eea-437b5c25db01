package com.lucablock.digiboxapi.producttype.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.ProductType
import com.lucablock.digiboxapi.producttype.dto.ProductTypeDto
import com.lucablock.digiboxapi.producttype.repository.ProductTypeRepository
import com.lucablock.digiboxapi.producttype.request.ProductTypeRequest
import com.lucablock.digiboxapi.producttype.service.ProductTypeService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProductTypeServiceImpl @Autowired constructor(
  private val productTypeRepository: ProductTypeRepository,
  private val s3Service: S3Service
): ProductTypeService {
  @Transactional
  override fun createProductType(productTypeRequest: ProductTypeRequest): ProductTypeDto {
    productTypeRepository.existsByNameIgnoreCaseAndIsDeletedFalse(productTypeRequest.name).let {
      if (it) {
        throw Exception("ชื่อของประเภทสินค้าซ้ำ")
      }
    }
    val productType = ProductType(
        name = productTypeRequest.name,
        imageUrl = productTypeRequest.imageUrl,
      )

    val urlFile = s3Service.moveFile(productTypeRequest.imageUrl).url
    productType.imageUrl = urlFile
    val savedProductType = productTypeRepository.save(productType)

    return savedProductType.toProductTypeDto()
  }

  override fun updateProductType(id: Long, productTypeRequest: ProductTypeRequest): ProductTypeDto {
    val productType = productTypeRepository.findById(id).orElseThrow{
      throw Exception("ไม่พบข้อมูลประเภทสินค้า")
    }
      productTypeRepository.existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(productTypeRequest.name,id).let {
        if (it) {
          throw Exception("ชื่อของประเภทสินค้าซ้ำ")
        }
      }
    productType.name = productTypeRequest.name
    productType.isActive = productTypeRequest.isActive

    if (productTypeRequest.imageUrl != productType.imageUrl) {
      s3Service.deleteFile(productType.imageUrl)
      val newFileUrl = s3Service.moveFile(productTypeRequest.imageUrl).url
      productType.imageUrl = newFileUrl
    }

    val updateProductType = productTypeRepository.save(productType)
    return updateProductType.toProductTypeDto()
  }

  override fun findProductTypeById(id: Long): ProductTypeDto {
    val productType = productTypeRepository.findById(id).orElseThrow{
      throw Exception("ไม่พบข้อมูลประเภทสินค้า")
    }
    return productType.toProductTypeDto()
  }

  override fun findPageProductType(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<ProductTypeDto> {
    return productTypeRepository.findPageProductType(
      pageable, ascending, search, isActive)
  }

  override fun deleteProductType(id: Long): Boolean {
    val productType = productTypeRepository.findById(id).orElseThrow{
      throw Exception("ไม่พบข้อมูลประเภทสินค้า")
    }
    productType.isActive = false
    productType.isDeleted = true
    productTypeRepository.save(productType)
    return true
  }
}