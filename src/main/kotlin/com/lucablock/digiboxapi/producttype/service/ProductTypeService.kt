package com.lucablock.digiboxapi.producttype.service

import com.lucablock.digiboxapi.producttype.dto.ProductTypeDto
import com.lucablock.digiboxapi.producttype.request.ProductTypeRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface ProductTypeService {
  fun createProductType(productTypeRequest: ProductTypeRequest): ProductTypeDto
  fun updateProductType(id: Long, productTypeRequest: ProductTypeRequest): ProductTypeDto
  fun findProductTypeById(id: Long): ProductTypeDto
  fun findPageProductType(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?
  ): Page<ProductTypeDto>
  fun deleteProductType(id: Long): <PERSON>olean
}