package com.lucablock.digiboxapi.producttype.service.impl

import com.lucablock.digiboxapi.entity.QProductTag
import com.lucablock.digiboxapi.entity.QProductType
import com.lucablock.digiboxapi.producttype.dto.ProductTypeDto
import com.lucablock.digiboxapi.producttype.repository.ProductTypeRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProductTypeRepositoryImpl : ProductTypeRepositoryCustom{
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }
  private val qProductType = QProductType.productType

  override fun findPageProductType(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?,
  ): Page<ProductTypeDto> {
    var criteria = qProductType.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qProductType.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qProductType.name.containsIgnoreCase(search))
    }

    val sort = if (ascending) {
      qProductType.id.asc()
    } else {
      qProductType.id.desc()
    }

    val query = queryFactory
      .select(qProductType)
      .from(qProductType)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toProductTypeDto()
      }

    val total = queryFactory
      .select(qProductType)
      .from(qProductType)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}