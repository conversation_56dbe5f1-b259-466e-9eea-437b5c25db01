package com.lucablock.digiboxapi.unfoldedsize.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero

class UnfoldedSizeRequest {
  @NotBlank(message = "กรุณากรอกชื่อ")
  val name : String = ""

  @NotNull(message = "กรุณากรอกความกว้าง")
  @Positive(message = "กรุณากรอกความกว้างเป็นจำนวนบวก")
  val width : Double = 0.0

  @NotNull(message = "กรุณากรอกความสูง")
  @Positive(message = "กรุณากรอกความสูงเป็นจำนวนบวก")
  val height : Double = 0.0

  @NotNull(message = "กรุณากรอกค่าความคาดเคลื่อน (ตารางนิ้ว)")
  @Positive(message = "กรุณากรอกค่าความคาดเคลื่อน(ตารางนิ้ว) ให้ถูกต้อง")
  val overSizeLimit : Double = 0.0

  @JsonProperty("isActive")
  val isActive : Boolean = true
}