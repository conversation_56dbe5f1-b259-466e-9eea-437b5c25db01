package com.lucablock.digiboxapi.unfoldedsize.service.impl

import com.lucablock.digiboxapi.entity.UnfoldedSize
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeDto
import com.lucablock.digiboxapi.unfoldedsize.repository.UnfoldedSizeRepository
import com.lucablock.digiboxapi.unfoldedsize.request.UnfoldedSizeRequest
import com.lucablock.digiboxapi.unfoldedsize.service.UnfoldedSizeService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.stereotype.Service
import org.springframework.data.domain.Pageable

@Service
class UnfoldedSizeServiceImpl @Autowired constructor(
  private val unfoldedSizeRepository: UnfoldedSizeRepository
):UnfoldedSizeService{
  @Transactional
  override fun createUnfoldedSize(unfoldedSizeRequest: UnfoldedSizeRequest): UnfoldedSizeDto {
    unfoldedSizeRepository.existsUnfoldedSizeByWidthAndHeightAndIsDeletedFalse(
      unfoldedSizeRequest.width,
      unfoldedSizeRequest.height
    ).let {
      if (it) {
        throw BadRequestException("ข้อมูลขนาดกางออกนี้มีอยู่แล้ว")
      }
    }
    unfoldedSizeRepository.existsUnfoldedSizeByNameAndIsDeletedFalse(unfoldedSizeRequest.name).let {
      if (it){
        throw BadRequestException("ชื่อขนาดกางออกนี้มีอยู่แล้ว")
      }
    }
    val unfoldedSize = unfoldedSizeRepository.save(
      UnfoldedSize(
        name = unfoldedSizeRequest.name,
        width = unfoldedSizeRequest.width,
        height = unfoldedSizeRequest.height,
        overSizeLimit = unfoldedSizeRequest.overSizeLimit,
      )
    )
    return unfoldedSize.toUnfoldedSizeDto()
  }

  override fun updateUnfoldedSize(id: Long, unfoldedSizeRequest: UnfoldedSizeRequest): UnfoldedSizeDto {
    val unfoldedSize = unfoldedSizeRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดกางออกนี้")
    }
    unfoldedSizeRepository.existsUnfoldedSizeByWidthAndHeightAndIsDeletedFalseAndIdNot(
      unfoldedSizeRequest.width,
      unfoldedSizeRequest.height,
      id
    ).let {
      if (it) {
        throw BadRequestException("ข้อมูลขนาดกางออกนี้มีอยู่แล้ว")
      }
    }
    unfoldedSizeRepository.existsUnfoldedSizeByNameAndIsDeletedFalseAndIdNot(unfoldedSizeRequest.name,id).let {
      if (it){
        throw BadRequestException("ชื่อขนาดกางออกนี้มีอยู่แล้ว")
      }
    }
    unfoldedSize.name = unfoldedSizeRequest.name
    unfoldedSize.width = unfoldedSizeRequest.width
    unfoldedSize.height = unfoldedSizeRequest.height
    unfoldedSize.overSizeLimit = unfoldedSizeRequest.overSizeLimit
    unfoldedSize.isActive = unfoldedSizeRequest.isActive
    unfoldedSizeRepository.save(unfoldedSize)
    return unfoldedSize.toUnfoldedSizeDto()
  }

  override fun getUnfoldedSizeById(id: Long): UnfoldedSizeDto {
    val unfoldedSize = unfoldedSizeRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดกางออกนี้")
    }
    return unfoldedSize.toUnfoldedSizeDto()
  }

  override fun getAllUnfoldedSize(): List<UnfoldedSizeDto> {
    return unfoldedSizeRepository.findAll().map {
      it.toUnfoldedSizeDto()
    }
  }

  override fun deleteUnfoldedSize(id: Long): Boolean {
    val unfoldedSize = unfoldedSizeRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดกางออกนี้")
    }
    unfoldedSize.isDeleted = true
    unfoldedSizeRepository.save(unfoldedSize)
    return true
  }

  override fun getUnfoldedSizePage(
    pageable: Pageable,
    ascending: Boolean,
    sortBySize: Boolean?,
    search: String?,
    isActive: Boolean?,
  ): Page<UnfoldedSizeDto> {
    return unfoldedSizeRepository.getUnfoldedSizePage(
      pageable,
      ascending,
      sortBySize,
      search,
      isActive,
    )
  }
}