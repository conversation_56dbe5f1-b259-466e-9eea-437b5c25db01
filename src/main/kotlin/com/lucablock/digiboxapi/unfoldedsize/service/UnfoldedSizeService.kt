package com.lucablock.digiboxapi.unfoldedsize.service

import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeDto
import com.lucablock.digiboxapi.unfoldedsize.request.UnfoldedSizeRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface UnfoldedSizeService {
  fun createUnfoldedSize(unfoldedSizeRequest: UnfoldedSizeRequest):UnfoldedSizeDto
  fun updateUnfoldedSize(id:Long,unfoldedSizeRequest: UnfoldedSizeRequest):UnfoldedSizeDto
  fun getUnfoldedSizeById(id:Long):UnfoldedSizeDto
  fun getAllUnfoldedSize():List<UnfoldedSizeDto>
  fun deleteUnfoldedSize(id:Long):Boolean
  fun getUnfoldedSizePage(
    pageable: Pageable,
    ascending: Boolean,
    sortBySize:Boolean?,
    search: String?,
    isActive : Boolean?,
  ): Page<UnfoldedSizeDto>
}