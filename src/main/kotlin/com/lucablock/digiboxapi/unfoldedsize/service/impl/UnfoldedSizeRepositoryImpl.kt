package com.lucablock.digiboxapi.unfoldedsize.service.impl

import com.lucablock.digiboxapi.entity.QUnfoldedSize
import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeDto
import com.lucablock.digiboxapi.unfoldedsize.repository.UnfoldedSizeRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service


@Service
class UnfoldedSizeRepositoryImpl : UnfoldedSizeRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qUnfoldedSize = QUnfoldedSize.unfoldedSize

  override fun getUnfoldedSizePage(
    pageable: Pageable,
    ascending: Boolean,
    sortBySize: Boolean?,
    search: String?,
    isActive: Boolean?,
  ): Page<UnfoldedSizeDto> {
    var criteria = qUnfoldedSize.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qUnfoldedSize.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qUnfoldedSize.name.containsIgnoreCase(search))
    }

    val sort =
      if(sortBySize == null){
        if (ascending) {
          qUnfoldedSize.id.asc()
        } else {
          qUnfoldedSize.id.desc()
        }
      }else{
        if (sortBySize == true){
          qUnfoldedSize.width.asc()
        }else{
          qUnfoldedSize.width.desc()
        }
      }

    val query = queryFactory
      .select(qUnfoldedSize)
      .from(qUnfoldedSize)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toUnfoldedSizeDto()
      }

    val total = queryFactory
      .select(qUnfoldedSize)
      .from(qUnfoldedSize)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }

}