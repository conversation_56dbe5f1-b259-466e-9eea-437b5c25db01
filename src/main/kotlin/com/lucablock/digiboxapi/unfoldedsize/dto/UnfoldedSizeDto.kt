package com.lucablock.digiboxapi.unfoldedsize.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class UnfoldedSizeDto
@QueryProjection
constructor(
  val id: Long,
  val name: String,
  val width: Double,
  val height: Double,
  val overSizeLimit : Double,
  val isActive: <PERSON>olean = true,
  val isDeleted: Boolean = false,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
)

data class UnfoldedSizeListDto
@QueryProjection
constructor(
  val id: Long,
  val name: String,
)