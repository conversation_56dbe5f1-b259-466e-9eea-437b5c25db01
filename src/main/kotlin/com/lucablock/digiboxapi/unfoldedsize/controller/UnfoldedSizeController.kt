package com.lucablock.digiboxapi.unfoldedsize.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.unfoldedsize.request.UnfoldedSizeRequest
import com.lucablock.digiboxapi.unfoldedsize.service.UnfoldedSizeService
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping("api")
class UnfoldedSizeController {
  private val logger: Logger = LoggerFactory.getLogger(UnfoldedSizeController::class.java)

  @Autowired
  lateinit var unfoldedSizeService: UnfoldedSizeService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/unfolded-size")
  fun createUnfoldedSize(
    @Valid @RequestBody unFoldedSizeRequest: UnfoldedSizeRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลขนาดกางออกสำเร็จ",
          unfoldedSizeService.createUnfoldedSize(unFoldedSizeRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลขนาดกางออกนี้มีอยู่แล้ว"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถสร้างข้อมูลขนาดกางออก กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/unfolded-size/{id}")
  fun updateUnfoldedSize(
    @PathVariable id: Long,
    @Valid @RequestBody unfoldedSizeRequest: UnfoldedSizeRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลขนาดกางออกสำเร็จ",
          unfoldedSizeService.updateUnfoldedSize(id, unfoldedSizeRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลขนาดกางออกนี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลขนาดกางออกนี้"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลขนาดกางออก กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @GetMapping("/web/unfolded-size")
  fun findAllUnfoldedSize(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          unfoldedSizeService.getAllUnfoldedSize()
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/unfolded-size/{id}")
  fun findUnfoldedSizeById(@PathVariable id: Long)
  : ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          unfoldedSizeService.getUnfoldedSizeById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลขนาดกางออกนี้"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/unfolded-size/{id}")
  fun deleteUnfoldedSize(@PathVariable id: Long)
  : ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลขนาดกางออกสำเร็จ",
          unfoldedSizeService.deleteUnfoldedSize(id)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลขนาดกางออกนี้ มีการใช้งานอยู"))
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลขนาดกางออกนี้"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @GetMapping("/web/unfolded-size/page")
  fun findUnfoldedSizePage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "true") ascending: Boolean,
    @RequestParam("sortBySize", required = false) sortBySize: Boolean?,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = unfoldedSizeService.getUnfoldedSizePage(
            pageable,
            ascending,
            sortBySize,
            search,
            isActive
          )
        )
      )
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

}