package com.lucablock.digiboxapi.unfoldedsize.repository

import com.lucablock.digiboxapi.entity.UnfoldedSize
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface UnfoldedSizeRepository : JpaRepository<UnfoldedSize, Long> , UnfoldedSizeRepositoryCustom{
  fun existsUnfoldedSizeByWidthAndHeightAndIsDeletedFalse(width: Double, height: Double): Boolean
  fun existsUnfoldedSizeByWidthAndHeightAndIdNot(width: Double, height: Double,id: Long ): Boolean
  fun existsUnfoldedSizeByWidthAndHeightAndIsDeletedFalseAndIdNot(width: Double, height: Double, id: Long): Boolean
  fun existsUnfoldedSizeByNameAndIsDeletedFalse(name: String): Boolean
  fun existsUnfoldedSizeByNameAndIsDeletedFalseAndIdNot(name: String, id: Long): <PERSON><PERSON><PERSON>
  @Query("SELECT * FROM unfolded_size ORDER BY width ASC , height ASC ", nativeQuery = true)
  fun findAllOrderByWidthAndHeightDesc(): List<UnfoldedSize>
}