package com.lucablock.digiboxapi.unfoldedsize.repository

import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeDto
import org.springframework.data.domain.Page
import org.springframework.stereotype.Repository
import org.springframework.data.domain.Pageable

@Repository
interface UnfoldedSizeRepositoryCustom {
  fun getUnfoldedSizePage(
    pageable: Pageable,
    ascending: Boolean,
    sortBySize:Boolean?,
    search: String?,
    isActive : Boolean?,
  ): Page<UnfoldedSizeDto>
}