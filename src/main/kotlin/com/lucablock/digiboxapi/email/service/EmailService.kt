package com.lucablock.digiboxapi.email.service

import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.user.dto.UserPrincipal

interface EmailService {
  /**
   * Sends a password reset email to the user.
   * @param user The user entity to whom the email will be sent.
   * @param token The password reset token.
   */
  fun sendPasswordResetEmail(user: User, token: String)

  /**
   * Sends an email verification email to the user.
   * @param userPrincipal The user principal containing email and name.
   * @param verificationToken The token to be included in the verification link.
   * @param host The host information to construct the verification link (e.g., "localhost:8080").
   */
  fun sendVerificationEmail(userPrincipal: UserPrincipal, verificationToken: String?, host: String)
}
