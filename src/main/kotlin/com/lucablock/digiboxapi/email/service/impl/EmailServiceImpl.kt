package com.lucablock.digiboxapi.email.service.impl

import com.lucablock.digiboxapi.email.service.EmailService
import com.lucablock.digiboxapi.entity.User
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import org.springframework.beans.factory.annotation.Value
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.stereotype.Service
import org.thymeleaf.context.Context
import org.thymeleaf.spring6.SpringTemplateEngine
import java.nio.charset.StandardCharsets

@Service
class EmailServiceImpl(
  private val emailSender: JavaMailSender,
  private val templateEngine: SpringTemplateEngine,
  @Value("\${appUrl}") private val appUrl: String // For password reset
) : EmailService {

  override fun sendPasswordResetEmail(user: User, token: String) {
    val message = emailSender.createMimeMessage()
    val helper = MimeMessageHelper(
      message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
      StandardCharsets.UTF_8.name()
    )
    val context = Context()
    context.setVariable("name", user.username ?: user.firstName ?: "User")
    context.setVariable("link", "$appUrl/reset-password?token=$token")
    val html = templateEngine.process("forgot_template.html", context)
    helper.setSubject("Digiboxs: Please reset your password.")
    helper.setTo(user.email!!)
    helper.setText(html, true)
    emailSender.send(message)
  }

  override fun sendVerificationEmail(
    userPrincipal: UserPrincipal,
    verificationToken: String?,
    host: String
  ) {
    val userEmail = userPrincipal.email
    val userName = userPrincipal.name ?: "User"

    val message = emailSender.createMimeMessage()
    val helper = MimeMessageHelper(
      message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
      StandardCharsets.UTF_8.name()
    )
    val context = Context()
    context.setVariable("name", userName)
    // Aligning with original UserServiceImpl.verifyEmail logic
    context.setVariable("link", "https://$host/api/user/verifybyemail/$verificationToken")
    val html = templateEngine.process("register_template.html", context) // Using original template
    helper.setSubject("Digiboxs: กรุณายืนยันอีเมลของคุณ") // Using original subject
    if (userEmail != null) {
      helper.setTo(userEmail)
    }
    helper.setText(html, true)
    emailSender.send(message)
  }
}
