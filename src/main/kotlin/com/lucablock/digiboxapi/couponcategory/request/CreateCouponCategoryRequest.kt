package com.lucablock.digiboxapi.couponcategory.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import org.hibernate.validator.constraints.URL

data class CreateCouponCategoryRequest (
  @NotBlank(message = "Name must not be blank")
  @Size(min = 3, max = 100, message = "Name must be between 3 and 100 characters long")
  val name: String,
  @NotBlank(message = "Image URL must not be blank")
  @URL(message = "Invalid image URL format")
  val imageUrl: String,
  val description: String? = null,
)