package com.lucablock.digiboxapi.couponcategory.request

import jakarta.validation.constraints.Size
import org.hibernate.validator.constraints.URL

data class UpdateCouponCategoryRequest (
  @Size(min = 3, max = 100, message = "Name must be between 3 and 100 characters long")
  val name: String? = null,
  @URL(message = "Invalid image URL format")
  val imageUrl: String? = null,
  val description: String? = null
)