package com.lucablock.digiboxapi.couponcategory.repository

import com.lucablock.digiboxapi.couponcategory.dto.CouponCategoryDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CouponCategoryRepositoryCustom {
  fun getListCouponCategory(
    ascending: <PERSON><PERSON><PERSON>,
  ): List<CouponCategoryDto>
  fun getPaginationCouponCategory(
    pageable: Pageable,
    ascending: <PERSON><PERSON><PERSON>,
  ):  Page<CouponCategoryDto>
}