package com.lucablock.digiboxapi.couponcategory.repository

import com.lucablock.digiboxapi.entity.CouponCategory
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface CouponCategoryRepository: JpaRepository<CouponCategory, Long>, CouponCategoryRepositoryCustom {
  fun existsCouponCategoryByNameIgnoreCase(name: String): Boolean
  fun findCouponCategoryByNameIgnoreCaseAndIdNot(name: String, id: Long): Optional<CouponCategory>
  fun findByIdAndIsDeletedFalse(id: Long): Optional<CouponCategory>
}