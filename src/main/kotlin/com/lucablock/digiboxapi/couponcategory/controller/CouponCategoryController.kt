package com.lucablock.digiboxapi.couponcategory.controller

import com.lucablock.digiboxapi.couponcategory.request.CreateCouponCategoryRequest
import com.lucablock.digiboxapi.couponcategory.request.UpdateCouponCategoryRequest
import com.lucablock.digiboxapi.couponcategory.service.CouponCategoryService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
class CouponCategoryController @Autowired constructor(
  private val couponCategoryService: CouponCategoryService
) {
  private val logger = LoggerFactory.getLogger(CouponCategoryController::class.java)

  @PostMapping("/admin/coupon-category")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun createCouponCategory(
    @Valid @RequestBody request: CreateCouponCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "สร้างหมวดหมู่คูปองสำเร็จ",
          data = couponCategoryService.createCouponCategory(request)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("Error creating coupon category", e)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อหมวดหมู่คูปองนี้มีอยู่แล้ว")
      )
    }  catch (e: Exception) {
      logger.error("Error creating coupon category", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการสร้างหมวดหมู่คูปอง")
      )
    }
  }

  @PutMapping("/admin/coupon-category/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun updateCouponCategory(
    @PathVariable id: Long,
    @Valid @RequestBody request: UpdateCouponCategoryRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "อัปเดตหมวดหมู่คูปองสำเร็จ",
          data = couponCategoryService.updateCouponCategory(id, request)
        )
      )
    }  catch (e: BadRequestException) {
      logger.error("Error updating coupon category", e)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อหมวดหมู่คูปองนี้มีอยู่แล้ว")
      )
    } catch (e: NotFoundException) {
      logger.error("Error updating coupon category", e)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบหมวดหมู่คูปอง")
      )
    } catch (e: Exception) {
      logger.error("Error updating coupon category", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการอัปเดตหมวดหมู่คูปอง")
      )
    }
  }

  @DeleteMapping("/admin/coupon-category/{id}")
  @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'ADMIN')")
  fun deleteCouponCategory(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ลบหมวดหมู่คูปองสำเร็จ",
          data = couponCategoryService.deleteCouponCategory(id)
        )
      )
    }  catch (e: BadRequestException) {
      logger.error("Error deleting coupon category", e)
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ไม่สามารถลบหมวดหมู่คูปองได้")
      )
    } catch (e: NotFoundException) {
      logger.error("Error deleting coupon category", e)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบหมวดหมู่คูปอง")
      )
    } catch (e: Exception) {
      logger.error("Error deleting coupon category", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการลบหมวดหมู่คูปอง")
      )
    }
  }

  @GetMapping("/web/coupon-category/{id}")
  fun getCouponCategoryById(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลหมวดหมู่คูปองสำเร็จ",
          data = couponCategoryService.getCouponCategoryById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Error getting coupon category by ID", e)
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบหมวดหมู่คูปอง")
      )
    } catch (e: Exception) {
      logger.error("Error getting coupon category by ID", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงข้อมูลหมวดหมู่คูปอง")
      )
    }
  }

  @GetMapping("/web/coupon-category/list")
  fun getListCouponCategory(
    @RequestParam ascending: Boolean = true
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลหมวดหมู่คูปองสำเร็จ",
          data = couponCategoryService.getListCouponCategory(ascending)
        )
      )
    } catch (e: Exception) {
      logger.error("Error getting pagination coupon category", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงข้อมูลหมวดหมู่คูปอง")
      )
    }
  }

  @GetMapping("/web/coupon-category/page")
  fun getPaginationCouponCategory(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "10") size: Int,
    @RequestParam("ascending", defaultValue = "true") ascending: Boolean
  ): ResponseEntity<Any> {
    return try {
      val pageable = PageRequest.of(page, size)
      ResponseEntity.ok().body(
        HttpResponse(
          status = true,
          message = "ดึงข้อมูลหมวดหมู่คูปองสำเร็จ",
          data = couponCategoryService.getPaginationCouponCategory(pageable, ascending)
        )
      )
    } catch (e: Exception) {
      logger.error("Error getting pagination coupon category", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "เกิดข้อผิดพลาดในการดึงข้อมูลหมวดหมู่คูปอง")
      )
    }
  }
}