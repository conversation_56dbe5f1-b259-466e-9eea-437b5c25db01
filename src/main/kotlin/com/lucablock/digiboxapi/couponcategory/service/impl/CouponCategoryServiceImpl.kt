package com.lucablock.digiboxapi.couponcategory.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.couponcategory.dto.CouponCategoryDto
import com.lucablock.digiboxapi.couponcategory.repository.CouponCategoryRepository
import com.lucablock.digiboxapi.couponcategory.request.CreateCouponCategoryRequest
import com.lucablock.digiboxapi.couponcategory.request.UpdateCouponCategoryRequest
import com.lucablock.digiboxapi.couponcategory.service.CouponCategoryService
import com.lucablock.digiboxapi.entity.CouponCategory
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CouponCategoryServiceImpl @Autowired constructor(
  private val couponCategoryRepository: CouponCategoryRepository,
  private val s3Service: S3Service
): CouponCategoryService {
  @Transactional
  override fun createCouponCategory(request: CreateCouponCategoryRequest): CouponCategoryDto {
    val trimmedName = request.name.trim()

    if (couponCategoryRepository.existsCouponCategoryByNameIgnoreCase(trimmedName)) {
      throw BadRequestException("หมวดหมู่คูปองนี้มีอยู่แล้ว")
    }

    val finalImageUrl = try {
      s3Service.moveFile(request.imageUrl).url
    } catch (e: Exception) {
      throw BadRequestException("ไม่สามารถอัปโหลดไฟล์ภาพได้: ${e.message}")
    }

    val newCouponCategory = CouponCategory(
      name = request.name,
      description = request.description,
      imageUrl = finalImageUrl,
    )

    val savedCouponCategory = couponCategoryRepository.save(newCouponCategory)

    return savedCouponCategory.couponCategoryDto()
  }

  @Transactional
  override fun updateCouponCategory(
    id: Long,
    request: UpdateCouponCategoryRequest
  ): CouponCategoryDto {
    val couponCategory = couponCategoryRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบหมวดหมู่คูปอง")
    }

    request.name?.trim()?.let { trimmedName ->
      couponCategoryRepository.findCouponCategoryByNameIgnoreCaseAndIdNot(trimmedName, id).ifPresent {
        throw BadRequestException("หมวดหมู่คูปองนี้มีอยู่แล้ว")
      }
      couponCategory.name = trimmedName
    }

    request.description?.let {
      couponCategory.description = it
    }

    request.imageUrl?.let { newImage ->
      if (newImage != couponCategory.imageUrl) {
        try {
          s3Service.deleteFile(couponCategory.imageUrl)
          val moveFile = s3Service.moveFile(newImage).url
          couponCategory.imageUrl = moveFile
        } catch (e: Exception) {
          throw BadRequestException("ไม่สามารถอัปโหลดไฟล์ภาพได้: ${e.message}")
        }
      }
    }
    val saveCouponCategory = couponCategoryRepository.save(couponCategory)

    return saveCouponCategory.couponCategoryDto()

  }

  @Transactional
  override fun deleteCouponCategory(id: Long): Boolean {
    val couponCategory = couponCategoryRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบหมวดหมู่คูปอง")
    }

    couponCategory.isDeleted = true
    couponCategoryRepository.save(couponCategory)
    return true
  }

  override fun getCouponCategoryById(id: Long): CouponCategoryDto {
    val couponCategory = couponCategoryRepository.findByIdAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบหมวดหมู่คูปอง")
    }

    return couponCategory.couponCategoryDto()
  }

  override fun getListCouponCategory(
    ascending: Boolean
  ): List<CouponCategoryDto> {
    return couponCategoryRepository.getListCouponCategory(ascending)
  }

  override fun getPaginationCouponCategory(
    pageable: Pageable,
    ascending: Boolean
  ): Page<CouponCategoryDto> {
    return couponCategoryRepository.getPaginationCouponCategory(pageable, ascending)
  }
}