package com.lucablock.digiboxapi.couponcategory.service

import com.lucablock.digiboxapi.couponcategory.dto.CouponCategoryDto
import com.lucablock.digiboxapi.couponcategory.request.CreateCouponCategoryRequest
import com.lucablock.digiboxapi.couponcategory.request.UpdateCouponCategoryRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CouponCategoryService {
  fun createCouponCategory(request: CreateCouponCategoryRequest): CouponCategoryDto
  fun updateCouponCategory(id: Long, request: UpdateCouponCategoryRequest): CouponCategoryDto
  fun deleteCouponCategory(id: Long): Boolean
  fun getCouponCategoryById(id: Long): CouponCategoryDto
  fun getListCouponCategory(
    ascending: Boolean,
  ): List<CouponCategoryDto>
  fun getPaginationCouponCategory(
    pageable: Pageable,
    ascending: Boolean,
  ): Page<CouponCategoryDto>
}