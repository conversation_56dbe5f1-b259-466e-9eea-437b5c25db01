package com.lucablock.digiboxapi.couponcategory.service.impl

import com.lucablock.digiboxapi.couponcategory.dto.CouponCategoryDto
import com.lucablock.digiboxapi.couponcategory.repository.CouponCategoryRepositoryCustom
import com.lucablock.digiboxapi.entity.QCouponCategory
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CouponCategoryRepositoryImpl: CouponCategoryRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qCouponCategory = QCouponCategory.couponCategory

  override fun getListCouponCategory(
    ascending: Boolean
  ): List<CouponCategoryDto> {
    val criteria = qCouponCategory.isDeleted.isFalse

    val orderBy = if (ascending) {
      qCouponCategory.id.asc()
    } else {
      qCouponCategory.id.desc()
    }

    val query = queryFactory
      .select(qCouponCategory)
      .from(qCouponCategory)
      .where(criteria)
      .orderBy(orderBy)
      .fetch()


    return query.map { it.couponCategoryDto() }
  }

  override fun getPaginationCouponCategory(
    pageable: Pageable,
    ascending: Boolean
  ): Page<CouponCategoryDto> {
    val criteria = qCouponCategory.isDeleted.isFalse

    val orderBy = if (ascending) {
      qCouponCategory.id.asc()
    } else {
      qCouponCategory.id.desc()
    }

    val query = queryFactory
      .select(qCouponCategory)
      .from(qCouponCategory)
      .where(criteria)
      .orderBy(orderBy)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()

    val total = queryFactory
      .select(qCouponCategory)
      .from(qCouponCategory)
      .where(criteria)
      .fetchCount()

    val couponCategories = query.map { it.couponCategoryDto() }

    return PageImpl(couponCategories, pageable, total)
  }
}