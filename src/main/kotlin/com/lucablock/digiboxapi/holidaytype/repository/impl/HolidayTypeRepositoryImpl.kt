package com.lucablock.digiboxapi.holidaytype.repository.impl

import com.lucablock.digiboxapi.entity.QHolidayType
import com.lucablock.digiboxapi.holidaytype.dto.HolidayTypeDto
import com.lucablock.digiboxapi.holidaytype.repository.HolidayTypeRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class HolidayTypeRepositoryImpl : HolidayTypeRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qHolidayType = QHolidayType.holidayType

  override fun getPageHolidayType(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    holidayCategoryId: Long?,
    isActive: Boolean?
  ): Page<HolidayTypeDto> {
    var criteria = qHolidayType.isNotNull

    if (isActive != null) {
      criteria = criteria.and(qHolidayType.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qHolidayType.name.containsIgnoreCase(search))
    }
    if (holidayCategoryId != null) {
      criteria = criteria.and(qHolidayType.holidayCategoryId.eq(holidayCategoryId))
    }

    val sort = if (ascending) {
      qHolidayType.name.asc()
    } else {
      qHolidayType.name.desc()
    }

    val query = queryFactory
      .select(qHolidayType)
      .from(qHolidayType)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toHolidayTypeDto()
      }

    val total = queryFactory
      .select(qHolidayType.count())
      .from(qHolidayType)
      .where(criteria)
      .fetchOne() ?: 0L

    return PageImpl(query, pageable, total)
  }
}