package com.lucablock.digiboxapi.holidaytype.repository

import com.lucablock.digiboxapi.entity.HolidayType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface HolidayTypeRepository : JpaRepository<HolidayType, Long>, HolidayTypeRepositoryCustom {
  fun existsHolidayTypesByNameIgnoreCase(name : String):Boolean
  fun existsHolidayTypesByNameIgnoreCaseAndIdNot(name: String, id: Long): Boolean
  fun findAllHolidayTypeByIsActiveTrue():List<HolidayType>
  fun findAllByHolidayCategoryId(holidayCategoryId: Long): MutableList<HolidayType>
}