package com.lucablock.digiboxapi.holidaytype.repository

import com.lucablock.digiboxapi.holidaytype.dto.HolidayTypeDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface HolidayTypeRepositoryCustom {
  fun getPageHolidayType(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    holidayCategoryId: Long?,
    isActive: Boolean?
  ): Page<HolidayTypeDto>
}