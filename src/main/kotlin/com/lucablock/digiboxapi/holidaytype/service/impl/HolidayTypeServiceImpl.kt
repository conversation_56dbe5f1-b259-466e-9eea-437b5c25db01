package com.lucablock.digiboxapi.holidaytype.service.impl

import com.lucablock.digiboxapi.entity.HolidayType
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.holiday.repository.HolidayRepository
import com.lucablock.digiboxapi.holidaycategory.repository.HolidayCategoryRepository
import com.lucablock.digiboxapi.holidaytype.dto.HolidayTypeDto
import com.lucablock.digiboxapi.holidaytype.repository.HolidayTypeRepository
import com.lucablock.digiboxapi.holidaytype.request.HolidayTypeRequest
import com.lucablock.digiboxapi.holidaytype.service.HolidayTypeService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class HolidayTypeServiceImpl @Autowired constructor(
  private val holidayTypeRepository: HolidayTypeRepository,
  private val holidayCategoryRepository: HolidayCategoryRepository,
  private val holidayRepository: HolidayRepository
):HolidayTypeService{
  @Transactional
  override fun createHolidayType(holidayTypeRequest: HolidayTypeRequest): HolidayTypeDto {
    holidayTypeRepository.existsHolidayTypesByNameIgnoreCase(holidayTypeRequest.name).let {
      if(it){
        throw NotFoundException("ชื่อประเภทวันหยุดซ้ำ")
      }
    }
    val category = holidayCategoryRepository.findById(holidayTypeRequest.holidayCategoryId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลหมวดหมู่วันหยุด")
    }
    val holidayType = HolidayType(
      name = holidayTypeRequest.name,
      color = holidayTypeRequest.color,
      isActive = holidayTypeRequest.isActive,
      canRecur = holidayTypeRequest.canRecur,
      holidayCategoryId = holidayTypeRequest.holidayCategoryId,
      holidayCategory = category
    )
    val updateHolidayType = holidayTypeRepository.save(holidayType)
    return updateHolidayType.toHolidayTypeDto()
  }

  override fun updateHolidayType(id: Long, holidayTypeRequest: HolidayTypeRequest): HolidayTypeDto {
    val type = holidayTypeRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลประเภทวันหยุด")
    }
    val category = holidayCategoryRepository.findById(holidayTypeRequest.holidayCategoryId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลหมวดหมู่วันหยุด")
    }
    holidayTypeRepository.existsHolidayTypesByNameIgnoreCaseAndIdNot(holidayTypeRequest.name,id).let {
      if(it){
        throw NotFoundException("ชื่อประเภทวันหยุดซ้ำ")
      }
    }
    type.name = holidayTypeRequest.name
    type.color = holidayTypeRequest.color
    type.holidayCategoryId = holidayTypeRequest.holidayCategoryId
    type.isActive = holidayTypeRequest.isActive
    type.canRecur = holidayTypeRequest.canRecur
    type.holidayCategory = category
    val updateType = holidayTypeRepository.save(type)
    return updateType.toHolidayTypeDto()
  }

  override fun getAllHolidayType(): List<HolidayTypeDto> {
    val type = holidayTypeRepository.findAllHolidayTypeByIsActiveTrue()
    return type.map { it.toHolidayTypeDto() }
  }

  override fun getHolidayTypeById(id: Long): HolidayTypeDto {
    val type = holidayTypeRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลประเภทวันหยุด")
    }
    return type.toHolidayTypeDto()
  }

  override fun deleteHolidayType(id: Long): Boolean {
    val type = holidayTypeRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลประเภทวันหยุด")
    }
    val holiday = holidayRepository.findAllHolidayByHolidayTypeId(type.id)
    if(holiday.isNotEmpty()){
      throw BadRequestException("ไม่สามารถลบข้อมูลประเภทวันหยุดได้ เนื่องจากกำลังใช้งานอยู่")
    }else{
      holidayTypeRepository.deleteById(id)
      return true
    }
  }

  override fun getPageHolidayType(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    holidayCategoryId: Long?,
    isActive: Boolean?
  ): Page<HolidayTypeDto> {
    return holidayTypeRepository.getPageHolidayType(
      pageable,
      ascending,
      search,
      holidayCategoryId,
      isActive
    )
  }
}