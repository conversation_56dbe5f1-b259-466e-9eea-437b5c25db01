package com.lucablock.digiboxapi.holidaytype.service

import com.lucablock.digiboxapi.entity.HolidayType
import com.lucablock.digiboxapi.holidaytype.dto.HolidayTypeDto
import com.lucablock.digiboxapi.holidaytype.request.HolidayTypeRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface HolidayTypeService {
  fun createHolidayType(holidayTypeRequest: HolidayTypeRequest): HolidayTypeDto
  fun updateHolidayType(id:Long,holidayTypeRequest: HolidayTypeRequest): HolidayTypeDto
  fun getAllHolidayType():List<HolidayTypeDto>
  fun getHolidayTypeById(id:Long):HolidayTypeDto
  fun deleteHolidayType(id: Long): Boolean
  fun getPageHolidayType(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    holidayCategoryId: Long?,
    isActive: Boolean?
  ): Page<HolidayTypeDto>
}