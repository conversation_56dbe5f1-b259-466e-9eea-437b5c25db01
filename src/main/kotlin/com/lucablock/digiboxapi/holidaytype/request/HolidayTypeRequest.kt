package com.lucablock.digiboxapi.holidaytype.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive

class HolidayTypeRequest {
  @NotBlank(message = "กรุณาระบุชื่อประเภทวันหยุด")
  var name: String = ""
  @NotBlank(message = "กรุณาระบุสีประเภทวันหยุด")
  var color: String = ""
  @NotNull(message = "กรุณาระบุหมวดหมู่วันหยุด")
  @Positive(message = "กรุณาระบุหมวดหมู่วันหยุด")
  var holidayCategoryId: Long = 0
  @JsonProperty("isActive")
  var isActive: Boolean = true
  @JsonProperty("canRecur")
  var canRecur: Boolean = false
}