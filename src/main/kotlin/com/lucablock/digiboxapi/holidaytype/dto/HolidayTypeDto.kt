package com.lucablock.digiboxapi.holidaytype.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class HolidayTypeDto
@QueryProjection constructor(
  val id : Long,
  val name : String,
  val color : String,
  val holidayCategoryId : Long,
  val holidayCategoryName : String,
  val isActive : <PERSON>olean,
  val canRecur : <PERSON><PERSON><PERSON>,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createDate : Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updateDate : Date = Date()
)