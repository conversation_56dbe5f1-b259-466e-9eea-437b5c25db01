package com.lucablock.digiboxapi.company.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.company.dto.CompanyDto
import com.lucablock.digiboxapi.company.repository.CompanyRepository
import com.lucablock.digiboxapi.company.request.CompanyRequest
import com.lucablock.digiboxapi.company.service.CompanyService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.thaiAddress.repository.SubDistrictRepository
import com.lucablock.digiboxapi.thaiAddress.repository.ZipcodeRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CompanyServiceImpl @Autowired constructor(
  private val companyRepository: CompanyRepository,
  private val zipcodeRepository: ZipcodeRepository,
  private val subDistrictRepository: SubDistrictRepository,
  private val s3Service: S3Service
): CompanyService {
  override fun updateCompany(companyRequest: CompanyRequest): CompanyDto {
    val company = companyRepository.findById(1).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลบริษัท")
    }
    zipcodeRepository.existsByZipcode(companyRequest.zipCode).let {
      if(!it){
        throw BadRequestException("กรุณากรอกรหัสไปรษณีย์ให้ถูกต้อง")
      }
    }
    company.name = companyRequest.name
    company.taxId = companyRequest.taxId
    company.address = companyRequest.address
    company.zipCode = companyRequest.zipCode
    company.province = companyRequest.province
    company.district = companyRequest.district
    company.subDistrict = companyRequest.subDistrict
    company.phoneNumber = companyRequest.phoneNumber
    company.email = companyRequest.email
    company.lineId = companyRequest.lineId
    company.websiteUrl = companyRequest.websiteUrl
    company.mapUrl = companyRequest.mapUrl

    if (companyRequest.imageUrl != company.imageUrl) {
      s3Service.deleteFile(company.imageUrl)
      val newFileUrl = s3Service.moveFile(companyRequest.imageUrl).url
      company.imageUrl = newFileUrl
    }
    if (companyRequest.lineImage != company.lineImage) {
      if (!company.lineImage.isNullOrBlank()) {
        s3Service.deleteFile(company.lineImage!!)
      }
      val newFileUrl = s3Service.moveFile(companyRequest.lineImage!!).url
      company.lineImage = newFileUrl
    }
    val updateCompany = companyRepository.save(company)
    return updateCompany.toCompanyDto()
  }

  override fun getAllCompany(): List<CompanyDto> {
    return companyRepository.findCompanyByIsDeletedFalse().map { it.toCompanyDto() }
  }

  override fun getCompanyById(): CompanyDto {
    val company = companyRepository.findById(1).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลบริษัท")
    }
    return company.toCompanyDto()
  }

  override fun deleteCompany(): Boolean {
    val company = companyRepository.findById(1).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลบริษัท")
    }
    company.isDeleted = true
    companyRepository.save(company)
    return true
  }
}