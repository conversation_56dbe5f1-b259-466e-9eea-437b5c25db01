package com.lucablock.digiboxapi.company.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank

class CompanyRequest {
    @NotBlank(message = "กรุณากรอกชื่อบริษัท")
    val name : String = ""
    @NotBlank(message = "กรุณาอัปโหลดรูปโลโก้บริษัท")
    val imageUrl : String = ""
    @NotBlank(message = "กรุณากรอกที่อยู่บริษัท")
    val address : String = ""
    @NotBlank(message = "กรุณากรอกรหัสไปรษณีย์")
    val zipCode: String = ""
    @NotBlank(message = "กรุณากรอกจังหวัด")
    val province : String = ""
    @NotBlank(message = "กรุณากรอกอำเภอ")
    val district : String = ""
    @NotBlank(message = "กรุณากรอกตำบล")
    val subDistrict : String = ""
    @NotBlank(message = "กรุณากรอกหมายเลขโทรศัพท์")
    val phoneNumber : String = ""
    val taxId : String? = null
    val email : String? = null
    val lineId : String? = null
    val lineImage : String? = null
    val websiteUrl : String? = null
    val mapUrl : String? = null
}