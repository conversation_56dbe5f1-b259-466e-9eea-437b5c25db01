package com.lucablock.digiboxapi.company.controller

import com.lucablock.digiboxapi.company.request.CompanyRequest
import com.lucablock.digiboxapi.company.service.CompanyService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api/company")
class CompanyController {
  private val logger: Logger = LoggerFactory.getLogger(CompanyController::class.java)
  @Autowired
  lateinit var companyService: CompanyService

  @PreAuthorize("hasAnyRole('USER','ADMIN','SUPER_ADMIN')")
  @PutMapping("/update")
  fun updateCompany(
    @Valid @RequestBody companyRequest: CompanyRequest
  ): ResponseEntity<Any>{
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลบริษัทสำเร็จ",
          companyService.updateCompany(companyRequest)
        )
      )
    }catch (e: NotFoundException){
      logger.error("update company error : ${e.message}")
      ResponseEntity.badRequest().body(HttpResponse(false, e.message?: "ไม่พบข้อมูลบริษัท"))
    }catch (e: Exception) {
      logger.error("update company error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลบริษัท กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @GetMapping("/all")
  fun findAllCompany(
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          companyService.getAllCompany()
        )
      )
    } catch (e: Exception) {
      logger.error("find all company error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping()
  fun findCompanyById(): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          companyService.getCompanyById()
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find company by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลบริษัท"))
    } catch (e: Exception) {
      logger.error("find company by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('USER','ADMIN','SUPER_ADMIN')")
  @DeleteMapping("")
  fun deleteCompany(): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลสำเร็จ",
          companyService.deleteCompany()
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete company error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่พบข้อมูลบริษัท"))
    } catch (e: BadRequestException) {
      logger.error("delete company error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลบริษัทถูกใช้งานอยู่ ไม่สามารถลบได้"))
    } catch (e: Exception) {
      logger.error("delete company error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}