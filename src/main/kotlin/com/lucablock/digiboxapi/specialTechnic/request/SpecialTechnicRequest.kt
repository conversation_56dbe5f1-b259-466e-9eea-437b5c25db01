package com.lucablock.digiboxapi.specialTechnic.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.lucablock.digiboxapi.specialtechnicconfig.request.SpecialTechnicConfigRequest
import jakarta.validation.Valid
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

class SpecialTechnicRequest {
  @NotBlank(message = "กรุณากรอกชื่อเทคนิคพิเศษ")
  var name: String = ""

  var description : String? = null

  @NotBlank(message = "กรุณาระบุ url รูปภาพ")
  var imageUrl: String = ""

  @JsonProperty("isActive")
  var isActive:Boolean = true

  @Valid
  @NotEmpty(message = "กรุณากรอกข้อมูลพื้นที่และราคา")
  var specialTechnicConfig: List<SpecialTechnicConfigRequest> = mutableListOf()

//  @NotEmpty(message = "ต้องระบุหมวดหมู่สินค้า")
//  var categories: List<Long> = mutableListOf()


}