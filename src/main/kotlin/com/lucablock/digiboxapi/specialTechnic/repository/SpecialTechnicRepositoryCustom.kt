package com.lucablock.digiboxapi.specialTechnic.repository

import com.lucablock.digiboxapi.coating.dto.CoatingDto
import com.lucablock.digiboxapi.specialTechnic.dto.SpecialTechnicDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface SpecialTechnicRepositoryCustom {
  fun getSpecialTechnicPage(
    pageable: Pageable,
    ascending: <PERSON>olean,
    search: String?,
    productId : Long?,
    isActive : Boolean?
  ): Page<SpecialTechnicDto>
}