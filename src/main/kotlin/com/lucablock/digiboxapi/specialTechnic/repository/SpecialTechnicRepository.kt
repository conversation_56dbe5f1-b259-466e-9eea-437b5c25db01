package com.lucablock.digiboxapi.specialTechnic.repository

import com.lucablock.digiboxapi.entity.SpecialTechnic
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository


@Repository
interface SpecialTechnicRepository : JpaRepository<SpecialTechnic, Int>,SpecialTechnicRepositoryCustom {
  fun findAllByNameContainingIgnoreCase(name: String, pageable: Pageable): Page<SpecialTechnic>
  fun findAllByOrderByIdAsc(): List<SpecialTechnic>
  fun existsByNameIgnoreCaseAndIsDeletedFalse(name: String): Boolean
  fun existsByNameIgnoreCaseAndIdNot(name: String, id: Int): Boolean

  @Query(
    value = "SELECT * FROM special_technic st " +
        "JOIN product_category_special_technic pcst ON st.id = pcst.special_technic_id " +
        "WHERE pcst.product_category_id = :categoryId",
    nativeQuery = true
  )
  fun findAllByCategoryId(categoryId: Long): List<SpecialTechnic>
  fun existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(name: String, id: Int): Boolean
  fun findAllByIsActiveTrueOrderByIdAsc() : List<SpecialTechnic>
}