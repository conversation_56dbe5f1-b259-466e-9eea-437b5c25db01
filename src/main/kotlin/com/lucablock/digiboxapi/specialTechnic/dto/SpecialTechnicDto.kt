package com.lucablock.digiboxapi.specialTechnic.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.entity.SpecialTechnicConfig
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigDto
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class SpecialTechnicDto
@QueryProjection
constructor(
  val id: Int,
  val name: String,
  val description : String? = null,
  val imageUrl: String,
  val isActive: Boolean = true,
  val isDeleted: Boolean = false,
//  val categories: List<ProductSpecialTechnicDtoCategoryDto> = mutableListOf(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
  val specialTechnicConfig: List<SpecialTechnicConfigListDto>
)

data class SpecialTechnicConfigListDto
@QueryProjection
constructor(
  val id : Long,
  val price : Double,
  val period : Int,
  val specialTechnicId : Int,
  val areaSizePercentageId : Long,
  val areaSizePercentage : Int?
)