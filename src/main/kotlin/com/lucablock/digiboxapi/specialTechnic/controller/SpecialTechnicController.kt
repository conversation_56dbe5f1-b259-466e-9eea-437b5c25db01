package com.lucablock.digiboxapi.specialTechnic.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.specialTechnic.request.SpecialTechnicRequest
import com.lucablock.digiboxapi.specialTechnic.service.SpecialTechnicService
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping("api")
class SpecialTechnicController {
  private val logger: Logger = LoggerFactory.getLogger(SpecialTechnicController::class.java)

  @Autowired
  lateinit var specialTechnicService: SpecialTechnicService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/special-technic")
  fun createSpecialTechnic(
    @Valid @RequestBody specialTechnicRequest: SpecialTechnicRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลเทคนิคพิเศษสำเร็จ",
          specialTechnicService.createSpecialTechnic(specialTechnicRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("create special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message?: "เทคนิคพิเศษนี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error("create special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่มีหมวดหมู่ผลิตภัณฑ์"))
    } catch (e: Exception) {
      logger.error("create special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถสร้างข้อมูลเทคนิคพิเศษ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/special-technic")
  fun findAllSpecialTechnic(
    @RequestParam("category", required = false) category: Long?,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          specialTechnicService.findAllSpecialTechnics(category)
        )
      )
    } catch (e: Exception) {
      logger.error("find all special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/special-technic/search")
  fun searchSpecialTechnic(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("searchTerm", required = false) searchTerm: String?,
    @RequestParam("sortField", required = false) sortField: String?,
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นหาเทคนิคพิเศษสำเร็จ",
          specialTechnicService.searchSpecialTechnic(pageable, ascending, searchTerm, sortField)
        )
      )
    } catch (e: Exception) {
      logger.error("search special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/special-technic/{id}")
  fun findSpecialTechnicById(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          specialTechnicService.findSpecialTechnicById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("find special technic by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลเทคนิคพิเศษ"))
    } catch (e: Exception) {
      logger.error("find special technic by id error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/special-technic/{id}")
  fun updateSpecialTechnic(
    @PathVariable id: Int,
    @Valid @RequestBody specialTechnicRequest: SpecialTechnicRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลเทคนิคพิเศษสำเร็จ",
          specialTechnicService.updateSpecialTechnic(id, specialTechnicRequest)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("update special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "เทคนิคพิเศษนี้มีอยู่แล้ว"))
    } catch (e: NotFoundException) {
      logger.error("update special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลเทคนิคพิเศษ"))
    } catch (e: Exception) {
      logger.error("update special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลเทคนิคพิเศษ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @DeleteMapping("/admin/special-technic/{id}")
  fun deleteSpecialTechnic(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบข้อมูลสำเร็จ",
          specialTechnicService.deleteSpecialTechnic(id)
        )
      )
    } catch (e: BadRequestException) {
      logger.error("update special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถลบเทคนิคพิเศษได้ มีการใช้งานอยู"))
    } catch (e: NotFoundException) {
      logger.error("update special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลเทคนิคพิเศษ"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถลบข้อมูลได้ กรุณาลองอีกครั้ง"
          )
        )
    }
  }

  @GetMapping("/web/special-technic/page")
  fun findSpecialTechnicPage(
    @RequestParam("page", defaultValue = "0") page: Int,
    @RequestParam("size", defaultValue = "20") size: Int,
    @RequestParam("ascending", defaultValue = "false") ascending: Boolean,
    @RequestParam("search", required = false) search: String?,
    @RequestParam("productId", required = false) productId: Long?,
    @RequestParam("isActive", required = false) isActive: Boolean?,
    @RequestParam("isDeleted", required = false) isDeleted: Boolean?
  ): ResponseEntity<Any> {
    return try {
      val pageable: Pageable = PageRequest.of(page, size)
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงข้อมูลสำเร็จ",
          data = specialTechnicService.getSpecialTechnicPage(
            pageable,
            ascending,
            search,
            productId,
            isActive
          )
        )
      )
    } catch (e: Exception) {
      logger.error("find special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/special-technic/active")
  fun updateSpecialTechnicActive(
    @RequestParam("id" , required = true) id: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกข้อมูลเทคนิคพิเศษสำเร็จ",
          specialTechnicService.updateSpecialTechnicActive(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลเทคนิคพิเศษ"))
    } catch (e: Exception) {
      logger.error("update special technic error : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            e.message ?: "ไม่สามารถบันทึกข้อมูลเทคนิคพิเศษ กรุณาลองอีกครั้ง"
          )
        )
    }
  }
}