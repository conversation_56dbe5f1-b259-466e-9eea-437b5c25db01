package com.lucablock.digiboxapi.specialTechnic.service

import com.lucablock.digiboxapi.coating.dto.CoatingDto
import com.lucablock.digiboxapi.specialTechnic.dto.SpecialTechnicDto
import com.lucablock.digiboxapi.specialTechnic.request.SpecialTechnicRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.multipart.MultipartFile

interface SpecialTechnicService {
  fun createSpecialTechnic(specialTechnicRequest: SpecialTechnicRequest): SpecialTechnicDto
  fun findAllSpecialTechnics(category: Long?): List<SpecialTechnicDto>
  fun searchSpecialTechnic(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<SpecialTechnicDto>

  fun findSpecialTechnicById(id: Int): SpecialTechnicDto
  fun updateSpecialTechnic(id: Int, specialTechnicRequest: SpecialTechnicRequest): SpecialTechnicDto
  fun deleteSpecialTechnic(id: Int)
  fun getSpecialTechnicPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    productId : Long?,
    isActive : Boolean?
  ): Page<SpecialTechnicDto>
  fun updateSpecialTechnicActive(id: Int):Boolean
}