package com.lucablock.digiboxapi.specialTechnic.service.impl

import com.lucablock.digiboxapi.coating.dto.CoatingDto
import com.lucablock.digiboxapi.coating.repository.CoatingRepositoryCustom
import com.lucablock.digiboxapi.entity.QCoating
import com.lucablock.digiboxapi.entity.QProductSpecialTechnic
import com.lucablock.digiboxapi.entity.QSpecialTechnic
import com.lucablock.digiboxapi.specialTechnic.dto.SpecialTechnicDto
import com.lucablock.digiboxapi.specialTechnic.repository.SpecialTechnicRepositoryCustom
import com.querydsl.jpa.JPAExpressions
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class SpecialTechnicRepositoryImpl : SpecialTechnicRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qSpecialTechnic = QSpecialTechnic.specialTechnic
  private val qProductSpecialTechnic = QProductSpecialTechnic.productSpecialTechnic
  override fun getSpecialTechnicPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    productId : Long?,
    isActive : Boolean?
  ): Page<SpecialTechnicDto> {
    var criteria = qSpecialTechnic.isDeleted.eq(false)

    if (isActive != null) {
      criteria = criteria.and(qSpecialTechnic.isActive.eq(isActive))
    }
    if (search != null) {
      criteria = criteria.and(qSpecialTechnic.name.containsIgnoreCase(search))
    }
    if (productId != null) {
      val subQuery = JPAExpressions
        .select(qProductSpecialTechnic.specialTechnicId)
        .from(qProductSpecialTechnic)
        .where(qProductSpecialTechnic.product.id.eq(productId))
      criteria = criteria.and(qSpecialTechnic.id.`in`(subQuery))
    }

    val sort = if (ascending) {
      qSpecialTechnic.id.asc()
    } else {
      qSpecialTechnic.id.desc()
    }

    val query = queryFactory
      .select(qSpecialTechnic)
      .from(qSpecialTechnic)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        it.toSpecialTechnicDto()
      }

    val total = queryFactory
      .select(qSpecialTechnic)
      .from(qSpecialTechnic)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}