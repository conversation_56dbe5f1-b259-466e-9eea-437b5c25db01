package com.lucablock.digiboxapi.specialTechnic.service.impl

import com.lucablock.digiboxapi.areasizepercentage.repository.AreaSizePercentageRepository
import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.customize.repository.CustomizeSpecialTechnicRepository
import com.lucablock.digiboxapi.entity.QSpecialTechnic.specialTechnic
import com.lucablock.digiboxapi.entity.SpecialTechnic
import com.lucablock.digiboxapi.entity.SpecialTechnicConfig
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.item.repository.ItemSpecialTechnicRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategoryRepository
import com.lucablock.digiboxapi.productCategory.repository.ProductCategorySpecialTechnicRepository
import com.lucablock.digiboxapi.specialTechnic.dto.SpecialTechnicDto
import com.lucablock.digiboxapi.specialTechnic.repository.SpecialTechnicRepository
import com.lucablock.digiboxapi.specialTechnic.request.SpecialTechnicRequest
import com.lucablock.digiboxapi.specialTechnic.service.SpecialTechnicService
import com.lucablock.digiboxapi.specialtechnicconfig.repository.SpecialTechnicConfigRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service


@Service
class SpecialTechnicServiceImpl @Autowired constructor(
  private val specialTechnicRepository: SpecialTechnicRepository,
  private val productCategoryRepository: ProductCategoryRepository,
  private val productCategorySpecialTechnicRepository: ProductCategorySpecialTechnicRepository,
  private val itemSpecialTechnicRepository: ItemSpecialTechnicRepository,
  private val s3Service: S3Service,
  private val specialTechnicConfigRepository: SpecialTechnicConfigRepository,
  private val areaSizePercentageRepository: AreaSizePercentageRepository,
  private val customizeSpecialTechnicRepository: CustomizeSpecialTechnicRepository
) : SpecialTechnicService {

  @Transactional
  override fun createSpecialTechnic(specialTechnicRequest: SpecialTechnicRequest): SpecialTechnicDto {
    specialTechnicRepository.existsByNameIgnoreCaseAndIsDeletedFalse(specialTechnicRequest.name)
      .let { if (it) throw BadRequestException("เทคนิคพิเศษนี้มีอยู่แล้ว") }

    val areaSize = specialTechnicRequest.specialTechnicConfig.map { it.areaSizePercentageId }.toSet()
    val area = areaSizePercentageRepository.findAllById(areaSize)
    if (area.size != areaSize.size) {
      throw NotFoundException("ไม่พบข้อมูลพื้นที่")
    }

    val areaIds = specialTechnicRequest.specialTechnicConfig.map { it.areaSizePercentageId }
    if (areaIds.size != areaIds.toSet().size) {
      throw Exception("ไม่สามารถระบุพื้นที่ ซ้ำกันได้")
    }

    val savedSpecialTechnic =
      specialTechnicRepository.save(
        SpecialTechnic(
          name = specialTechnicRequest.name,
          description = specialTechnicRequest.description,
          imageUrl = specialTechnicRequest.imageUrl,
        )
      )

    val savedSpecialTechnicConfig = specialTechnicRequest.specialTechnicConfig.map {
      val areaSizePercentage = areaSizePercentageRepository.findById(it.areaSizePercentageId).orElseThrow{
        NotFoundException("ไม่พบข้อมูลพื้นที่")
      }
      specialTechnicConfigRepository.save(
        SpecialTechnicConfig(
          price = it.price,
          period = it.period,
          specialTechnicId = savedSpecialTechnic.id,
          specialTechnic = savedSpecialTechnic,
          areaSizePercentageId = areaSizePercentage.id.toLong(),
          areaSizePercentage = areaSizePercentage
        )
      )
    }
    savedSpecialTechnic.specialTechnicConfig = savedSpecialTechnicConfig

//    val categories = specialTechnicRequest.categories.map {
//      productCategoryRepository.findById(it)
//        .orElseThrow { NotFoundException("ไม่มีหมวดหมู่ผลิตภัณฑ์") }
//
//      ProductCategorySpecialTechnic(
//        productCategoryId = it,
//        specialTechnicId = savedSpecialTechnic.id.toLong(),
//      )
//    }
//    productCategorySpecialTechnicRepository.saveAll(categories)
//
    val newFileUrl = s3Service.moveFile(specialTechnicRequest.imageUrl).url
    savedSpecialTechnic.imageUrl = newFileUrl
    val updateSpecialTechnic = specialTechnicRepository.save(savedSpecialTechnic)

    return updateSpecialTechnic.toSpecialTechnicDto()
  }

  override fun findAllSpecialTechnics(category: Long?): List<SpecialTechnicDto> {
    if (category != null) {
      return specialTechnicRepository.findAllByCategoryId(category).map { it.toSpecialTechnicDto() }
    }
    return specialTechnicRepository.findAllByIsActiveTrueOrderByIdAsc().map { it.toSpecialTechnicDto() }
  }

  override fun searchSpecialTechnic(
    pageable: Pageable,
    ascending: Boolean,
    searchTerm: String?,
    sortField: String?
  ): Page<SpecialTechnicDto> {
    var term = ""
    if (searchTerm != null) {
      term = searchTerm
    }
    return specialTechnicRepository.findAllByNameContainingIgnoreCase(term, pageable)
      .map { it.toSpecialTechnicDto() }
  }

  override fun findSpecialTechnicById(id: Int): SpecialTechnicDto {
    return specialTechnicRepository.findById(id)
      .orElseThrow { throw NotFoundException("ไม่มีข้อมูลเทคนิคพิเศษ") }.toSpecialTechnicDto()
  }

  @Transactional
  override fun updateSpecialTechnic(
    id: Int,
    specialTechnicRequest: SpecialTechnicRequest
  ): SpecialTechnicDto {
    val specialTechnic =
      specialTechnicRepository.findById(id)
        .orElseThrow { NotFoundException("ไม่มีข้อมูลเทคนิคพิเศษ") }

    specialTechnicRepository.existsByNameIgnoreCaseAndIsDeletedFalseAndIdNot(specialTechnicRequest.name, id)
      .let { if (it) throw BadRequestException("เทคนิคพิเศษนี้มีอยู่แล้ว") }

    val areaSize = specialTechnicRequest.specialTechnicConfig.map { it.areaSizePercentageId }.toSet()
    val area = areaSizePercentageRepository.findAllById(areaSize)
    if (area.size != areaSize.size) {
      throw NotFoundException("ไม่พบข้อมูลพื้นที่")
    }
    val areaIds = specialTechnicRequest.specialTechnicConfig.map { it.areaSizePercentageId }
    if (areaIds.size != areaIds.toSet().size) {
      throw Exception("ไม่สามารถระบุพื้นที่ ซ้ำกันได้")
    }

    val specialTechnicConfig = specialTechnicConfigRepository.findAllBySpecialTechnicId(id)
    val customize = customizeSpecialTechnicRepository.findAllBySpecialTechnicConfigIdIn(specialTechnicConfig.map { it.id })
    val configToSoft = specialTechnicConfigRepository.findAllByIdIn(customize.map { it.specialTechnicConfigId }.toMutableSet())
    configToSoft.forEach { it.isDeleted = true }
    specialTechnicConfigRepository.saveAll(configToSoft)

    val configsToHardDelete = specialTechnicConfigRepository
      .findAllBySpecialTechnicIdAndIsDeletedFalse(id)
      .filterNot { it.id in customize.map { it.specialTechnicConfigId } }
    specialTechnicConfigRepository.deleteAll(configsToHardDelete)

    specialTechnic.name = specialTechnicRequest.name
    specialTechnic.description = specialTechnicRequest.description
    specialTechnic.isActive = specialTechnicRequest.isActive
    specialTechnic.specialTechnicConfig = specialTechnicRequest.specialTechnicConfig.map {
      val areaSizePercentage = areaSizePercentageRepository.findById(it.areaSizePercentageId).orElseThrow{
        NotFoundException("ไม่พบข้อมูลพื้นที่")
      }
      SpecialTechnicConfig(
        price = it.price,
        period = it.period,
        specialTechnicId = specialTechnic.id,
        areaSizePercentageId = it.areaSizePercentageId,
        specialTechnic = specialTechnic,
        areaSizePercentage = areaSizePercentage,
      )
    }

    if (specialTechnicRequest.imageUrl != specialTechnic.imageUrl) {
      s3Service.deleteFile(specialTechnic.imageUrl)
      val newFileUrl = s3Service.moveFile(specialTechnicRequest.imageUrl).url
      specialTechnic.imageUrl = newFileUrl
    }
//
//    productCategorySpecialTechnicRepository.deleteAllBySpecialTechnicId(id.toLong())
//    val category = specialTechnicRequest.categories.map {
//      productCategoryRepository.findById(it)
//        .orElseThrow { NotFoundException("ไม่มีหมวดหมู่ผลิตภัณฑ์") }
//
//      ProductCategorySpecialTechnic(
//        productCategoryId = it,
//        specialTechnicId = id.toLong(),
//      )
//    }
//    category.let { productCategorySpecialTechnicRepository.saveAll(it) }
    val updatedSpecialTechnic = specialTechnicRepository.save(specialTechnic)

    return updatedSpecialTechnic.toSpecialTechnicDto()
  }

  @Transactional
  override fun deleteSpecialTechnic(id: Int) {
    val specialTechnic = specialTechnicRepository.findById(id)
      .orElseThrow { NotFoundException("ไม่มีข้อมูลเทคนิคพิเศษ") }

//    itemSpecialTechnicRepository.existsBySpecialTechnicId(id)
//      .let { if (it) throw BadRequestException("ไม่สามารถลบเทคนิคพิเศษได้ มีการใช้งานอยู่") }
//
//    productCategorySpecialTechnicRepository.deleteAllBySpecialTechnicId(id.toLong())
//
//    s3Service.deleteFile(specialTechnic.imageUrl)
//    specialTechnicRepository.delete(specialTechnic)
    specialTechnic.isDeleted = true
    specialTechnicRepository.save(specialTechnic)
  }

  override fun getSpecialTechnicPage(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    productId: Long?,
    isActive: Boolean?
  ): Page<SpecialTechnicDto> {
    return specialTechnicRepository.getSpecialTechnicPage(pageable,ascending,search,productId,isActive)
  }


  override fun updateSpecialTechnicActive(id: Int): Boolean {
    val specialTechnic = specialTechnicRepository.findById(id).orElseThrow{
      NotFoundException("ไม่มีข้อมูลเทคนิคพิเศษ")
    }
    specialTechnic.isActive = !specialTechnic.isActive
    specialTechnicRepository.save(specialTechnic)
    return specialTechnic.isActive
  }

}