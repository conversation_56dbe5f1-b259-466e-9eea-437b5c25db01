package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.couponuser.dto.CouponUserDto
import jakarta.persistence.*

@Entity
data class CouponUser(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  @Column(name = "coupon_id", nullable = false)
  val couponId: Long,
  @Column(name = "user_id", nullable = false)
  val userId: Long,
  @Column(name = "is_used", nullable = false)
  var isUsed: Boolean = false,

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "coupon_id", insertable = false, updatable = false)
  val coupon: Coupon? = null,

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "user_id", insertable = false, updatable = false)
  val user: User? = null,
) {
  fun toCouponUserDto(): CouponUserDto {
    return CouponUserDto(
      id = id,
      user = userId,
      coupon = coupon?.toCouponDto(),
      isUsed = isUsed,
    )
  }
}
