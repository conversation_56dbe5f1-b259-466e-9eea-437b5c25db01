package com.lucablock.digiboxapi.entity

import jakarta.persistence.*
import java.util.*

@Entity
data class CouponUsageLog(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  @Column(name = "order_id", nullable = false)
  val orderId: Int,
  @Column(name = "coupon_id", nullable = false)
  val couponId: Long,
  @Column(name = "user_id", nullable = false)
  val userId: Long,
  @Column(name = "discount_amount")
  val discountAmount: Double,
  @Column(name = "total_price")
  val totalPrice: Double,
  @Column(name = "credit_amount")
  val creditAmount: Double? = null,
  @Column(name = "used_at")
  val usedAt: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "order_id", insertable = false, updatable = false)
  val order: Order? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "coupon_id", insertable = false, updatable = false)
  val coupon: Coupon? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", insertable = false, updatable = false)
  val user: User? = null
)
