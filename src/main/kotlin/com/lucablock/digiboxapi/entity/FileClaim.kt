package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "file_claim")
data class FileClaim(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  val orderClaimId: Int,
  var url: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),
): Serializable
