package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.model.dto.ModelThumbnailDto
import jakarta.persistence.*

@Entity
@Table(name = "model_thumbnail")
data class ModelThumbnail(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,

  @Column(name = "model_id")
  var modelId: Long,

  @Column(name = "is_cover")
  var isCover: Boolean? = false,

  @Column(name = "thumbnail_url")
  var thumbnailUrl: String,
) {
  fun toModelThumbnailDto(): ModelThumbnailDto {
    return ModelThumbnailDto(
      id = id,
      modelId = modelId,
      isCover = isCover,
      thumbnailUrl = thumbnailUrl
    )
  }
}
