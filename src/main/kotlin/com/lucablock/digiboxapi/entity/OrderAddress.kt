package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.order.dto.AddressOrderDto
import jakarta.persistence.*

@Entity
@Table(name = "order_address")
data class OrderAddress (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  @Column(name = "order_id")
  var orderId: Int,
  var name: String,
  @Column(name = "phone_number")
  var phoneNumber: String,
  var address: String,
  var zipcode: String,
  var province: String,
  var district: String,
  @Column(name = "sub_district")
  var subDistrict: String,
  @Column(name = "is_tax")
  var isTax: Boolean = false,
  @Column(name = "tax_player_type")
  var taxPayerType: Int? = null,
  @Column(name = "tax_id")
  var taxId: String? = null,
  var email: String? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "order_id", insertable = false, updatable = false)
  var order: Order,
)
{
  fun toAddressOrderDto(): AddressOrderDto {
    return AddressOrderDto(
      name = name,
      phoneNumber = phoneNumber,
      email = email,
      address = address,
      province = province,
      district = district,
      subDistrict = subDistrict,
      zipCode = zipcode,
      taxId = taxId,
      isTax = isTax
    )
  }
}