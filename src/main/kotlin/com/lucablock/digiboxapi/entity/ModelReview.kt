package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonBackReference
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.Date

@Entity
@Table(name = "model_reviews")
data class ModelReview(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,

  val modelId: Long,
  val userId: Long,
  val rating: Int,
  val comment: String? = null,
  val orderId: Long,



  @CreationTimestamp
  val createdDate: Date = Date(),
  @UpdateTimestamp
  val updatedDate: Date = Date(),

  @OneToMany(mappedBy = "modelReview", cascade = [CascadeType.ALL], fetch = FetchType.LAZY, orphanRemoval = true)
  val images: List<ModelReviewImage> = mutableListOf()


)
