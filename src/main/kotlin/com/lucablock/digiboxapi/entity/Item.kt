package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import java.io.Serializable
import java.util.*
import kotlin.collections.ArrayList

@Entity
@Table(name = "item")
data class Item(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  @Column(name = "model_id")
  var modelId: Int,
  @Column(name = "material_id")
  var materialId: Int,
  var width: Int,
  var length: Int,
  var height: Int,
  var amount: Int,
  var printing: Int,
  @Column(name = "printing_price")
  var printingPrice: Double,
  @Column(name = "coating_id")
  var coatingId: Int,
  @Column(name = "is_artwork")
  var isArtwork: Boolean = false,
  @Column(name = "artwork_price")
  var artworkPrice: Double,
  var artworkUrl: String? = null,
  var productDemo: Int,
  @Column(name = "product_demo_price")
  var productDemoPrice: Double,
  var description: String? = null,
  var amountUnitPrice: Double,
  var unitPrice: Double,
  var totalPrice: Double,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_id", insertable = false, updatable = false)
  var model: Model? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "material_id", insertable = false, updatable = false)
  var material: Materials? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "coating_id", insertable = false, updatable = false,)
  var coating: Coating? = null,

  @OneToMany(mappedBy = "itemId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var specialTechnics: List<ItemSpecialTechnic?> = ArrayList(),
) : Serializable


