package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.gram.dto.GramDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "gram")
data class Gram (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var gsm : Int,
  var mm : Double,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @OneToMany(mappedBy = "grams", fetch = FetchType.LAZY)
  var materialConfig : List<MaterialConfig> = mutableListOf(),
){
  fun toGramDto() : GramDto{
    return GramDto(
      id = id,
      gsm = gsm,
      mm = mm,
      createdDate = createdDate,
      updatedDate = updatedDate
    )
  }
}