package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.banner.dto.BannerDto
import jakarta.persistence.*
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedDate
import java.util.Date

@Entity
@Table(name = "banner")
data class Banner(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,

  @Column(name = "image_url")
  var imageUrl: String,

  var description: String? = null,
  var status: Boolean = false,

  @Column(name = "start_date_time")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var startDateTime: Date = Date(),

  @Column(name = "end_date_time")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var endDateTime: Date = Date(),

  @CreatedDate
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val modifiedDate: Date = Date()

) {
  fun bannerToDto(): BannerDto {
    return BannerDto(
      id = id,
      imageUrl = imageUrl,
      description = description,
      status = status,
      startDateTime = startDateTime,
      endDateTime = endDateTime,
      createdDate = createdDate,
      modifiedDate = modifiedDate
    )
  }
}

