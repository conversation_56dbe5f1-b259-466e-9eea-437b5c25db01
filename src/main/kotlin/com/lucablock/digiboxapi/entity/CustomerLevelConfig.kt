package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "customer_level_config")
data class CustomerLevelConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,
  @Column(name = "customer_level_id")
  var customerLevelId : Long,
  @Column(name = "user_id")
  var userId : Long,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customer_level_id", insertable = false, updatable = false)
  var customerLevel: CustomerLevel,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", insertable = false, updatable = false)
  var user: User,
){
  fun toUserCustomerLevelListDto(): UserCustomerLevelListDto{
    return UserCustomerLevelListDto(
      id = id,
      userId = user.id,
      name = user.fullName,
      customerLevelId = customerLevel.id,
      customerLevelName = customerLevel.name,
      createDate = createdDate,
      isDeleted = isDeleted,
    )
  }
}