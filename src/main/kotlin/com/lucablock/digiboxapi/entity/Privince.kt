package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "province")
data class Province(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  val geoId: Int,
  val provinceCode: String,
  val name: String,

  @OneToMany(mappedBy = "province", fetch = FetchType.LAZY)
  val district: List<District>? = null,

  @OneToMany(mappedBy = "province", fetch = FetchType.LAZY)
  val subDistrict: List<SubDistrict>? = null,

  @OneToMany(mappedBy = "province", fetch = FetchType.LAZY)
  val zipcode: List<Zipcode>? = null,
  )
