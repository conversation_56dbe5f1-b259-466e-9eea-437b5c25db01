package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.product.dto.ProductDto
import jakarta.persistence.*
import org.hibernate.annotations.UpdateTimestamp
import java.io.Serializable
import java.util.*


@Entity
@Table(name = "product")
data class Product(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,

  @Column(unique = true)
  var name: String,

  var description: String? = null,

  @Column(name = "guide_detail")
  var guideDetail: String,

  @Column(name = "installment_detail")
  var installmentDetail: String,

  @Column(name = "shipping_detail")
  var shippingDetail: String,

  @Column(name = "is_active")
  var isActive : Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,

  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @OneToMany(mappedBy = "product", fetch = FetchType.LAZY)
  var productCategoryConfig: List<ProductCategoryConfig> = mutableListOf(),

  @OneToMany(mappedBy = "product", fetch = FetchType.LAZY)
  var productTagConfig: List<ProductTagConfig> = mutableListOf(),

  @OneToMany(mappedBy = "product", fetch = FetchType.LAZY)
  var productTypeConfig: List<ProductTypeConfig> = mutableListOf(),

  @OneToMany(mappedBy = "product", fetch = FetchType.LAZY)
  var productSpecialTechnic: List<ProductSpecialTechnic> = mutableListOf(),

  @OneToMany(mappedBy = "product", fetch = FetchType.LAZY)
  var productGallery: List<ProductGallery> = mutableListOf(),

  @OneToMany(mappedBy = "product", fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
  var couponProduct: List<CouponProduct> = mutableListOf(),

) : Serializable {
  fun toProductDto(): ProductDto {
    return ProductDto(
      id = id,
      name = name,
      description = description,
      guideDetail = guideDetail,
      installmentDetail = installmentDetail,
      shippingDetail = shippingDetail,
      isActive = isActive,
      isDeleted = isDeleted,
      createdDate = createdDate,
      updatedDate = updatedDate,
      category = productCategoryConfig.map { it.toProductCategoryConfigDto() },
      tag = productTagConfig.map { it.toProductTagConfigDto() },
      type = productTypeConfig.map { it.toProductTypeConfigDto() },
      specialTechnic = productSpecialTechnic.map { it.toProductSpecialTechnicDto() },
      gallery = productGallery.sortedByDescending { it.fileType }
        .map { it.toProductGalleryDto() },
    )
  }
}

