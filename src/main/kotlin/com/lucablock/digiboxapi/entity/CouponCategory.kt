package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.couponcategory.dto.CouponCategoryDto
import jakarta.persistence.*
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedDate
import java.util.Date

@Entity
@Table(name = "coupon_category")
data class CouponCategory(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  @Column(name = "name", unique = true)
  var name: String,
  @Column(name = "image_url")
  var imageUrl: String,
  var description: String? = null,
  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,

  @CreatedDate
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val modifiedDate: Date = Date()
) {
  fun couponCategoryDto() = CouponCategoryDto(
    id = id,
    name = name,
    imageUrl = imageUrl,
    description = description,
    isDeleted = isDeleted
  )
}
