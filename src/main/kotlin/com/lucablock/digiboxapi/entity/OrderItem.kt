package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.order.dto.AddressOrderDto
import jakarta.persistence.*
import java.io.Serializable
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "order_item")
data class OrderItem(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  @Column(name = "order_id")
  var orderId: Int,
  @Column(name = "customize_id")
  var customizeId: Long,
  @Column(name = "shipping_cost")
  var shippingCost: Double = 0.0,
  @Column(name = "tracking_number")
  var trackingNumber: String? = null,
  @Column(name = "item_price")
  var itemPrice: Double = 0.0,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var deliveryDate: LocalDateTime? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var periodDate: LocalDateTime? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var orderDate: LocalDateTime? = null,
  @Column(name = "shipping_name")
  var shippingName: String? = null,
  @Column(name = "total_price")
  var totalPrice: Double = 0.0,
  var status: Int,
  @Column(name = "address_name")
  var addressName: String? = null,
  @Column(name = "phone_number")
  var phoneNumber: String? = null,
  var email: String? = null,
  var address: String? = null,
  var zipcode: String? = null,
  var province : String? = null,
  var district: String? = null,
  @Column(name = "sub_district")
  var subDistrict: String? = null,

//  @Column(name = "is_confirm_receipt")
//  var isConfirmReceipt: Boolean = false,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "order_id", insertable = false, updatable = false)
  var order: Order? = null,

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customize_id", insertable = false, updatable = false)
  var customize: Customize,


//  @OneToMany(mappedBy = "orderItemId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
//  var claims: List<OrderClaim?> = ArrayList()

  ) : Serializable
{
  fun toAddressOrderDto(): AddressOrderDto {
    return AddressOrderDto(
      name = addressName,
      phoneNumber = phoneNumber,
      email = email,
      address = address,
      province = province,
      district = district,
      subDistrict = subDistrict,
      zipCode = zipcode,
      taxId = null,
      isTax = false
    )
  }
}