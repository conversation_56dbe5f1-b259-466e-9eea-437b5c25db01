package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.orderClaim.dto.ProblemDto
import jakarta.persistence.*
import java.io.Serializable
import java.util.*
import kotlin.collections.ArrayList

@Entity
@Table(name = "claim_problem")
data class ClaimProblem(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var name: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),

  @OneToMany(mappedBy = "claimProblem", fetch = FetchType.LAZY)
  val cause: List<ClaimCause>? = ArrayList()

//  @OneToOne(fetch = FetchType.LAZY)
//  @JoinColumn(name = "orderItemId", insertable = false, updatable = false)
//  var orderItem: OrderItem? = null,

): Serializable {
  fun toProblemDto(): ProblemDto {
    return ProblemDto(
      id = id,
      name = name,
      createdDate = createdDate,
      modifiedDate = modifiedDate
    )
  }
}
