package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyListDto
import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyWorkingConfigDto
import jakarta.persistence.*

@Entity
@Table(name = "company_working_config")
data class CompanyWorkingConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "company_working_id")
  var companyWorkingId : Long = 0,
  @Column(name = "company_id")
  var companyId : Long = 0,
  @Column(name = "is_active")
  var isActive : Boolean = true,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "company_working_id", insertable = false, updatable = false)
  val companyWorking : CompanyWorking? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "company_id", insertable = false, updatable = false)
  val company: Company? = null,
){
  fun toCompanyWorkingConfigDto() : CompanyWorkingConfigDto{
    return CompanyWorkingConfigDto(
      id = id,
      isActive = isActive,
      companyWorking = companyWorking!!.toCompanyWorkingDto(),
      company = company!!.toCompanyListDto()
    )
  }
}