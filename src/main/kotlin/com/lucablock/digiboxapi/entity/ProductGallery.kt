package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.productgallery.constant.ProductGalleryEnum
import com.lucablock.digiboxapi.productgallery.dto.ProductGalleryDto
import jakarta.persistence.*
import org.hibernate.annotations.GenericGenerator

@Entity
@Table(name = "product_gallery")
data class ProductGallery (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "product_id")
  val productId : Long,
  @Column(name = "image_url")
  val imageUrl : String,
  @Column(name = "file_type")
  var fileType : Int,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_id", insertable = false, updatable = false)
  var product: Product? = null,
){
  fun toProductGalleryDto(): ProductGalleryDto{
    return ProductGalleryDto(
      id = id,
      productId = productId,
      imageUrl = imageUrl,
      fileType = fileType,
      fileTypeEnum = ProductGalleryEnum.fromValue(fileType).description
    )
  }
}