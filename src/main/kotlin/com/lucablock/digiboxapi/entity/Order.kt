package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.io.Serializable
import java.util.*


@Entity
@Table(name = "order")
data class Order(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Int = 0,
  @Column(name = "order_no")
  var orderNo : String,
  @Column(name = "user_id")
  var userId : Long,
  @Column(name = "total_price")
  var totalPrice : Double,
  var vat : Double,
  @Column(name = "vat_cost")
  var vatCost : Double,
  @Column(name = "shipping_cost")
  var shippingCost : Double,
  @Column(name="status")
  var status : Int,
  @Column(name = "receipt_type")
  var receiptType: Int,
  @Column(name = "shipping_type")
  var shippingType: Int,
  @Column(name = "shipping_sub_type")
  var shippingSubType: Int? = null,
  @Column(name = "pickup_name")
  var pickupName: String? = null,
  @Column(name = "pickup_tell")
  var pickupTell: String? = null,
  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,
  @Column(name = "net_price")
  var netPrice: Double = 0.0,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "status", insertable = false, updatable = false)
  val orderStatus: OrderStatus? = null,

  @OneToMany(mappedBy = "order", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  val couponUsageLog: List<CouponUsageLog> = emptyList(),
  ) : Serializable
