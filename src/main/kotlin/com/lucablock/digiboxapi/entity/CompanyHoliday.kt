package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.companyholiday.dto.CompanyHolidayDto
import com.lucablock.digiboxapi.companyholiday.dto.CompanyListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import java.util.*

@Entity
@Table(name = "company_holiday")
data class CompanyHoliday (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long =0,
  var name : String,
  var description : String? = null,

  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var holidayDate: Date = Date(),

  @Column(name = "company_id")
  var companyId : Long ,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "company_id", insertable = false, updatable = false)
  var company: Company? = null,
){
  fun toCompanyHolidayDto() : CompanyHolidayDto{
    return CompanyHolidayDto(
      id = id,
      name = name,
      description = description,
      holidayDate = holidayDate,
      company = company?.let {
        CompanyListDto(
          id = it.id,
          name = it.name
        )
      }
    )
  }
}