package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "customize_coating")
data class CustomizeCoating (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "customize_id")
  var customizeId : Long,
  @Column(name = "model_size_config_coating_id")
  var modelSizeConfigCoatingId : Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customize_id", insertable = false, updatable = false)
  var customize: Customize? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_size_config_coating_id", insertable = false, updatable = false)
  var modelSizeConfigCoating: ModelSizeConfigCoating? = null,
)