package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "sub_district")
data class SubDistrict(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  val subDistrictCode: String,
  val geoId: Int,
  val name: String,
  @Column(name = "district_id")
  val districtId: Long,
  @Column(name = "province_id")
  val provinceId: Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "district_id", insertable = false, updatable = false)
  val district: District? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "province_id", insertable = false, updatable = false)
  val province: Province? = null,

  @OneToMany(mappedBy = "subDistrict", fetch = FetchType.LAZY)
  val zipcode: List<Zipcode>? = null,
)

