package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import java.util.*


@Entity
@Table(name = "payment_transaction")
data class PaymentTransaction(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var orderId: Int,
  var paymentAmount: Double = 0.0,
  var paymentType: String,
  var bankAccountName: String,
  var bankAccountNumber: String,
  var bankName: String,
  var dateTime: String,
  var description: String? = null,
  var slipUrl: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),
)
