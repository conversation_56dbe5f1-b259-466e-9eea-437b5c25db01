package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonBackReference
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import java.util.*

@Entity
@Table(name = "model_reviews_images")
data class ModelReviewImage(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,

  @Column(name = "model_reviews_id")
  val modelReviewId: Long = 0,

  val imageUrl: String = "",

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_reviews_id", insertable = false, updatable = false)
  @JsonBackReference
  val modelReview: ModelReview? = null,

  @CreationTimestamp
  val createdDate: Date = Date(),
)
