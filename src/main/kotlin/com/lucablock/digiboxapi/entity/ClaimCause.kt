package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.orderClaim.dto.CauseDto
import jakarta.persistence.*
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "claim_cause")
data class ClaimCause(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var name: String,
  @Column(name = "claim_problem_id")
  var problemId: Int,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "claim_problem_id", insertable = false, updatable = false)
  val claimProblem: ClaimProblem? = null,

//  @OneToOne(fetch = FetchType.LAZY)
//  @JoinColumn(name = "orderItemId", insertable = false, updatable = false)
//  var orderItem: OrderItem? = null,
): Serializable {
  fun toCauseDto(): CauseDto {
    return CauseDto(
      id = id,
      name = name,
      problem = claimProblem!!.toProblemDto(),
      createdDate = createdDate,
      modifiedDate = modifiedDate
    )
  }
}
