package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.material.dto.MaterialsDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "materials")
data class Materials(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var name: String,

  @Column(name = "image_url")
  var imageUrl: String,

  @Column(name = "is_active")
  var isActive : Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @OneToMany(mappedBy = "materials", fetch = FetchType.LAZY)
  var materialConfig : List<MaterialConfig> = mutableListOf(),
//
//  @ManyToMany
//  @JoinTable(
//    name = "product_category_material",
//    joinColumns = [JoinColumn(name = "material_id")],
//    inverseJoinColumns = [JoinColumn(name = "product_category_id")]
//  )
//  var categories: List<ProductCategory> = mutableListOf()

) {
  fun toMaterialDto(): MaterialsDto {
    return MaterialsDto(
      id = id,
      name = name,
      imageUrl = imageUrl,
      isActive = isActive,
      isDeleted = isDeleted,
      grams = materialConfig.mapNotNull { it.grams?.toGramDto() },
//      categories = categories.map { it.toProductCategoryDto() },
      createdDate = createdDate,
      updatedDate = updatedDate
    )
  }
}

