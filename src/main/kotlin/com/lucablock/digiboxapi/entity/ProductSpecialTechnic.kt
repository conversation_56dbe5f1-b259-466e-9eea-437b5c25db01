package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.productspecialtechnic.dto.ProductSpecialTechnicDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "product_special_technic")
data class ProductSpecialTechnic (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,

  @Column(name = "product_id")
  var productId : Long,

  @Column(name = "special_technic_id")
  var specialTechnicId: Int,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_id", insertable = false, updatable = false)
  var product: Product? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "special_technic_id", insertable = false, updatable = false)
  var specialTechnic: SpecialTechnic? = null,

): Serializable {
  fun toProductSpecialTechnicDto(): ProductSpecialTechnicDto {
    return ProductSpecialTechnicDto(
      id = id,
      productId = productId,
      specialTechnicId = specialTechnicId,
      specialTechnicName = specialTechnic?.name
    )
  }
}