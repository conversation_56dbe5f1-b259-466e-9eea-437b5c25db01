package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.discountCategory.dto.DiscountCategoryDto
import jakarta.persistence.*
import java.io.Serializable

@Entity
@Table(name = "discount_category")
data class DiscountCategory(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  var name: String,
  var categoryImage: String,
) : Serializable {
  fun toDiscountCategoryDto(): DiscountCategoryDto {
    return DiscountCategoryDto(
      id = id,
      name = name,
      categoryImage = categoryImage
    )
  }
}
