package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.productcategoryconfig.dto.ProductCategoryConfigDto
import com.lucablock.digiboxapi.producttagconfig.dto.ProductTagConfigDto
import jakarta.persistence.*
import java.io.Serializable

@Entity
@Table(name = "product_tag_config")
data class ProductTagConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,

  @Column(name = "product_id")
  var productId : Long,

  @Column(name = "product_tag_id")
  var productTagId: Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_id", insertable = false, updatable = false)
  var product: Product? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_tag_id", insertable = false, updatable = false)
  var productTag: ProductTag? = null,
): Serializable {
  fun toProductTagConfigDto(): ProductTagConfigDto {
    return ProductTagConfigDto(
      id = id,
      productId = productId,
      productTagId = productTagId,
      productTagName = productTag?.name
    )
  }
}