package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.coupontype.dto.CouponTypeDto
import jakarta.persistence.*
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedDate
import java.util.Date

@Entity
@Table(name = "coupon_type")
data class CouponType(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  var name: String,
  @Column(name = "is_active")
  var isActive: Boolean = true,
  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,
  @CreatedDate
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val modifiedDate: Date = Date()
) {
  fun couponTypeToDto(): CouponTypeDto {
    return CouponTypeDto(
      id = id,
      name = name,
      isActive = isActive
    )
  }
}
