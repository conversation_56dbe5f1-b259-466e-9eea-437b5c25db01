package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.printing.dto.MachineListDto
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigDto
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigModelSizeDto
import jakarta.persistence.*

@Entity
@Table(name = "printing_config")
data class PrintingConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "model_size_config_id")
  var modelSizeConfigId : Long,
  @Column(name = "printing_id")
  var printingId : Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_size_config_id", insertable = false, updatable = false)
  var modelSizeConfig: ModelSizeConfig? = null,
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "printing_id", insertable = false, updatable = false)
  var printing: Printing? = null,
  @OneToMany(mappedBy = "printingConfig", fetch = FetchType.LAZY)
  var modelSizeConfigDetail: List<ModelSizeConfigDetail> = mutableListOf(),
//  @OneToMany(mappedBy = "printingConfig", fetch = FetchType.LAZY)
//  var modelSizeConfigCoating: List<ModelSizeConfigCoating> = mutableListOf(),
){
  fun toPrintingConfigDto(): PrintingConfigDto {
    return PrintingConfigDto(
      id = id,
      modelSizeConfig = modelSizeConfig?.toModelSizeConfigMaterialDto(),
      printing = printing?.toPrintingListDto(),
      modelSizeConfigDetail = modelSizeConfigDetail.map { it.toModelSizeConfigDetailDto() },
    )
  }
  fun toPrintingModelSizeDto() : PrintingConfigModelSizeDto {
    return PrintingConfigModelSizeDto(
      id = id,
      printing = printing?.toPrintingListDto(),
      modelSizeConfigDetail = modelSizeConfigDetail.map { it.toModelSizeConfigDetailDto() },
//      modelSizeConfigCoating = modelSizeConfigCoating.map { it.toModelSizeConfigCoatingDto() }
    )
  }
}