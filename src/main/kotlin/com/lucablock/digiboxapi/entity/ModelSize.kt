package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.model.dto.ModelSizeDto
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "model_size")
data class ModelSize (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,

  @Column(name = "model_id")
  var modelId : Long,

  @Column(name = "unfolded_size_id")
  var unfoldedSizeId : Long,

  var width: Double,
  var height: Double,
  var length: Double?,

  @Column(name = "unfolded_width")
  var unfoldedWidth : Double,

  @Column(name = "unfolded_height")
  var unfoldedHeight : Double,

  @Column(name = "is_three_d")
  var isThreeD : Boolean = false,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @OneToMany(mappedBy = "modelSize", fetch = FetchType.LAZY)
  var modelSizeConfig: List<ModelSizeConfig> = mutableListOf(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_id", insertable = false, updatable = false)
  var model: Model? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "unfolded_size_id", insertable = false, updatable = false)
  var unfoldedSize: UnfoldedSize? = null,
){
  fun toModelSizeDto(): ModelSizeListDto {
    return ModelSizeListDto(
      id = id,
      width = width,
      height = height,
      length = length,
      unfoldedSizeId = unfoldedSizeId,
      unfoldedSizeName = unfoldedSize?.name?:"",
    )
  }
}