package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonManagedReference
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.persistence.*
import java.io.Serializable
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import kotlin.collections.ArrayList

@Entity
@Table(name = "users")
data class User(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  var firstName: String? = null,
  var lastName: String? = null,
  var phoneNumber: String? = null,
  val phoneVerified: Boolean = false,
  val username: String? = null,
  var email: String? = null,
  var password: String? = null,
  val userTypeId: Long = 0,
  val status: Boolean = true,
  var emailVerified: Boolean = false,
  var imageUrl: String? = null,
  var fullName: String? = null,
  var provider: String? = null,
  var providerId: String? = null,
  @Column(name = "customer_level_id")
  var customerLevelId : Long? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  @Column(name = "customer_level_date")
  var customerLevelDate: LocalDateTime? = null,

  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  @Column(name = "created_date")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  @Column(name = "modified_date")
  val modifiedDate: Date = Date(),
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  @JsonManagedReference
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn( name = "userTypeId", insertable = false, updatable = false, columnDefinition = "int default 1" )
  val userType: UserType? = null,
  @Column(name = "is_deleted")
  var isDeleted: Boolean? = false,

  @OneToMany(mappedBy = "userId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var address: List<Address?> = ArrayList(),

//  @OneToMany(mappedBy = "userId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
//  var customerLevelConfig: List<CustomerLevelConfig?> = mutableListOf(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customer_level_id", insertable = false, updatable = false)
  var customerLevel: CustomerLevel? = null,

//  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
//  val userDiscount: List<UserDiscount>? = mutableListOf()

  @OneToMany(mappedBy = "user", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  val couponUsers: List<CouponUser> = mutableListOf(),

  @OneToMany(mappedBy = "user", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  val couponUsageLogs: List<CouponUsageLog> = mutableListOf()

) : Serializable
