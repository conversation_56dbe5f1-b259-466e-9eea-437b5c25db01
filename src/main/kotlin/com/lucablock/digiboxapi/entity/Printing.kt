package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.printing.dto.MachineListDto
import com.lucablock.digiboxapi.printing.dto.PrintingDto
import com.lucablock.digiboxapi.printing.dto.PrintingListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "printing")
data class Printing (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var name : String,
  @Column(name = "image_url")
  var imageUrl : String,
  @Column(name = "min_print")
  var minPrint : Int,
  @Column(name = "max_print")
  var maxPrint : Int,
  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),
  @Column(name = "is_active")
  var isActive : Boolean = true,

  @OneToMany(mappedBy = "printing", fetch = FetchType.LAZY)
  var machine: List<Machine> = mutableListOf(),

  @OneToMany(mappedBy = "printing", fetch = FetchType.LAZY)
  var printingConfig : List<PrintingConfig> = mutableListOf(),
){
  fun toPrintingDto() : PrintingDto{
    return PrintingDto(
      id = id,
      name = name,
      minPrint = minPrint,
      maxPrint = maxPrint,
      imageUrl = imageUrl,
      isActive = isActive,
      machine = machine.map {
        MachineListDto(
          id = it.id,
          name = it.name
        )
      }
    )
  }
  fun toPrintingListDto() : PrintingListDto {
    return PrintingListDto(
      id = id,
      name = name,
      imageUrl = imageUrl,
    )
  }
}