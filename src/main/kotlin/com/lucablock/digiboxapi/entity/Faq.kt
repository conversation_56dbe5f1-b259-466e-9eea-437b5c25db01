package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonBackReference
import com.lucablock.digiboxapi.faq.dto.CreateFaqUserDto
import com.lucablock.digiboxapi.faq.dto.FaqDto
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp

import java.util.Date

@Entity
@Table(name = "faq")
data class Faq(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,

  var question: String,
  var answer: String,

  val userId: Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "userId", insertable = false, updatable = false)
  @JsonBackReference
  val user: User? = null,

  var isActive: Boolean = false,

  @CreationTimestamp
  val createdDate: Date = Date(),
  @UpdateTimestamp
  val updatedDate: Date = Date()
) {
  fun toDto(): FaqDto {
    return FaqDto(
      id = id,
      question = question,
      answer = answer,
      createdDate = createdDate,
      updatedDate = updatedDate,
      isActive = isActive,
      user=CreateFaqUserDto(
        id = user!!.id,
        name = "${user.firstName} ${user.lastName}"
      )
    )
  }
}
