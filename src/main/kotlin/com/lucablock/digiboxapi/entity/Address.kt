package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.user.dto.AddressDto
import com.lucablock.digiboxapi.user.dto.CreateAddressDto
import jakarta.persistence.*
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "address")
data class Address(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var userId: Int,
  var name: String,
  var phoneNumber: String,
  var address: String,
  var zipCode: String,
  var province: String,
  var district: String,
  var subDistrict: String,
  @Column(name = "is_tax")
  var isTax: Boolean = false,
  var taxPayerType: Int? = null,
  var taxId: String? = null,
  var email: String? = null,
  @Column(name = "is_default")
  var isDefault: Boolean,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date()

) : Serializable
{
  fun toAddressDto(): AddressDto {
    return AddressDto(
      id = id,
      name = name,
      taxId = taxId,
      phoneNumber = phoneNumber,
      email = email,
      address = address,
      zipCode = zipCode,
      province = province,
      district = district,
      subDistrict = subDistrict,
      isTax = isTax,
    )
  }
}
