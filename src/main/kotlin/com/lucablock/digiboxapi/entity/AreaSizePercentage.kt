package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.areasizepercentage.dto.AreaSizePercentageDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.AreaSizePercentageListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "area_size_percentage")
data class AreaSizePercentage(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var name: String,
  var percentage : Int,

  @Column(name = "image_url")
  var imageUrl: String,

  @Column(name = "is_active")
  var isActive: Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @OneToMany(mappedBy = "areaSizePercentage", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var specialTechnicConfig: List<SpecialTechnicConfig> = mutableListOf()
) {
  fun toAreaSizePercentageDto(): AreaSizePercentageDto {
    return AreaSizePercentageDto(
      id = id,
      name = name,
      percentage = percentage,
      imageUrl = imageUrl,
      isActive = isActive,
      isDeleted = isDeleted,
      createdDate = createdDate,
      updatedDate = updatedDate
    )
  }
  fun toAreaSizePercentageListDto(): AreaSizePercentageListDto {
    return AreaSizePercentageListDto(
      id = id,
      name = name,
      percentage = percentage,
      imageUrl = imageUrl,
    )
  }
}
