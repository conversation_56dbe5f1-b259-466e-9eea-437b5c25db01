package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.holidaytype.dto.HolidayTypeDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "holiday_type")
data class HolidayType (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var name : String,
  var color : String,
  @Column(name = "is_active")
  var isActive : Boolean,
  @Column(name = "can_recur")
  var canRecur : Boolean,
  @Column(name = "holiday_category_id")
  var holidayCategoryId : Long,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "holiday_category_id", insertable = false, updatable = false)
  var holidayCategory: HolidayCategory,
)
{
  fun toHolidayTypeDto():HolidayTypeDto{
    return HolidayTypeDto(
      id = id,
      name = name,
      color = color,
      isActive = isActive,
      canRecur = canRecur,
      holidayCategoryId = holidayCategoryId,
      holidayCategoryName = holidayCategory.name,
      createDate = createdDate,
      updateDate = updatedDate
    )
  }
}