package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodDto
import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "product_period")
data class ProductPeriod (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var name : String,
  var price : Double,
  @Column(name = "min_period")
  var minPeriod : Int,
  @Column(name = "max_period")
  var maxPeriod : Int,
  @Column(name = "image_url")
  var imageUrl : String,
  @Column(name = "is_active")
  var isActive : Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,
){
  fun toProductPeriodDto() : ProductPeriodDto{
    return ProductPeriodDto(
      id = id,
      name = name,
      price = price,
      minPeriod = minPeriod,
      maxPeriod = maxPeriod,
      imageUrl = imageUrl,
      isActive = isActive,
    )
  }
}