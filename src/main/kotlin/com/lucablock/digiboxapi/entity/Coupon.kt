package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.coupon.dto.CouponDto
import jakarta.persistence.*
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedDate
import java.util.Date

@Entity
@Table(name = "coupon")
data class Coupon(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  var name: String,
  var code: String,
  var description: String,
  @Column(name = "coupon_category_id")
  var couponCategoryId: Long,
  @Column(name = "coupon_type_id")
  var couponTypeId: Long,
  @Min(0)
  @Max(100)
  var percentage: Int? = null,
  @Column(name = "max_price")
  var maxPrice: Double,
  @Column(name = "min_price")
  var minPrice: Double,
  @Column(name = "customer_level_id")
  var customerLevelId: Long,
  @Column(name = "max_usage")
  var maxUsage: Int,
  var startDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var endDate: Date = Date(),
  @Column(name = "is_active")
  var isActive: Boolean = true,
  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,
  @Column(name = "is_private")
  var isPrivate: Boolean = false,
  @CreatedDate
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val modifiedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "coupon_category_id", insertable = false, updatable = false)
  val couponCategory: CouponCategory? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "coupon_type_id", insertable = false, updatable = false)
  val couponType: CouponType? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customer_level_id", insertable = false, updatable = false)
  val customerLevel: CustomerLevel? = null,

  @OneToMany(mappedBy = "coupon", fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
  val couponProducts: List<CouponProduct> = mutableListOf(),

  @OneToMany(mappedBy = "coupon", fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
  val couponUsers: List<CouponUser> = mutableListOf(),

  @OneToMany(mappedBy = "coupon", fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
  val couponSpecialTechnics: List<CouponSpecialTechnic> = mutableListOf(),

  @OneToMany(mappedBy = "coupon", fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
  val couponUsageLogs: List<CouponUsageLog> = mutableListOf()
) {
  fun toCouponDto(): CouponDto {
    return CouponDto(
      id = id,
      name = name,
      code = code,
      description = description,
      couponCategory = couponCategory?.couponCategoryDto(),
      customerLevel = customerLevel?.toCustomerLevelDto(),
      couponType = couponType?.couponTypeToDto(),
      percentage = percentage,
      maxPrice = maxPrice,
      minPrice = minPrice,
      maxUsage = maxUsage,
      startDate = startDate,
      endDate = endDate,
      isActive = isActive,
      createdDate = createdDate
    )
  }
}
