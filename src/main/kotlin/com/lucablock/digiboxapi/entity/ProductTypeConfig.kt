package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.producttagconfig.dto.ProductTagConfigDto
import com.lucablock.digiboxapi.producttypeconfig.dto.ProductTypeConfigDto
import jakarta.persistence.*
import java.io.Serializable

@Entity
@Table(name = "product_type_config")
data class ProductTypeConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,

  @Column(name = "product_id")
  var productId : Long,

  @Column(name = "product_type_id")
  var productTypeId: Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_id", insertable = false, updatable = false)
  var product: Product? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_type_id", insertable = false, updatable = false)
  var productType: ProductType? = null,

): Serializable {
  fun toProductTypeConfigDto(): ProductTypeConfigDto {
    return ProductTypeConfigDto(
      id = id,
      productId = productId,
      productTypeId = productTypeId,
      productTypeName = productType?.name
    )
  }
}