package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.coating.dto.CoatingListDto
import com.lucablock.digiboxapi.customize.constant.ArtWorkEnum
import com.lucablock.digiboxapi.customize.constant.SampleProductEnum
import com.lucablock.digiboxapi.customize.constant.ShippingTypeEnum
import com.lucablock.digiboxapi.customize.dto.CustomizeCoatingDto
import com.lucablock.digiboxapi.customize.dto.CustomizeDto
import com.lucablock.digiboxapi.customize.dto.CustomizeSpacialTechnicDto
import com.lucablock.digiboxapi.model.dto.ModelCustomDto
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeCustomDto
import com.lucablock.digiboxapi.product.dto.ProductListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "customize")
data class Customize (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "model_id")
  var modelId : Long,
  @Column(name = "model_size_id")
  var modelSizeId : Long,
  @Column(name = "material_config_id")
  var materialConfigId : Long,
  @Column(name = "printing_id")
  var printingId : Long,
  @Column(name = "model_size_config_detail_id")
  var modelSizeConfigDetailId : Long,
  var width : Double ,
  var height : Double,
  var length : Double?,
  @Column(name = "unfolded_size_id")
  var unfoldedSizeId : Long,
  @Column(name = "product_period_id")
  var productPeriodId : Long,
  @Column(name = "sample_product")
  var sampleProduct : Int,
  var shippingType : Int,
  var zipcode: String ,
  @Column(name = "person_pickup_name")
  var personPickupName : String? = null,
  @Column(name = "person_pickup_tell")
  var personPickupTell : String? = null,
  @Column(name = "is_artwork")
  var isArtwork : Boolean = false,
  @Column(name = "die_line")
  var dieLine : String? = null,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var periodDate : Date = Date(),

  @OneToMany(mappedBy = "customize", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var customizeSpecialTechnic: List<CustomizeSpecialTechnic> = mutableListOf(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_id", insertable = false, updatable = false)
  var model: Model,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_size_id", insertable = false, updatable = false)
  var modelSize: ModelSize,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "material_config_id", insertable = false, updatable = false)
  var materialConfig: MaterialConfig,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "printing_id", insertable = false, updatable = false)
  var printing: Printing,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_size_config_detail_id", insertable = false, updatable = false)
  var modelSizeConfigDetail: ModelSizeConfigDetail,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "unfolded_size_id", insertable = false, updatable = false)
  var unfoldedSize: UnfoldedSize? = null,

  @OneToMany(mappedBy = "customize", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var customizeCoating: List<CustomizeCoating> = mutableListOf(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_period_id", insertable = false, updatable = false)
  var productPeriod: ProductPeriod,
){
  fun toCustomizeDto() : CustomizeDto{
    return CustomizeDto(
      id = id,
      model = model?.let {
        ModelCustomDto(
          id = it.id,
          name = it.name,
          imageUrl = it.imageUrl,
          modelCode = it.modelCode,
          description = it.description,
          isActive = it.isActive,
          product = it.product?.let { product ->
            ProductListDto(
              id = product.id,
              name = product.name,
              description = product.description,
            )
          }
        )
      },
      modelSizeId = modelSizeId,
      width = width,
      height = height,
      length = length?: 0.0,
      unfoldedSize = unfoldedSize?.toUnfoldedSizeListDto(),
//      modelSize = modelSize?.let {
//        ModelSizeCustomDto(
//          id = it.id,
//          modelId = it.modelId,
//          unfoldedSizeId = it.unfoldedSizeId,
//          unfoldedSizeName = it.unfoldedSize?.name,
//          width = it.width,
//          height = it.height,
//          length = it.length,
//          isThreeD = it.isThreeD
//        )
//      },
      materialConfig = materialConfig?.toMaterialConfigDto(),
      printing = printing?.toPrintingDto(),
      coating = customizeCoating.map {
        CustomizeCoatingDto(
          id = it.id,
          customizeId = it.customizeId,
          modelSizeConfigCoating = it.modelSizeConfigCoating?.toModelSizeConfigCoatingDto()
        )
      },
      modelSizeConfigDetail = modelSizeConfigDetail?.toModelSizeConfigDetailDto(),
      customizeSpecialTechnic = customizeSpecialTechnic.map {
        CustomizeSpacialTechnicDto(
          id = it.id,
          customizeId = it.customizeId,
          specialTechnicConfig = it.specialTechnicConfig?.toSpecialTechnicConfig()
        )
      },
      productPeriod = productPeriod?.toProductPeriodDto(),
      periodDate = periodDate,
      sampleProductEnum = SampleProductEnum.fromValue(sampleProduct).value,
      sampleProductEnumName = SampleProductEnum.fromValue(sampleProduct).description,
      sampleProductEnumPrice = SampleProductEnum.fromValue(sampleProduct).price,
      shippingTypeEnum = shippingType,
      shippingTypeEnumName = ShippingTypeEnum.fromValue(shippingType).description,
      personPickupName = personPickupName,
      personPickupTell = personPickupTell,
      zipcode = zipcode,
      isArtwork = isArtwork,
      artworkEnum = ArtWorkEnum.fromValue(isArtwork).description,
      dieLine = dieLine
    )
  }
}