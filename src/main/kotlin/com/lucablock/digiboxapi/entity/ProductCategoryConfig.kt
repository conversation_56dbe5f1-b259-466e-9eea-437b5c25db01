package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.product.dto.ProductDto
import com.lucablock.digiboxapi.productcategoryconfig.dto.ProductCategoryConfigDto
import jakarta.persistence.*
import org.springframework.data.jpa.domain.AbstractAuditable_.createdDate
import java.io.Serializable

@Entity
@Table(name = "product_category_config")
data class ProductCategoryConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,

  @Column(name = "product_id")
  var productId : Long,

  @Column(name = "product_category_id")
  var productCategoryId: Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_id", insertable = false, updatable = false)
  var product: Product? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_category_id", insertable = false, updatable = false)
  var productCategory: ProductCategory? = null,

): Serializable {
  fun toProductCategoryConfigDto(): ProductCategoryConfigDto {
    return ProductCategoryConfigDto(
      id = id,
      productId = productId,
      productCategoryId = productCategoryId,
      productCategoryName = productCategory?.name
    )
  }
}