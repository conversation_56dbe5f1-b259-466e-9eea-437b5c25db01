package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.ModelSizeConfigCoating.dto.ModelSizeConfigCoatingDto
import com.lucablock.digiboxapi.coating.dto.CoatingListDto
import jakarta.persistence.*

@Entity
@Table(name = "model_size_config_coating")
data class ModelSizeConfigCoating (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,

  @Column(name = "coating_id")
  var coatingId : Long,

//  @Column(name = "printing_config_id")
//  var printingConfigId : Long,

  @Column(name = "model_size_config_detail_id")
  var modelSizeConfigDetailId : Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "coating_id", insertable = false, updatable = false)
  var coating: Coating,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_size_config_detail_id", insertable = false, updatable = false)
  var modelSizeConfigDetail: ModelSizeConfigDetail? = null,
//  @ManyToOne(fetch = FetchType.LAZY)
//  @JoinColumn(name = "printing_config_id", insertable = false, updatable = false)
//  var printingConfig: PrintingConfig? = null,
){
  fun toModelSizeConfigCoatingDto() : ModelSizeConfigCoatingDto{
    return ModelSizeConfigCoatingDto(
      id = id,
      coating = coating?.let {
        CoatingListDto(
          id = it.id,
          name = it.name,
          price = it.price,
          imageUrl = it.imageUrl,
          isActive = it.isActive
        )
      }
    )
  }
}