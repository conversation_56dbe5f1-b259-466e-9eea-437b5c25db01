package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "zipcode")
data class Zipcode (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  val zipcode: String,
  val subDistrictCode: String,
  @Column(name = "province_id")
  val provinceId : Long,
  @Column(name = "district_id")
  val districtId : Long,
  @Column(name = "sub_district_id")
  val subDistrictId : Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "province_id", insertable = false, updatable = false)
  val province: Province? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "district_id", insertable = false, updatable = false)
  val district: District? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "sub_district_id", insertable = false, updatable = false)
  val subDistrict: SubDistrict? = null,
  )