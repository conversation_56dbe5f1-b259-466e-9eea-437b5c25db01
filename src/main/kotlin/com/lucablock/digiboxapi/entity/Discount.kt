package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.discount.dto.DiscountDto
import jakarta.persistence.*
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "discount")
data class Discount(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Int = 0,
  var title: String,
  @Column(name = "discount_code")
  var discountCode: String,
  var percentage: Int,
  var maxDiscount: Int,
  var minPrice: Int,
  var maxUsage: Int,
  var condition: String,
  var description: String,
  var discountImage: String,
  @Column(name = "discount_category_id")
  var discountCategoryId: Long,
  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,
  @Column(name = "is_active")
  var isActive: Boolean = true,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var startDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var endDate: Date,

  @OneToMany(mappedBy = "discountId", fetch = FetchType.LAZY)
  val orderDiscount: List<OrderDiscount> = mutableListOf(),

  @ManyToOne
  @JoinColumn(name = "discount_category_id", insertable = false, updatable = false)
  val category: DiscountCategory? = null

) : Serializable {

  fun updateIsActive() {
    if (Date().after(endDate)) {
      isActive = false
    }
  }

  fun toDiscountDto(): DiscountDto {
    return DiscountDto(
      id = id,
      title = title,
      discountCode = discountCode,
      percentage = percentage,
      maxDiscount = maxDiscount,
      minPrice = minPrice,
      maxUsage = maxUsage,
      condition = condition,
      description = description,
//      discountCategory = category!!.toDiscountCategoryDto(),
      discountImage = discountImage,
      startDate = startDate,
      endDate = endDate,
      isActive = isActive,
    )
  }
}
