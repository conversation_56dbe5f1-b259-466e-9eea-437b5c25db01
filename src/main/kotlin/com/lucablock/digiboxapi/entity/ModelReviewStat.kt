package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonBackReference
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.Table

@Entity
@Table(name = "model_reviews_stat")
data class ModelReviewStat(
  @Id
  @Column(name = "model_id")
  val id: Long = 0,
  val countVote: Int = 0,
  val avg: Double = 0.0,

  @OneToOne
  @JoinColumn(name = "model_id")
  @JsonBackReference
  val model: Model? = null
)
