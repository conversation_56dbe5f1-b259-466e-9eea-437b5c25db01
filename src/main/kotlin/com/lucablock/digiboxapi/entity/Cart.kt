package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import java.io.Serializable
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "cart")
data class Cart(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  @Column(name = "user_id")
  var userId: Int? = null,
  var uuid : String? = null,
  @Column(name = "customize_id")
  var customizeId : Long? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var expireDate : LocalDateTime? = null,

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customize_id", insertable = false, updatable = false)
  val customize: Customize? = null,

//  var itemId: Int,
//  @OneToOne(fetch = FetchType.LAZY)
//  @JoinColumn(name = "itemId", insertable = false, updatable = false)
//  var item: Item? = null

) : Serializable
