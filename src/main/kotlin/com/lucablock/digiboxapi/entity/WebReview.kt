package com.lucablock.digiboxapi.entity

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import java.util.*

@Entity
@Table(name = "web_review")
data class WebReview(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,

  val userId: Long,

  val rating: Int,
  val comment: String? = null,
  @CreationTimestamp
  val createdDate: Date = Date()
)
