package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "order_claim")
data class OrderClaim(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var orderId: Int,
  var orderItemId: Int,
  var amount: Int,
  var description: String? = null,
  var status: Int,
  var shipping: Int? = null,
  var shippingCost: Double = 0.0,
  var trackingNumber: String? = null,
  var recipientName: String? = null,
  var phoneNumber: String? = null,
  var address: String? = null,
  var district: String? = null,
  var subDistrict: String? = null,
  var province: String? = null,
  var zipCode: String? = null,
  var email: String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var deliveryDate: Date? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var receivedDate: Date? = null,
  var shippingName: String? = null,
  var receivedDescription: String? = null,
  @Column(name = "is_confirm_receipt")
  var isConfirmReceipt: Boolean = false,
  @Column(name = "is_confirm")
  var isConfirm: Boolean = false,
  @Column(name = "problem_id")
  var problemId: Int? = null,
  @Column(name = "cause_id")
  var causeId: Int? = null,
  var auditDetail: String? = null,
  var auditDescription: String? = null,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "orderId", insertable = false, updatable = false)
  var order: Order? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "problem_id", insertable = false, updatable = false)
  var problem: ClaimProblem? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cause_id", insertable = false, updatable = false)
  var cause: ClaimCause? = null,

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "orderItemId", insertable = false, updatable = false)
  var orderItem: OrderItem? = null,

  @OneToMany(mappedBy = "orderClaimId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  @OrderBy("id ASC")
  var files: List<FileClaim?> = ArrayList(),

): Serializable
