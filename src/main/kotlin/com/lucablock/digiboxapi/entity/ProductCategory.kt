package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.productCategory.dto.ProductCategoryDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "product_category")
data class ProductCategory(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  var name: String,

  @Column(name = "image_url")
  var imageUrl: String,

  @Column(name = "is_active")
  var isActive : Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date()
) {
  fun toProductCategoryDto(): ProductCategoryDto {
    return ProductCategoryDto(
      id = id,
      name = name,
      imageUrl = imageUrl,
      isActive = isActive,
      isDeleted = isDeleted,
      createdDate = createdDate,
      updatedDate = updatedDate
    )
  }
}
