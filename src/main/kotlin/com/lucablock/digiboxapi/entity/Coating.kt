package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.coating.dto.CoatingDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "coating")
data class Coating(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var name: String,
  var description : String?,
  var price: Double,
  var imageUrl: String,

  @Column(name = "is_active")
  var isActive: Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

//  @ManyToMany
//  @JoinTable(
//    name = "product_category_coating",
//    joinColumns = [JoinColumn(name = "coating_id")],
//    inverseJoinColumns = [JoinColumn(name = "product_category_id")]
//  )
//  var categories: List<ProductCategory> = mutableListOf()
) {
  fun toCoatingDto(): CoatingDto {
    return CoatingDto(
      id = id,
      name = name,
      description = description,
      price = price,
      imageUrl = imageUrl,
      isActive = isActive,
      isDeleted = isDeleted,
//      categories = categories.map { it.toProductCategoryDto() },
      createdDate = createdDate,
      updatedDate = updatedDate
    )
  }
}
