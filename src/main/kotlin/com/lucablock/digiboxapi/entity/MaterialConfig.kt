package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.materialconfig.dto.GramListDto
import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigDto
import com.lucablock.digiboxapi.materialconfig.dto.MaterialConfigListDto
import com.lucablock.digiboxapi.materialconfig.dto.MaterialListDto
import jakarta.persistence.*

@Entity
@Table(name = "materialConfig")
data class MaterialConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "materials_id")
  var materialsId : Int,
  @Column(name = "gram_id")
  var gramId : Long,

  @Column(name = "is_active")
  var isActive : Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "materials_id", insertable = false, updatable = false)
  val materials : Materials,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "gram_id", insertable = false, updatable = false)
  val grams: Gram,
){
  fun toMaterialConfigDto(): MaterialConfigDto {
    return MaterialConfigDto(
      id = id,
      isActive = isActive,
      isDeleted = isDeleted,
      materials = materials.let {
        MaterialListDto(
          id = it.id,
          name = it.name,
          imageUrl = it.imageUrl
        )
      },
      grams = grams.let {
        GramListDto(
          id = it.id,
          gsm = it.gsm,
          mm = it.mm
        )
      }
    )
  }
  fun toMaterialConfigListDto(): MaterialConfigListDto {
    return MaterialConfigListDto(
      id = id,
      materialsId = materialsId,
      materialsName = materials.name?:"",
      gramsId = gramId,
      gsm = grams.gsm?:0,
      mm = grams.mm?:0.0
    )
  }
}