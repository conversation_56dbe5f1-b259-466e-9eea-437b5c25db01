package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "machine")
data class Machine (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var name: String,
  @Column(name = "printing_id")
  var printingId : Long,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "printing_id", insertable = false, updatable = false,)
  var printing: Printing? = null,
)