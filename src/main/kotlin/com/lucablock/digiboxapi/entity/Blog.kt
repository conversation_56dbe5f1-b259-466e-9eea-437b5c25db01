package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "blog")
data class Blog(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Int = 0,
  @NotBlank(message = "Title must not be blank")
  var title: String,
  var content: String,
  var keyword: String,
  var thumbnailUrl: String,
  @Column(name = "is_published")
  var isPublished: Boolean? = false,
  @Column(name = "is_deleted")
  var isDeleted: Boolean? = false,
  var blogTypeId: Int,
  var description: String,
  var urlSlug: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val updatedDate: Date = Date(),
  var author: String,

  @ManyToMany
  @JoinTable(
    name = "blog_category_mapping",
    joinColumns = [JoinColumn(name = "blog_id")],
    inverseJoinColumns = [JoinColumn(name = "blog_category_id")]
  )
  val categories: Set<BlogCategory>? = emptySet(),

  @ManyToOne
  @JoinColumn(name = "blogTypeId", insertable = false, updatable = false)
  val blogType: BlogType? = null,
) : Serializable