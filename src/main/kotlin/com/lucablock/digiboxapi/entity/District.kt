package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "district")
data class District(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  val districtCode: String,
  val geoId: Int,
  val name: String,
  @Column(name = "province_id")
  val provinceId: Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "province_id", insertable = false, updatable = false)
  val province: Province? = null,

  @OneToMany(mappedBy = "district", fetch = FetchType.LAZY)
  val subDistrict: List<SubDistrict>? = null,

  @OneToMany(mappedBy = "district", fetch = FetchType.LAZY)
  val zipcode: List<Zipcode>? = null,
)