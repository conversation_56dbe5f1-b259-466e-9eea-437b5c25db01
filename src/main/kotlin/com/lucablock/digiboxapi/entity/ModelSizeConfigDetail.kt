package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailDto
import jakarta.persistence.*
import java.util.ArrayList

@Entity
@Table(name = "model_size_config_detail")
data class ModelSizeConfigDetail (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  var amount : Int,
  var price : Double,
  var period : Int,
  @Column(name = "printing_config_id")
  var printingConfigId : Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "printing_config_id", insertable = false, updatable = false)
  var printingConfig: PrintingConfig? = null,

  @OneToMany(mappedBy = "modelSizeConfigDetail", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var modelSizeConfigCoating: List<ModelSizeConfigCoating> = mutableListOf(),
){
  fun toModelSizeConfigDetailDto() : ModelSizeConfigDetailDto{
    return ModelSizeConfigDetailDto(
      id = id,
      amount = amount,
      price = price,
      period = period,
      modelSizeConfigCoating = modelSizeConfigCoating.map { it.toModelSizeConfigCoatingDto() },
//      printingConfigId = printingConfigId
    )
  }
}