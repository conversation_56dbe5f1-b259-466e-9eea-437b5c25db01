package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "product_category_special_technic")
data class ProductCategorySpecialTechnic(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,

  @Column(name = "special_technic_id")
  var specialTechnicId: Long,

  @Column(name = "product_category_id")
  var productCategoryId: Long,
)
