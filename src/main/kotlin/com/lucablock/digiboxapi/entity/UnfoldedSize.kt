package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeDto
import com.lucablock.digiboxapi.unfoldedsize.dto.UnfoldedSizeListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "unfolded_size")
data class UnfoldedSize(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  var name: String,
  var width: Double,
  var height: Double,
  @Column(name = "over_size_limit")
  var overSizeLimit : Double,
  @Column(name = "is_active")
  var isActive : Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),
){
  fun toUnfoldedSizeDto():UnfoldedSizeDto{
    return UnfoldedSizeDto(
      id = id,
      name = name,
      width = width,
      height = height,
      overSizeLimit = overSizeLimit,
      isActive = isActive,
      isDeleted = isDeleted,
      createdDate = createdDate,
      updatedDate = updatedDate,
    )
  }
  fun toUnfoldedSizeListDto(): UnfoldedSizeListDto {
    return UnfoldedSizeListDto(
      id = id,
      name = name,
    )
  }
}