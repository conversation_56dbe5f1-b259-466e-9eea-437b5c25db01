package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.customerlevel.dto.CustomerLevelDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "customer_level")
data class CustomerLevel (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var name : String,
  var description : String? = null,
  @Column(name = "image_url")
  var imageUrl : String,

  @Column(name = "is_active")
  var isActive : Boolean = true,

  @Column(name = "min_purchase_amount")
  var minPurchaseAmount : Double? = null,

  @Column(name = "min_order_count")
  var minOrderCount : Int? = null,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),
)
{
  fun toCustomerLevelDto():CustomerLevelDto{
    return CustomerLevelDto(
      id = id,
      name = name,
      description = description,
      imageUrl = imageUrl,
      isActive = isActive,
      minPurchaseAmount = minPurchaseAmount,
      minOrderCount = minOrderCount,
    )
  }
}