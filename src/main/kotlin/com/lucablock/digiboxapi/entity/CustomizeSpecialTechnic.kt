package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "customize_special_technic")
data class CustomizeSpecialTechnic (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  @Column(name = "customize_id")
  var customizeId : Long,
  @Column(name = "special_technic_config_id")
  var specialTechnicConfigId : Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customize_id", insertable = false, updatable = false)
  val customize: Customize? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "special_technic_config_id", insertable = false, updatable = false)
  val specialTechnicConfig: SpecialTechnicConfig,
)