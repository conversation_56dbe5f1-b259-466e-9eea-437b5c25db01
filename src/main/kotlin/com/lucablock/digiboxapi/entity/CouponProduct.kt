package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "coupon_product")
data class CouponProduct(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  @Column(name = "coupon_id", nullable = false)
  val couponId: Long,
  @Column(name = "product_id", nullable = false)
  val productId: Long,

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "coupon_id", insertable = false, updatable = false)
  val coupon: Coupon? = null,

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "product_id", insertable = false, updatable = false)
  val product: Product? = null
)
