package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.ModelSizeConfigCoating.dto.ModelSizeConfigCoatingDto
import com.lucablock.digiboxapi.model.dto.ModelDto
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeNewDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigDto
import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailDto
import com.lucablock.digiboxapi.product.dto.ProductListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.io.Serializable
import java.util.*


@Entity
@Table(name = "model")
data class Model(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  var id: Long = 0,

  @Column(unique = true)
  var name: String,

  @Column(name = "image_url")
  var imageUrl: String = "",

  @Column(name = "model_code")
  var modelCode: String,

  var description: String? = null,

  @Column(name = "is_active")
  var isActive: Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,

  @Column(name = "product_id")
  var productId: Long,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_id", insertable = false, updatable = false)
  var product: Product,

  @OneToMany(mappedBy = "model", fetch = FetchType.LAZY)
  var modelSize: List<ModelSize> = mutableListOf(),

//  @OneToMany(fetch = FetchType.LAZY)
//  @JoinTable(
//    name = "model_price",
//    joinColumns = [JoinColumn(name = "model_id")],
//    inverseJoinColumns = [JoinColumn(name = "id")]
//  )
//  @OrderBy("id ASC")
//  var prices: List<ModelPrice> = mutableListOf(),
//
//  @OneToMany(fetch = FetchType.LAZY)
//  @JoinTable(
//    name = "model_thumbnail",
//    joinColumns = [JoinColumn(name = "model_id")],
//    inverseJoinColumns = [JoinColumn(name = "id")]
//  )
//  @OrderBy("id ASC")
//  var thumbnails: List<ModelThumbnail> = mutableListOf(),
  @OneToOne(mappedBy = "model", cascade = [CascadeType.ALL])
  val modelReviewStat: ModelReviewStat? = null,

  ) : Serializable {
  fun toModelDto(): ModelDto {
    return ModelDto(
      id = id,
      name = name,
      imageUrl = imageUrl,
      modelCode = modelCode,
      description = description,
      isActive = isActive,
      product = product?.let {
        ProductListDto(
          id = it.id,
          name = it.name,
          description = it.description
        )
      },
      createdDate = createdDate,
      updatedDate = updatedDate,
      modelSize = modelSize.map { size ->
        ModelSizeNewDto(
          id = size.id,
          modelId = id,
          unfoldedSizeId = size.unfoldedSizeId,
          unfoldedSizeName = size.unfoldedSize?.name,
          unfoldedWidth = size.unfoldedWidth,
          unfoldedHeight = size.unfoldedHeight,
          width = size.width,
          height = size.height,
          length = size.length,
          isThreeD = size.isThreeD,
          modelSizeConfig = mutableListOf()
        )
          },
      stat = modelReviewStat
        )
      }
  }
