package com.lucablock.digiboxapi.entity

import jakarta.persistence.*
import java.io.Serializable

@Entity
@Table(name = "user_type")
data class UserType(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,
  val name: String,
  val isAdmin: <PERSON><PERSON><PERSON>,
  val isAnonymous: <PERSON><PERSON><PERSON>,
  val isUser: <PERSON><PERSON><PERSON>,
  val isSuperAdmin: <PERSON><PERSON>an,
) : Serializable {
  constructor() : this(0, "", false, false, false, false) {

  }
}
