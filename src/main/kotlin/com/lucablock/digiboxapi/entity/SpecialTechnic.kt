package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.specialTechnic.dto.SpecialTechnicConfigListDto
import com.lucablock.digiboxapi.specialTechnic.dto.SpecialTechnicDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "special_technic")
data class SpecialTechnic(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  var name: String,
  var description : String? = null,
  var imageUrl: String,

  @Column(name = "is_active")
  var isActive: Boolean = true,

  @Column(name = "is_deleted")
  var isDeleted: Boolean = false,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
  @JoinTable(
    name = "product_category_special_technic",
    joinColumns = [JoinColumn(name = "special_technic_id")],
    inverseJoinColumns = [JoinColumn(name = "product_category_id")]
  )
  var categories: List<ProductCategory> = mutableListOf(),

  @OneToMany(mappedBy = "specialTechnic", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var specialTechnicConfig: List<SpecialTechnicConfig> = mutableListOf(),

  @OneToMany(mappedBy = "specialTechnic", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
  var couponSpecialTechnic: List<CouponSpecialTechnic> = mutableListOf(),
) {
  fun toSpecialTechnicDto(): SpecialTechnicDto {
    return SpecialTechnicDto(
      id = id,
      name = name,
      description = description,
      imageUrl = imageUrl,
      isActive = isActive,
      isDeleted = isDeleted,
//      categories = categories.map { it.toProductCategoryDto() },
      createdDate = createdDate,
      updatedDate = updatedDate,
      specialTechnicConfig = specialTechnicConfig.filter { !it.isDeleted }.map {
        SpecialTechnicConfigListDto(
          id = it.id,
          price = it.price,
          period = it.period,
          specialTechnicId = it.specialTechnicId,
          areaSizePercentageId = it.areaSizePercentageId,
          areaSizePercentage = it.areaSizePercentage?.percentage
        )
      }
    )
  }
  fun toSpecialTechnicListDto() : SpecialTechnicListDto{
    return SpecialTechnicListDto(
      id = id,
      name = name,
      description = description,
      imageUrl = imageUrl,
    )
  }
}
