package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigListDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigMaterialDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigPrintingDto
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigDto
import jakarta.persistence.*

@Entity
@Table(name = "model_size_config")
data class ModelSizeConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,

  @Column(name = "model_size_id")
  var modelSizeId : Long,

  @Column(name = "material_config_id")
  var materialConfigId : Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "model_size_id", insertable = false, updatable = false)
  var modelSize: ModelSize? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "material_config_id", insertable = false, updatable = false)
  var materialConfig: MaterialConfig? = null,

  @OneToMany(mappedBy = "modelSizeConfig", fetch = FetchType.LAZY)
  var printingConfig: List<PrintingConfig> = mutableListOf(),
){
  fun toModelSizeConfigDto() : ModelSizeConfigDto{
    return ModelSizeConfigDto(
      id = id,
      modelSizeId = modelSizeId,
      materialConfigDto = materialConfig?.toMaterialConfigDto() ,
      printingConfig = printingConfig.map { it.toPrintingConfigDto() }
    )
  }
  fun toModelSizeConfigListDto() : ModelSizeConfigListDto {
    return ModelSizeConfigListDto(
      id = id,
      modelSizeId = modelSizeId,
      materialConfigId = materialConfigId,
    )
  }
  fun toModelSizeConfigMaterialDto() : ModelSizeConfigMaterialDto {
    return ModelSizeConfigMaterialDto(
      id = id,
      modelSize = modelSize?.toModelSizeDto(),
      materialConfig = materialConfig?.toMaterialConfigListDto(),
    )
  }
  fun toModelSizeConfigPrintingDto() : ModelSizeConfigPrintingDto {
    return ModelSizeConfigPrintingDto(
      id = id,
      modelSize = modelSize?.toModelSizeDto(),
      materialConfig = materialConfig?.toMaterialConfigListDto(),
      printingConfig = printingConfig.map { it.toPrintingModelSizeDto() }
    )
  }
}