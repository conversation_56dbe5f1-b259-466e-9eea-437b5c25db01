package com.lucablock.digiboxapi.entity

enum class StatusEnum(val key: Int, val value: String) {
  WAITING_FOR_PO(1, "รอเสนอราคา"),
  WAITING_FOR_PAYMENT(2, "รอชำระเงิน"),
  VERIFY_PAYMENT(3, "ตรวจสอบการชำระเงิน"),
  PROCEED_WITH_PRODUCTION(4, "ดำเนินการผลิต"),
  COMPLETE(5, "สำเร็จ"),
  CANCEL(6, "ยกเลิก");

  companion object {
    fun fromKey(key: Int): String? {
      return entries.find { it.key == key }?.value
    }
  }
}