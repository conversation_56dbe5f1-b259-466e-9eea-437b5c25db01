package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.specialtechnicconfig.dto.AreaSizePercentageListDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicListDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "special_technic_config")
data class SpecialTechnicConfig (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var price : Double,
  var period : Int,

  @Column(name = "special_technic_id")
  var specialTechnicId : Int,

  @Column(name = "area_size_percentage_id")
  var areaSizePercentageId : Long,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "special_technic_id", insertable = false, updatable = false)
  val specialTechnic: SpecialTechnic,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "area_size_percentage_id", insertable = false, updatable = false)
  val areaSizePercentage: AreaSizePercentage,
){
  fun toSpecialTechnicConfig(): SpecialTechnicConfigDto {
    return SpecialTechnicConfigDto(
      id = id,
      price = price,
      specialTechnic = specialTechnic!!.let {
        SpecialTechnicListDto(
          id = it.id,
          name = it.name,
          description = it.description,
          imageUrl = it.imageUrl
        )
      },
      areaSize = areaSizePercentage!!.let {
        AreaSizePercentageListDto(
          id = it.id,
          name = it.name,
          percentage = it.percentage,
          imageUrl = it.imageUrl
        )
      }
    )
  }
}