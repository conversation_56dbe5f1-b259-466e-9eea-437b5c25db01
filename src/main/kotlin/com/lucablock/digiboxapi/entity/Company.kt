package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.company.dto.CompanyDto
import com.lucablock.digiboxapi.companyworkingconfig.dto.CompanyListDto
import jakarta.persistence.*

@Entity
@Table(name = "company")
data class Company (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var name : String,
  var imageUrl : String,
  @Column(name = "tax_id")
  var taxId : String?,
  var address : String,
  @Column(name = "zip_code")
  var zipCode: String,
  var province : String,
  var district : String,
  @Column(name = "sub_district")
  var subDistrict : String,
  @Column(name = "phone_number")
  var phoneNumber : String,
  var email : String?,
  @Column(name = "line_id")
  var lineId : String?,
  @Column(name = "line_image")
  var lineImage : String?,
  @Column(name = "website_url")
  var websiteUrl : String?,
  @Column(name = "map_url")
  var mapUrl : String?,
  @Column(name = "is_deleted")
  var isDeleted : Boolean = false,
){
  fun toCompanyDto() : CompanyDto{
    return CompanyDto(
      id = id,
      name = name,
      imageUrl = imageUrl,
      taxId = taxId,
      address = address,
      zipCode = zipCode,
      province = province,
      district = district,
      subDistrict = subDistrict,
      phoneNumber = phoneNumber,
      email = email,
      lineId = lineId,
      lineImage = lineImage,
      websiteUrl = websiteUrl,
      mapUrl = mapUrl,
    )
  }
  fun toCompanyListDto(): CompanyListDto {
    return CompanyListDto(
      id = id,
      name = name
    )
  }
}