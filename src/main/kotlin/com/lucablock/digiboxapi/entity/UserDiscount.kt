package com.lucablock.digiboxapi.entity

import jakarta.persistence.*
import java.io.Serializable

@Entity
@Table(name = "user_discount")
data class UserDiscount(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Int = 0,
  @Column(name = "user_id")
  val userId: Int = 0,
  @Column(name = "discount_id")
  val discountId: Int = 0,
  @Column(name = "is_active")
  var isActive: Boolean = true,

  ) : Serializable
