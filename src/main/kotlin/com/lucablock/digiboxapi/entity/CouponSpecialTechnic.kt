package com.lucablock.digiboxapi.entity

import jakarta.persistence.*

@Entity
@Table(name = "coupon_special_technic")
data class CouponSpecialTechnic(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  @Column(name = "coupon_id", nullable = false)
  val couponId: Long,
  @Column(name = "special_technic_id", nullable = false)
  val specialTechnicId: Long,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "coupon_id", insertable = false, updatable = false)
  val coupon: Coupon? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "special_technic_id", insertable = false, updatable = false)
  val specialTechnic: SpecialTechnic? = null
)
