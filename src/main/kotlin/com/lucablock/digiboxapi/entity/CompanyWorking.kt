package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.companyworking.dto.CompanyWorkingDto
import jakarta.persistence.*

@Entity
@Table(name = "company_working")
data class CompanyWorking (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var day : String,
  var sort : Int,

){
  fun toCompanyWorkingDto(): CompanyWorkingDto {
    return CompanyWorkingDto(
      id = id,
      day = day,
      sort = sort
    )
  }
}