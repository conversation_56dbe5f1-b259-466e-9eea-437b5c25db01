package com.lucablock.digiboxapi.entity

import jakarta.persistence.*
import java.io.Serializable

@Entity
@Table(name = "order_discount")
data class OrderDiscount(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id: Long = 0,
  val orderId: Int,
  val discountId: Int,
  val totalDiscount: Double,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "discountId", insertable = false, updatable = false)
  val discount: Discount? = null,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "discountId", insertable = false, updatable = false)
  val order: Order? = null,
) : Serializable
