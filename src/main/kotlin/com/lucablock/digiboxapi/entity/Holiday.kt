package com.lucablock.digiboxapi.entity

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.holiday.dto.HolidayDto
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "holiday")
data class Holiday (
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  val id : Long = 0,
  var name : String,
  var description : String? = null,

  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
  var holidayDate: LocalDateTime,

  @Column(name = "holiday_type_id")
  var holidayTypeId : Long,
  @Column(name = "is_active")
  var isActive : Boolean,
  @Column(name = "is_recurring")
  var isRecurring : Boolean,
  @Column(name = "company_id")
  var companyId : Long,

  @CreationTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date() ,

  @UpdateTimestamp
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var updatedDate: Date = Date(),

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "holiday_type_id", insertable = false, updatable = false)
  var holidayType: HolidayType,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "company_id", insertable = false, updatable = false)
  var company: Company? = null,
)
{
  fun toHolidayDto():HolidayDto{
    return HolidayDto(
      id = id,
      name = name,
      description = description,
      holidayDate = holidayDate,
      holidayTypeId = holidayTypeId,
      holidayTypeName = holidayType.name,
      holidayTypeColor = holidayType.color,
      isRecurring = isRecurring,
      isActive = isActive
    )
  }
}