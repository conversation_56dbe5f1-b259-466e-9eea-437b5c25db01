package com.lucablock.digiboxapi.entity

import jakarta.persistence.*
import java.io.Serializable

@Entity
@Table(name = "item_special_technic")
data class ItemSpecialTechnic(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Int = 0,
  @Column(name = "item_id")
  var itemId: Int,
  @Column(name = "specialTechnic_id")
  var specialTechnicId: Int,
  var width: Int,
  var height: Int,

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "specialTechnic_id", insertable = false, updatable = false)
  var specialTechnic: SpecialTechnic? = null,

) : Serializable
