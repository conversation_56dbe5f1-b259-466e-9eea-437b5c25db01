package com.lucablock.digiboxapi.entity

import com.lucablock.digiboxapi.model.dto.ModelPriceDto
import jakarta.persistence.*

@Entity
@Table(name = "model_price")
data class ModelPrice(
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  val id: Long = 0,

  @Column(name = "model_id")
  var modelId: Long,
  var amount: Long,
  var price: Double,
  var percentage: Long,
) {
  fun toModelPriceDto(): ModelPriceDto {
    return ModelPriceDto(
      id = id,
      modelId = modelId,
      amount = amount,
      price = price,
      percentage = percentage,
    )
  }
}
