package com.lucablock.digiboxapi.cart.service

import com.lucablock.digiboxapi.cart.dto.AddCustomCartDto
import com.lucablock.digiboxapi.cart.dto.CartListDto
import com.lucablock.digiboxapi.cart.dto.GetUserCartDto
import com.lucablock.digiboxapi.cart.request.AddToCartRequest
import com.lucablock.digiboxapi.cart.request.UpdateCartRequest
import com.lucablock.digiboxapi.cart.request.UpdateMyCartRequest
import com.lucablock.digiboxapi.user.dto.UserPrincipal

interface CartService {
  fun addToCart(userId: Int, addToCartRequest: AddToCartRequest)
  fun findAllByUserId(userId: Int): List<GetUserCartDto>?
  fun findItemById(id: Int): GetUserCartDto?
  fun updateItemCart(id: Int, updateCartRequest: UpdateCartRequest)
  fun deleteCartItem(id: Int)

  fun addCustomToCart(
    userPrincipal: UserPrincipal?,
    uuid : String?,
    customizeId : Long
    ): AddCustomCartDto
  fun getMyCart(userPrincipal: UserPrincipal?, uuid : String?): List<CartListDto>
  fun updateMyCart(cartId : Int, updateCartRequest : UpdateMyCartRequest): CartListDto
  fun getMyCartById(cartId: Int):CartListDto
  fun getMyCartByListId(cartId: List<Int>):List<CartListDto>
  fun deleteCartById(cartId: Int):Boolean
}