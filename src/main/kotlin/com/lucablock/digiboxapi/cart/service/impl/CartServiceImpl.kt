package com.lucablock.digiboxapi.cart.service.impl

import com.lucablock.digiboxapi.cart.dto.AddCustomCartDto
import com.lucablock.digiboxapi.cart.dto.CartListDto
import com.lucablock.digiboxapi.cart.dto.GetUserCartDto
import com.lucablock.digiboxapi.cart.repository.CartRepository
import com.lucablock.digiboxapi.cart.request.AddToCartRequest
import com.lucablock.digiboxapi.cart.request.UpdateCartRequest
import com.lucablock.digiboxapi.cart.request.UpdateMyCartRequest
import com.lucablock.digiboxapi.cart.service.CartService
import com.lucablock.digiboxapi.customize.repository.CustomizeRepository
import com.lucablock.digiboxapi.entity.Cart
import com.lucablock.digiboxapi.entity.Item
import com.lucablock.digiboxapi.entity.ItemSpecialTechnic
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.item.repository.ItemRepository
import com.lucablock.digiboxapi.item.repository.ItemSpecialTechnicRepository
import com.lucablock.digiboxapi.modelsizeconfigdetail.repository.ModelSizeConfigDetailRepository
import com.lucablock.digiboxapi.productperiod.repository.ProductPeriodRepository
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.text.DecimalFormat
import java.time.LocalDateTime
import java.util.*

@Service
class CartServiceImpl @Autowired constructor(
  private val cartRepository: CartRepository,
  private val itemRepository: ItemRepository,
  private val itemSpecialTechnicRepository: ItemSpecialTechnicRepository,
  private val customizeRepository: CustomizeRepository,
  private val modelSizeConfigDetailRepository: ModelSizeConfigDetailRepository,
  private val productPeriodRepository: ProductPeriodRepository
) : CartService {

  @Transactional
  override fun addToCart(userId: Int, addToCartRequest: AddToCartRequest) {
    val item = Item(
      modelId = addToCartRequest.modelId,
      width = addToCartRequest.width,
      length = addToCartRequest.length,
      height = addToCartRequest.height,
      materialId = addToCartRequest.materialId,
      amount = addToCartRequest.amount,
      printing = addToCartRequest.printingId,
      printingPrice = addToCartRequest.printingPrice,
      coatingId = addToCartRequest.coatingId,
      isArtwork = addToCartRequest.isArtwork,
      artworkPrice = addToCartRequest.artworkPrice,
      artworkUrl = addToCartRequest.artworkUrl,
      productDemo = addToCartRequest.productDemoId,
      productDemoPrice = addToCartRequest.productDemoPrice,
      description = addToCartRequest.description,
      amountUnitPrice = addToCartRequest.unitPrice,
      unitPrice = 0.0,
      totalPrice = 0.0
    )

    val decimalFormat = DecimalFormat("#.##")
    var summarySpecialTechnicPrice: Double = 0.0
    if (addToCartRequest.specialTechnic.isNotEmpty()) {
      summarySpecialTechnicPrice = addToCartRequest.specialTechnic.sumOf { it!!.price }
    }

    val summaryMaterialPrice = (addToCartRequest.amount * addToCartRequest.unitPrice)
    val summaryTotal = decimalFormat.format(
      (
          summaryMaterialPrice
              + summarySpecialTechnicPrice
              + addToCartRequest.printingPrice
              + addToCartRequest.coatingPrice
              + addToCartRequest.artworkPrice
              + addToCartRequest.productDemoPrice
          )
    ).toDouble()
    val summaryUnitPrice = decimalFormat.format(summaryTotal / addToCartRequest.amount).toDouble()
    item.unitPrice = summaryUnitPrice
    item.totalPrice = summaryTotal

    val createItem = itemRepository.save(item)

    if (addToCartRequest.specialTechnic.isNotEmpty()) {
      val itemSpecialTechnic = addToCartRequest.specialTechnic.map {
        ItemSpecialTechnic(
          itemId = createItem.id,
          specialTechnicId = it!!.id,
          width = it.width,
          height = it.height
        )
      }

      itemSpecialTechnic.let { itemSpecialTechnicRepository.saveAll(it) }
    }



    cartRepository.save(
      Cart(
        userId = userId,
//        itemId = createItem.id
      )
    )
  }

  override fun findAllByUserId(userId: Int): List<GetUserCartDto>? {
    return cartRepository.findCartByUserId(userId)
  }

  override fun findItemById(id: Int): GetUserCartDto? {
    itemRepository.findById(id).orElseThrow { throw NotFoundException("ไม่มีสินค้าในรถเข็น") }
    return cartRepository.findCartByItemId(id)
  }

  @Transactional
  override fun updateItemCart(id: Int, updateCartRequest: UpdateCartRequest) {
    val item =
      itemRepository.findById(id).orElseThrow { throw NotFoundException("ไม่มีสินค้าในรถเข็น") }
    item.modelId = updateCartRequest.modelId
    item.width = updateCartRequest.width
    item.length = updateCartRequest.length
    item.height = updateCartRequest.height
    item.materialId = updateCartRequest.materialId
    item.amount = updateCartRequest.amount
    item.amountUnitPrice = updateCartRequest.unitPrice
    item.printing = updateCartRequest.printingId
    item.printingPrice = updateCartRequest.printingPrice
    item.coatingId = updateCartRequest.coatingId
    item.isArtwork = updateCartRequest.isArtwork
    item.artworkPrice = updateCartRequest.artworkPrice
    item.artworkUrl = updateCartRequest.artworkUrl
    item.productDemo = updateCartRequest.productDemoId
    item.productDemoPrice = updateCartRequest.productDemoPrice
    item.description = updateCartRequest.description
    item.modifiedDate = Date()

    val decimalFormat = DecimalFormat("#.##")

    var summarySpecialTechnicPrice: Double = 0.0
    if (updateCartRequest.specialTechnic.isNotEmpty()) {
      summarySpecialTechnicPrice = updateCartRequest.specialTechnic.sumOf { it!!.price }
    }
    val summaryMaterialPrice = (updateCartRequest.amount * updateCartRequest.unitPrice)
    val summaryTotal = decimalFormat.format(
      (
          summaryMaterialPrice
              + summarySpecialTechnicPrice
              + updateCartRequest.printingPrice
              + updateCartRequest.coatingPrice
              + updateCartRequest.artworkPrice
              + updateCartRequest.productDemoPrice
          )
    ).toDouble()
    val summaryUnitPrice = decimalFormat.format(summaryTotal / updateCartRequest.amount).toDouble()

    item.unitPrice = summaryUnitPrice
    item.totalPrice = summaryTotal
    itemRepository.save(item)

    itemSpecialTechnicRepository.deleteAllByItemId(id)
    if (updateCartRequest.specialTechnic.isNotEmpty()) {
      val itemSpecialTechnic = updateCartRequest.specialTechnic.map {
        ItemSpecialTechnic(
          itemId = id,
          specialTechnicId = it!!.id,
          width = it.width,
          height = it.height
        )
      }

      itemSpecialTechnic.let { itemSpecialTechnicRepository.saveAll(it) }
    }
  }

  @Transactional
  override fun deleteCartItem(id: Int) {
    itemRepository.findById(id).orElseThrow { throw NotFoundException("ไม่มีสินค้าในรถเข็น") }

    itemSpecialTechnicRepository.deleteAllByItemId(id)
    itemRepository.deleteById(id)
    cartRepository.deleteByItemId(id)
  }

  override fun addCustomToCart(userPrincipal: UserPrincipal?, uuid: String?, customizeId: Long): AddCustomCartDto {
    val customize = customizeRepository.findById(customizeId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลปรับแต่งสินค้า")
    }
    val guestId = UUID.randomUUID()
    val expDate = Calendar.getInstance().apply { add(Calendar.YEAR, 1) }.time
    val expireDate = LocalDateTime.now().plusDays(7)
    var myCart = Cart()
    if (userPrincipal == null && uuid.isNullOrBlank()) {
      myCart = cartRepository.save(
        Cart(
          uuid = guestId.toString(),
          customizeId = customizeId,
          expireDate = expireDate
        )
      )
    }

    if (uuid != null && userPrincipal == null) {
      myCart = cartRepository.save(
        Cart(
          uuid = uuid,
          customizeId = customizeId,
          expireDate = expireDate
        )
      )
      val cart = cartRepository.findAllByUuid(uuid)
      cart.map {
        it.expireDate = expireDate
      }
      cartRepository.saveAll(cart)
    }

    if (userPrincipal != null && uuid.isNullOrBlank()) {
      myCart = cartRepository.save(
        Cart(
          userId = userPrincipal.getUserId().toInt(),
          customizeId = customizeId
        )
      )
    }

    if (userPrincipal != null && !uuid.isNullOrBlank()) {
      val cart = cartRepository.findAllByUuid(uuid)
      cart.map {
        it.uuid = null
        it.userId = userPrincipal.getUserId().toInt()
        it.expireDate = null
      }
      cartRepository.saveAll(cart)
      myCart = cartRepository.save(
        Cart(
          userId = userPrincipal.getUserId().toInt(),
          customizeId = customizeId
        )
      )
    }
    return myCart.let {
      AddCustomCartDto(
        id = it.id,
        userId = it.userId?.toLong(),
        uuid = it.uuid,
        customizeId = it.customizeId!!,
      )
    }
  }

  override fun getMyCart(userPrincipal: UserPrincipal?, uuid: String?): List<CartListDto> {
    if (userPrincipal == null && uuid.isNullOrBlank()) {
      throw BadRequestException("ไม่พบข้อมูลผู้ใช้")
    }

    return cartRepository.findCustomCart(userPrincipal, uuid)
  }

  override fun updateMyCart(cartId: Int, updateCartRequest: UpdateMyCartRequest): CartListDto {
    val cart = cartRepository.findById(cartId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลรถเข็น")
    }
    val customize = customizeRepository.findById(cart.customize!!.id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลปรับแต่งสินค้า")
    }
    val modelDetail = modelSizeConfigDetailRepository.findById(updateCartRequest.modelSizeConfigDetailId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลขนาดสินค้า")
    }
    val period = productPeriodRepository.findById(updateCartRequest.productPeriodId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลระยะเวลาการผลิต")
    }

    customize.modelSizeConfigDetailId = modelDetail.id
    customize.modelSizeConfigDetail = modelDetail
    customize.productPeriodId = period.id
    customize.productPeriod = period
    customizeRepository.save(customize)
    return cartRepository.getMyCartById(cartId)
  }

  override fun getMyCartById(cartId: Int): CartListDto {
    val cart = cartRepository.findById(cartId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลรถเข็น")
    }
    return cartRepository.getMyCartById(cart.id)
  }

  override fun getMyCartByListId(cartId: List<Int>): List<CartListDto> {
    val existId = cartRepository.findAllById(cartId).map { it.id }
    if (existId.size != cartId.size) {
      throw NotFoundException("ไม่พบข้อมูลรถเข็น")
    }
    val cart = cartRepository.findAllById(cartId)
    return cart.map { cartRepository.getMyCartById(it.id) }
  }

  override fun deleteCartById(cartId: Int): Boolean {
    val cart = cartRepository.findById(cartId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลรถเข็น")
    }
    cartRepository.deleteById(cart.id)
    return true
  }


  @Transactional
  @Scheduled(cron = "0 * * * * *")
  fun deleteExpiredGuestCarts() {
    val now = LocalDateTime.now()
    cartRepository.deleteAllByExpireDateBefore(now)
  }
}