package com.lucablock.digiboxapi.cart.service.impl

import com.lucablock.digiboxapi.cart.dto.*
import com.lucablock.digiboxapi.cart.repository.CartRepositoryCustom
import com.lucablock.digiboxapi.customize.constant.SampleProductEnum
import com.lucablock.digiboxapi.customize.dto.CustomizeCoatingListDto
import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.modelsize.dto.ModelSizeListDto
import com.lucablock.digiboxapi.productcategoryconfig.dto.ProductCategoryConfigDto
import com.lucablock.digiboxapi.productgallery.constant.ProductGalleryEnum
import com.lucablock.digiboxapi.productgallery.dto.ProductGalleryDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigCustomDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigListDto
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.Projections
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.stereotype.Service

@Service
class CartRepositoryImpl : CartRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private final val qCart = QCart.cart
  private val qCustomize = QCustomize.customize
  private val qCustomizeCoating = QCustomizeCoating.customizeCoating
  private val qModel = QModel.model
  private val qProduct = QProduct.product
  private val qModelSize = QModelSize.modelSize
  private val qMaterialCoating = QMaterialConfig.materialConfig
  private val qMaterial = QMaterials.materials
  private val qGram = QGram.gram
  private val qPrinting = QPrinting.printing
  private val qMaterialConfig = QMaterialConfig.materialConfig
  private val qModelSizeConfigCoating = QModelSizeConfigCoating.modelSizeConfigCoating
  private val qCoating = QCoating.coating
  private val qCustomizeSpecialTechnic = QCustomizeSpecialTechnic.customizeSpecialTechnic
  private val qSpecialTechnicConfig = QSpecialTechnicConfig.specialTechnicConfig
  private val qSpecialTechnic = QSpecialTechnic.specialTechnic
  private val qAreaSizePercentage = QAreaSizePercentage.areaSizePercentage
  private val qModelSizeConfigDetail = QModelSizeConfigDetail.modelSizeConfigDetail
  private val qProductPeriod = QProductPeriod.productPeriod
  private val qProductGallery = QProductGallery.productGallery
  private val qProductCategoryConfig = QProductCategoryConfig.productCategoryConfig
  private val qProductCategory = QProductCategory.productCategory

  override fun findCartByUserId(userId: Int): List<GetUserCartDto>? {
    TODO("Not yet implemented")
  }

  override fun findCartByItemId(itemId: Int): GetUserCartDto? {
    TODO("Not yet implemented")
  }

  override fun findCustomCart(
    userPrincipal: UserPrincipal?,
    uuid: String?
  ): List<CartListDto> {

    val criteria = BooleanBuilder()

    if (userPrincipal != null) {
      criteria.or(qCart.userId.eq(userPrincipal.getUserId().toInt()))
    }
    if (!uuid.isNullOrBlank()) {
      criteria.or(qCart.uuid.eq(uuid))
    }
    val query = queryFactory

    val cart = query.select(
      Projections.constructor(
        CartListDto::class.java,
        qCart.id,
        qCustomize.id,
        qProduct.id,
        qProduct.name,
        qCustomize.model.id,
        qCustomize.model.name,
        qCustomize.model.imageUrl,
        qCustomize.modelSizeId,
        qCustomize.modelSize.unfoldedSizeId,
        qCustomize.modelSize.unfoldedSize.name,
        qCustomize.modelSize.width,
        qCustomize.modelSize.height,
        qCustomize.modelSize.length,
        qCustomize.materialConfigId,
        qCustomize.materialConfig.materials.name,
        qCustomize.materialConfig.gramId,
        qCustomize.materialConfig.grams.gsm,
        qCustomize.materialConfig.grams.mm,
        qCustomize.printingId,
        qCustomize.printing.name,
        qCustomize.modelSizeConfigDetailId,
        qCustomize.modelSizeConfigDetail.amount,
        qCustomize.modelSizeConfigDetail.price,
        qCustomize.modelSizeConfigDetail.period,
        qCustomize.productPeriod.id,
        qCustomize.productPeriod.name,
        qCustomize.productPeriod.price,
        qCustomize.productPeriod.imageUrl,
        qCustomize.productPeriod.maxPeriod,
        qCustomize.productPeriod.minPeriod,
        qCustomize.zipcode,
        qCustomize.sampleProduct,
      )
    ) .from(qCart)
      .leftJoin(qCart.customize, qCustomize)
      .leftJoin(qCustomize.model, qModel)
      .leftJoin(qModel.product, qProduct)
      .leftJoin(qCustomize.materialConfig, qMaterialConfig)
      .leftJoin(qMaterialConfig.materials, qMaterial)
      .leftJoin(qMaterialConfig.grams, qGram)
      .leftJoin(qCustomize.printing, qPrinting)
      .leftJoin(qCustomize.modelSize, qModelSize)
      .leftJoin(qCustomize.productPeriod, qProductPeriod)
      .leftJoin(qCustomize.modelSizeConfigDetail, qModelSizeConfigDetail)
      .where(criteria)
      .fetch()

//    val modelSizes = queryFactory.select(
//      Projections.constructor(
//        ModelSizeListDto::class.java,
//        qModelSize.id,
//        qModelSize.width,
//        qModelSize.height,
//        qModelSize.length,
//        qModelSize.unfoldedSizeId,
//        qModelSize.unfoldedSize.name,
//        qModelSize.isThreeD
//      )
//    ).from(qModelSize)
//      .where(qModelSize.id.`in`(cart.map { it.modelSize.id }.distinct()))
//      .fetch()
//    val modelSizeMap = modelSizes.associateBy { it.id }

    val customCoating = query.select(
      Projections.constructor(
        CustomizeCoatingListDto::class.java,
        qCustomizeCoating.id,
        qCustomizeCoating.customizeId,
        qCustomizeCoating.modelSizeConfigCoating.coating.id,
        qCustomizeCoating.modelSizeConfigCoating.coating.name,
        qCustomizeCoating.modelSizeConfigCoating.coating.price,
      )
    )
      .from(qCustomizeCoating)
      .leftJoin(qCustomizeCoating.modelSizeConfigCoating, qModelSizeConfigCoating)
      .leftJoin(qModelSizeConfigCoating.coating, qCoating)
      .leftJoin(qCustomize).on(qCustomize.id.eq(qCustomizeCoating.customizeId))
      .where(qCustomizeCoating.customizeId.`in`(cart.map { it.customId }.distinct()))
      .fetch()

    val specialTechnic = query.select(
      Projections.constructor(
        SpecialTechnicConfigCustomDto::class.java,
        qCustomizeSpecialTechnic.id,
        qCustomizeSpecialTechnic.customizeId,
        qSpecialTechnicConfig.price,
        qSpecialTechnicConfig.specialTechnic.id,
        qSpecialTechnicConfig.specialTechnic.name,
        qSpecialTechnicConfig.areaSizePercentage.id,
        qSpecialTechnicConfig.areaSizePercentage.name,
        qSpecialTechnicConfig.areaSizePercentage.percentage
      )
    )
      .from(qCustomizeSpecialTechnic)
      .leftJoin(qCustomizeSpecialTechnic.specialTechnicConfig, qSpecialTechnicConfig)
      .leftJoin(qSpecialTechnicConfig.specialTechnic, qSpecialTechnic)
      .leftJoin(qSpecialTechnicConfig.areaSizePercentage, qAreaSizePercentage)
      .leftJoin(qCustomize).on(qCustomize.id.eq(qCustomizeSpecialTechnic.customizeId))
      .where(qCustomizeSpecialTechnic.customizeId.`in`(cart.map { it.customId }.distinct()))
      .fetch()

    val productGalleries = queryFactory
      .selectFrom(qProductGallery)
      .where(qProductGallery.productId.`in`(cart.map { it.productId }.distinct()))
      .fetch()
      .map { gallery ->
        ProductGalleryDto(
          id = gallery.id,
          productId = gallery.productId,
          imageUrl = gallery.imageUrl,
          fileType = gallery.fileType,
          fileTypeEnum = ProductGalleryEnum.fromValue(gallery.fileType).description
        )
      }

    val productCategoryConfigs = query.select(
      Projections.constructor(
        ProductCategoryConfigDto::class.java,
        qProductCategoryConfig.id,
        qProductCategoryConfig.productId,
        qProductCategoryConfig.productCategoryId,
        qProductCategory.name
      )
    )
      .from(qProductCategoryConfig)
      .leftJoin(qProductCategory).on(qProductCategory.id.eq(qProductCategoryConfig.productCategoryId))
      .where(qProductCategoryConfig.productId.`in`(cart.map { it.productId }.distinct()))
      .fetch()

    return cart.map { carts ->
      val dto = CartListDto(
        cartId = carts.cartId,
        customId = carts.customId,
        productId = carts.productId,
        productName = carts.productName,
        modelId = carts.modelId,
        modelName = carts.modelName,
        modelImage = carts.modelImage,
        modelSizeId = carts.modelSizeId,
        unfoldedSizeId = carts.unfoldedSizeId,
        unfoldedSizeName = carts.unfoldedSizeName,
        width = carts.width,
        height = carts.height,
        length = carts.length,
        materialConfigId = carts.materialConfigId,
        materialName = carts.materialName,
        gramId = carts.gramId,
        gramGsm = carts.gramGsm,
        gramMM = carts.gramMM,
        printingId = carts.printingId,
        printName = carts.printName,
        modelSizeConfigDetailId = carts.modelSizeConfigDetailId,
        amount = carts.amount,
        amountUnitPrice = carts.amountUnitPrice,
        period = carts.period,
        productPeriodId = carts.productPeriodId,
        productPeriodName = carts.productPeriodName,
        productPeriodPrice = carts.productPeriodPrice,
        productPeriodImageUrl = carts.productPeriodImageUrl,
        productMaxPeriod = carts.productMaxPeriod,
        productMinPeriod = carts.productMinPeriod,
        zipcode = carts.zipcode,
        sampleProduct = carts.sampleProduct,
//        sampleProductPrice = SampleProductEnum.fromValue(cart.sampleProduct).price,
//        specialTechnics = specialTechnic.filter { it.customId == cart.customId }
      )
      dto.sampleProductName = SampleProductEnum.fromValue(carts.sampleProduct).description
      dto.sampleProductPrice = SampleProductEnum.fromValue(carts.sampleProduct).price
      dto.coatings = customCoating.filter { it.customId == carts.customId }
      dto.specialTechnics = specialTechnic.filter { it.customId == carts.customId }
      dto.productImage = productGalleries.filter { it.productId == carts.productId }
      dto.productCategoryId = productCategoryConfigs.filter { it.productId == carts.productId }
      dto
    }.toMutableList()
  }

  override fun getMyCartById(cartId: Int): CartListDto {
    val criteria = qCart.id.eq(cartId)
    val query = queryFactory

    val cart = query.select(
      Projections.constructor(
        CartListDto::class.java,
        qCart.id,
        qCustomize.id,
        qProduct.id,
        qProduct.name,
        qCustomize.model.id,
        qCustomize.model.name,
        qCustomize.model.imageUrl,
        qCustomize.modelSizeId,
        qCustomize.modelSize.unfoldedSizeId,
        qCustomize.modelSize.unfoldedSize.name,
        qCustomize.modelSize.width,
        qCustomize.modelSize.height,
        qCustomize.modelSize.length,
        qCustomize.materialConfigId,
        qCustomize.materialConfig.materials.name,
        qCustomize.materialConfig.gramId,
        qCustomize.materialConfig.grams.gsm,
        qCustomize.materialConfig.grams.mm,
        qCustomize.printingId,
        qCustomize.printing.name,
        qCustomize.modelSizeConfigDetailId,
        qCustomize.modelSizeConfigDetail.amount,
        qCustomize.modelSizeConfigDetail.price,
        qCustomize.modelSizeConfigDetail.period,
        qCustomize.productPeriod.id,
        qCustomize.productPeriod.name,
        qCustomize.productPeriod.price,
        qCustomize.productPeriod.imageUrl,
        qCustomize.productPeriod.maxPeriod,
        qCustomize.productPeriod.minPeriod,
        qCustomize.zipcode,
        qCustomize.sampleProduct
      )
    ) .from(qCart)
      .leftJoin(qCart.customize, qCustomize)
      .leftJoin(qCustomize.model, qModel)
      .leftJoin(qModel.product, qProduct)
      .leftJoin(qCustomize.materialConfig, qMaterialConfig)
      .leftJoin(qMaterialConfig.materials, qMaterial)
      .leftJoin(qMaterialConfig.grams, qGram)
      .leftJoin(qCustomize.printing, qPrinting)
      .leftJoin(qCustomize.modelSize, qModelSize)
      .leftJoin(qCustomize.productPeriod, qProductPeriod)
      .leftJoin(qCustomize.modelSizeConfigDetail, qModelSizeConfigDetail)
      .where(criteria)
      .fetch()

    val customCoating = query.select(
      Projections.constructor(
        CustomizeCoatingListDto::class.java,
        qCustomizeCoating.id,
        qCustomizeCoating.customizeId,
        qCustomizeCoating.modelSizeConfigCoating.coating.id,
        qCustomizeCoating.modelSizeConfigCoating.coating.name,
        qCustomizeCoating.modelSizeConfigCoating.coating.price,
      )
    )
      .from(qCustomizeCoating)
      .leftJoin(qCustomizeCoating.modelSizeConfigCoating, qModelSizeConfigCoating)
      .leftJoin(qModelSizeConfigCoating.coating, qCoating)
      .leftJoin(qCustomize).on(qCustomize.id.eq(qCustomizeCoating.customizeId))
      .where(qCustomizeCoating.customizeId.`in`(cart.map { it.customId }.distinct()))
      .fetch()

    val specialTechnic = query.select(
      Projections.constructor(
        SpecialTechnicConfigCustomDto::class.java,
        qCustomizeSpecialTechnic.id,
        qCustomizeSpecialTechnic.customizeId,
        qSpecialTechnicConfig.price,
        qSpecialTechnicConfig.specialTechnic.id,
        qSpecialTechnicConfig.specialTechnic.name,
        qSpecialTechnicConfig.areaSizePercentage.id,
        qSpecialTechnicConfig.areaSizePercentage.name,
        qSpecialTechnicConfig.areaSizePercentage.percentage
      )
    )
      .from(qCustomizeSpecialTechnic)
      .leftJoin(qCustomizeSpecialTechnic.specialTechnicConfig, qSpecialTechnicConfig)
      .leftJoin(qSpecialTechnicConfig.specialTechnic, qSpecialTechnic)
      .leftJoin(qSpecialTechnicConfig.areaSizePercentage, qAreaSizePercentage)
      .leftJoin(qCustomize).on(qCustomize.id.eq(qCustomizeSpecialTechnic.customizeId))
      .where(qCustomizeSpecialTechnic.customizeId.`in`(cart.map { it.customId }.distinct()))
      .fetch()

    val productGalleries = queryFactory
      .selectFrom(qProductGallery)
      .where(qProductGallery.productId.`in`(cart.map { it.productId }.distinct()))
      .fetch()
      .map { gallery ->
        ProductGalleryDto(
          id = gallery.id,
          productId = gallery.productId,
          imageUrl = gallery.imageUrl,
          fileType = gallery.fileType,
          fileTypeEnum = ProductGalleryEnum.fromValue(gallery.fileType).description
        )
      }

    val productCategoryConfigs = query.select(
      Projections.constructor(
        ProductCategoryConfigDto::class.java,
        qProductCategoryConfig.id,
        qProductCategoryConfig.productId,
        qProductCategoryConfig.productCategoryId,
        qProductCategory.name
      )
    )
      .from(qProductCategoryConfig)
      .leftJoin(qProductCategory).on(qProductCategory.id.eq(qProductCategoryConfig.productCategoryId))
      .where(qProductCategoryConfig.productId.`in`(cart.map { it.productId }.distinct()))
      .fetch()

    val cartDto = cart.first()
    cartDto.sampleProductName = SampleProductEnum.fromValue(cart.first().sampleProduct).description
    cartDto.sampleProductPrice = SampleProductEnum.fromValue(cart.first().sampleProduct).price
    cartDto.coatings = customCoating
    cartDto.specialTechnics = specialTechnic
    cartDto.productImage = productGalleries
    cartDto.productCategoryId = productCategoryConfigs

    return cartDto
  }
//  override fun findCartByUserId(userId: Int): List<GetUserCartDto> {
//
//    val query = queryFactory.select(qCart).from(qCart).where(qCart.userId.eq(userId))
//    return query.fetch().map { cart ->
//      GetUserCartDto(
//        cart.itemId!!,
//        cart.item!!.width,
//        cart.item!!.length,
//        cart.item!!.height,
//        PrintingDto(
//          cart.item!!.printing,
//          cart.item!!.printingPrice
//        ),
//        cart.item!!.model!!.let {
//          ItemModelDto(
//            it.id,
//            it.name,
//            it.imageUrl,
//            it.productId
//          ) },
//        cart.item!!.material?.let {
//          ItemMaterialDto(
//            it.id,
//            it.name,
////            it.gram,
//            it.imageUrl,
//            cart.item!!.amount,
//            cart.item!!.amountUnitPrice
//          ) },
//        cart.item!!.coating?.let {
//          ItemCoatingDto(
//            it.id,
//            it.name,
//            it.imageUrl,
//            it.price
//          ) },
//        cart.item!!.specialTechnics.map {
//          ItemSpecialTechnicDto(
//            it!!.specialTechnic!!.id,
//            it.specialTechnic!!.name,
//            it.specialTechnic!!.imageUrl,
////            it.specialTechnic!!.price,
//            it.width,
//            it.height,
//          ) },
//        ArtworkDto(
//          cart.item!!.isArtwork,
//          cart.item!!.artworkUrl,
//          cart.item!!.artworkPrice,
//        ),
//        ProductDemoDto(
//          cart.item!!.productDemo,
//          cart.item!!.productDemoPrice
//        ),
//        cart.item!!.description,
//        cart.item!!.unitPrice,
//        cart.item!!.totalPrice,
//        cart.item!!.createdDate,
//        cart.item!!.modifiedDate
//      )
//    }
//  }
//
//  override fun findCartByItemId(itemId: Int): GetUserCartDto? {
//    val query = queryFactory.select(qCart).from(qCart).where(qCart.itemId.eq(itemId))
//    return query.fetchOne()?.let { cart ->
//      GetUserCartDto(
//        cart.itemId!!,
//        cart.item!!.width,
//        cart.item!!.length,
//        cart.item!!.height,
//        PrintingDto(
//          cart.item!!.printing,
//          cart.item!!.printingPrice
//        ),
//        cart.item!!.model!!.let {
//          ItemModelDto(
//            it.id,
//            it.name,
//            it.imageUrl,
//            it.productId
//          ) },
//        cart.item!!.material?.let {
//          ItemMaterialDto(
//            it.id,
//            it.name,
////            it.gram,
//            it.imageUrl,
//            cart.item!!.amount,
//            cart.item!!.amountUnitPrice
//          ) },
//        cart.item!!.coating?.let {
//          ItemCoatingDto(
//            it.id,
//            it.name,
//            it.imageUrl,
//            it.price
//          ) },
//        cart.item!!.specialTechnics.map {
//          ItemSpecialTechnicDto(
//            it!!.specialTechnic!!.id,
//            it.specialTechnic!!.name,
//            it.specialTechnic!!.imageUrl,
////            it.specialTechnic!!.price,
//            it.width,
//            it.height,
//          ) },
//        ArtworkDto(
//          cart.item!!.isArtwork,
//          cart.item!!.artworkUrl,
//          cart.item!!.artworkPrice,
//        ),
//        ProductDemoDto(
//          cart.item!!.productDemo,
//          cart.item!!.productDemoPrice
//        ),
//        cart.item!!.description,
//        cart.item!!.unitPrice,
//        cart.item!!.totalPrice,
//        cart.item!!.createdDate,
//        cart.item!!.modifiedDate
//      )
//    }
//  }
}
