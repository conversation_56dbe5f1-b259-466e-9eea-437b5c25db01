package com.lucablock.digiboxapi.cart.controller

import com.lucablock.digiboxapi.cart.request.AddToCartRequest
import com.lucablock.digiboxapi.cart.request.UpdateCartRequest
import com.lucablock.digiboxapi.cart.request.UpdateMyCartRequest
import com.lucablock.digiboxapi.cart.service.CartService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class CartController {
  private val logger: Logger = LoggerFactory.getLogger(CartController::class.java)

  @Autowired
  lateinit var cartService: CartService

  @PostMapping("/cart")
  fun addToCart(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @Valid @RequestBody addToCartRequest: AddToCartRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เพิ่มสินค้าลงในรถเข็นแล้ว",
          cartService.addToCart(userPrincipal.getUserId().toInt(), addToCartRequest)
        )
      )
    } catch (e: Exception) {
      logger.error("Add to cart failure :  ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/cart")
  fun findAllCartByUserId(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงสินค้าในรถเข็นทั้งหมดสำเร็จ",
          cartService.findAllByUserId(userPrincipal.getUserId().toInt())
        )
      )
    } catch (e: Exception) {
      logger.error("Find all cart by user id failure", e)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/cart/item/{id}")
  fun findItemById(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ค้นสินค้าในรถเข็นด้วยรหัสสำเร็จ",
          cartService.findItemById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Find item by id failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีสินค้าในรถเข็น"))
    } catch (e: Exception) {
      logger.error("Find item by id failure : ${e.message}")
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
          HttpResponse(
            false,
            "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"
          )
        )
    }
  }

  @PutMapping("/cart/item/{id}")
  fun updateItemCart(
    @PathVariable id: Int,
    @Valid @RequestBody updateCartRequest: UpdateCartRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แก้ไขสินค้าในรถเข็นสำเร็จ",
          cartService.updateItemCart(id, updateCartRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Find item by id failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีสินค้าในรถเข็น"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @DeleteMapping("/cart/{id}")
  fun deleteCartItem(
    @PathVariable id: Int
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบสินค้าในรถเข็นสำเร็จ",
          cartService.deleteCartItem(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Find item by id failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่มีสินค้าในรถเข็น"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }
  @PostMapping("/web/cart/add-custom-cart")
  fun addCustomToCart(
    @RequestParam(value = "uuid", required = false) uuid: String?,
    @RequestParam(value = "customizeId", required = true) customizeId: Long,
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal?
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "เพิ่มสินค้าในรถเข็นสำเร็จ",
          cartService.addCustomToCart(userPrincipal,uuid,customizeId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("Add custom to cart failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบผู้ใช้งาน"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false,e.message?: "คำร้องขอไม่ถูกต้อง"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/cart/my-cart")
  fun getMyCart(
    @RequestParam(value = "uuid", required = false) uuid: String?,
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal?
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงสินค้าในรถเข็นสำเร็จ",
          cartService.getMyCart(userPrincipal,uuid)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("show custom to cart failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false,e.message?: "ไม่พบผู้ใช้งาน"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "คำร้องขอไม่ถูกต้อง"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/cart/my-cart/{cartId}")
  fun getMyCartById(
    @PathVariable cartId: Int
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงสินค้าในรถเข็นสำเร็จ",
          cartService.getMyCartById(cartId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("show custom to cart failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false,e.message?: "ไม่พบผู้ใช้งาน"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "คำร้องขอไม่ถูกต้อง"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @PutMapping("/web/cart/update-my-cart/{cartId}")
  fun updateMyCartById(
    @PathVariable cartId: Int,
    @Valid @RequestBody updateCartRequest: UpdateMyCartRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แก้ไขสินค้าในรถเข็นสำเร็จ",
          cartService.updateMyCart(cartId,updateCartRequest)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("update custom to cart failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false,e.message?: "ไม่พบผู้ใช้งาน"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "คำร้องขอไม่ถูกต้อง"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @DeleteMapping("/web/cart/delete-my-cart/{cartId}")
  fun deleteMyCartById(
    @PathVariable cartId: Int
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบสินค้าในรถเข็นสำเร็จ",
          cartService.deleteCartById(cartId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error("delete custom to cart failure : ${e.message}")
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false,e.message?: "ไม่พบผู้ใช้งาน"))
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "คำร้องขอไม่ถูกต้อง"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @GetMapping("/web/cart/my-cart/select/{cartId}")
  fun getMyCartBySelectId(
    @PathVariable cartId: List<Int>
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงสินค้าในรถเข็นสำเร็จ",
          cartService.getMyCartByListId(cartId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูลรถเข็น"))
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

}