package com.lucablock.digiboxapi.cart.repository

import com.lucablock.digiboxapi.cart.dto.CartListDto
import com.lucablock.digiboxapi.cart.dto.GetUserCartDto
import com.lucablock.digiboxapi.user.dto.UserPrincipal

interface CartRepositoryCustom {
  fun findCartByUserId(userId: Int): List<GetUserCartDto>?
  fun findCartByItemId(itemId: Int): GetUserCartDto?
  fun findCustomCart(
    userPrincipal: UserPrincipal?,
    uuid:String?
  ): List<CartListDto>
  fun getMyCartById(cartId: Int):CartListDto
}