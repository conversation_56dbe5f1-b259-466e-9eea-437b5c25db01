package com.lucablock.digiboxapi.cart.repository

import com.lucablock.digiboxapi.entity.Cart
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Repository
interface CartRepository: JpaRepository<Cart, Int>, CartRepositoryCustom {
  fun findAllByUserId(userId: Int): List<Cart>?
  @Transactional
  @Modifying
  @Query("delete from cart where item_id = :itemId", nativeQuery = true)
  fun deleteByItemId(itemId: Int): Any
  fun findAllByUuid(uuid: String): List<Cart>
  fun deleteAllByExpireDateBefore(expireDateBefore: LocalDateTime)
  fun deleteAllByExpireDateAfter(expireDateAfter: LocalDateTime)
  fun findAllByUserIdOrUuid(userId: Int, uuid: String): MutableList<Cart>
  fun existsByIdIn(ids: List<Int>): Boolean
  fun findCartByCustomizeId(customizeId: Long): MutableList<Cart>
  fun findCartByCustomizeIdIn(customizeIds: List<Long>): MutableList<Cart>
  fun deleteByUserIdAndCustomizeId(userId: Int, customizeId: Long)
}