package com.lucablock.digiboxapi.cart.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.customize.dto.CustomizeCoatingDto
import com.lucablock.digiboxapi.customize.dto.CustomizeCoatingListDto
import com.lucablock.digiboxapi.entity.ProductCategoryConfig

import com.lucablock.digiboxapi.modelsize.dto.ModelSizeListDto
import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailDto
import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailListDto
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigModelSizeDto
import com.lucablock.digiboxapi.productcategoryconfig.dto.ProductCategoryConfigDto
import com.lucablock.digiboxapi.productgallery.dto.ProductGalleryDto
import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodDto
import com.lucablock.digiboxapi.productperiod.dto.ProductPeriodListDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigCustomDto
import com.lucablock.digiboxapi.specialtechnicconfig.dto.SpecialTechnicConfigListDto
import com.querydsl.core.annotations.QueryProjection
import org.postgresql.shaded.com.ongres.scram.common.bouncycastle.pbkdf2.Integers
import java.io.Serializable
import java.time.LocalDateTime
import java.util.*

data class GetUserCartDto
@QueryProjection
constructor(
  var id: Int,
  var width: Int,
  var length: Int,
  var height: Int,
  var printing: PrintingDto,
  var model: ItemModelDto,
  var material: ItemMaterialDto?,
  var coating: ItemCoatingDto?,
  var specialTechnics: List<ItemSpecialTechnicDto?>,
  var artwork: ArtworkDto,
  var productDemo: ProductDemoDto,
  var description: String? = null,
  var unitPrice: Double,
  var totalPrice: Double,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date()
) : Serializable

data class PrintingDto
@QueryProjection
constructor(
  var id: Int,
  var price: Double? = 0.0
) : Serializable

data class ItemMaterialDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
//  var gram: Int,
  var imageUrl: String,
  var amount: Int,
  var price: Double
) : Serializable

data class ItemModelDto
@QueryProjection
constructor(
  var id: Long,
  var name: String,
  var imageUrl: String,
  var productId: Long,
) : Serializable

data class ItemCoatingDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
  var imageUrl: String,
  var price: Double
) : Serializable

data class ItemSpecialTechnicDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
  var imageUrl: String,
//  var price: Double,
  var width: Int,
  var height: Int,
) : Serializable

data class ArtworkDto
@QueryProjection
constructor(
  var status: Boolean,
  var url: String? = null,
  var price: Double? = 0.0,
) : Serializable

data class ProductDemoDto
@QueryProjection
constructor(
  var id: Int,
  var price: Double? = 0.0
) : Serializable

data class CartListDto
@QueryProjection
constructor(
  val cartId : Int,
  val customId : Long,
  val productId: Long,
  val productName : String,
  val modelId : Long,
  val modelName : String,
  val modelImage : String,
  val modelSizeId: Long,
  var unfoldedSizeId: Long,
  var unfoldedSizeName: String,
  var width: Double,
  var height: Double,
  var length: Double?,
  val materialConfigId : Long,
  val materialName : String,
  val gramId : Long,
  val gramGsm : Int?,
  val gramMM : Double?,
  val printingId : Long,
  val printName : String,
  val modelSizeConfigDetailId : Long,
  val amount: Int,
  val amountUnitPrice: Double,
  val period: Int,
  val productPeriodId : Long,
  val productPeriodName : String,
  val productPeriodPrice : Double,
  val productPeriodImageUrl : String,
  val productMaxPeriod : Int,
  val productMinPeriod : Int,
  val zipcode : String,
  val sampleProduct : Int,
) : Serializable{
  var sampleProductName : String = ""
  var sampleProductPrice : Double = 0.0
  var coatings : List<CustomizeCoatingListDto> = mutableListOf()
  var specialTechnics: List<SpecialTechnicConfigCustomDto> = mutableListOf()
  var productImage : List<ProductGalleryDto> = mutableListOf()
  var productCategoryId : List<ProductCategoryConfigDto> = mutableListOf()
}

data class AddCustomCartDto
@QueryProjection
constructor(
  var id: Int,
  var userId: Long? = null,
  var uuid : String? = null,
  var customizeId : Long,
  var expireDate : LocalDateTime? = null,
) : Serializable











