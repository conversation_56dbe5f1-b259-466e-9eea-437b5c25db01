package com.lucablock.digiboxapi.discount.controller

import com.lucablock.digiboxapi.discount.request.DiscountRequest
import com.lucablock.digiboxapi.discount.service.DiscountService
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.user.dto.UserPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/discount")
class DiscountController @Autowired constructor(
  private val discountService: DiscountService
) {
  @PostMapping()
  fun createDiscount(
    @Valid @RequestBody request: DiscountRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "เพิ่มส่วนลดสำเร็จ",
          data = discountService.createDiscount(request)
        )
      )
    } catch (e: BadRequestException) {
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อส่วนลดซ้ำ")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @GetMapping
  fun getListDiscountPage(
    @RequestParam("search", required = false) search: String?,
    @RequestParam("ascending", defaultValue = "true", required = false) ascending: Boolean,
    @RequestParam("categoryId", required = false) categoryId: Long?,
    @RequestParam("page", required = false, defaultValue = "0") page: Int,
    @RequestParam("size", required = false, defaultValue = "10") size: Int,
  ): ResponseEntity<Any> {
    val pageable: Pageable = PageRequest.of(page, size)
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงรายการส่วนลดสำเร็จ",
          data = discountService.getListDiscountPage(pageable, search, ascending, categoryId)
        )
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @PutMapping("/{id}")
  fun updateDiscount(
    @PathVariable id: Int,
    @Valid @RequestBody request: DiscountRequest
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แก้ไขข้อมูลส่วนลดสำเร็จ",
          data = discountService.updateDiscount(id, request)
        )
      )
    } catch (e: BadRequestException) {
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ชื่อส่วนลดซ้ำ")
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @DeleteMapping("/{id}")
  fun deleteDiscount(@PathVariable id: Int): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "ลบส่วนลดสำเร็จ",
          data = discountService.deleteDiscount(id)
        )
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @PostMapping("/retrieve-discount")
  fun retrieveDiscount(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestParam("discountId", required = true) discountId: Int,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "เก็บส่วนลดสำเร็จ",
          data = discountService.retrieveDiscount(userPrincipal.getUserId().toInt(), discountId)
        )
      )
    } catch (e: BadRequestException) {
      ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
        HttpResponse(false, e.message ?: "ส่วนลดนี้ถูกเก็บแล้ว")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @GetMapping("/list/user-discount")
  fun getListUserDiscount(
    @RequestParam("categoryId", required = false) categoryId: Long?,
    @RequestParam("page", required = false, defaultValue = "0") page: Int,
    @RequestParam("size", required = false, defaultValue = "10") size: Int,
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
  ): ResponseEntity<Any> {
    val pageable: Pageable = PageRequest.of(page, size)
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงรายการส่วนลดของฉันสำเร็จ",
          data = discountService.listUserDiscount(
            pageable,
            categoryId,
            userPrincipal.getUserId().toInt()
          )
        )
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @GetMapping("/{id}")
  fun getDiscountById(
    @PathVariable id: Int
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงส่วนลดสำเร็จ",
          data = discountService.getDiscountById(id)
        )
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }

  @GetMapping("/discount-code")
  fun getDiscountByCode(
    @Parameter(hidden = true) @AuthenticationPrincipal userPrincipal: UserPrincipal,
    @RequestParam code: String
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          status = true,
          message = "แสดงส่วนลดสำเร็จ",
          data = discountService.getDiscountByCode(userPrincipal.getUserId().toInt(), code)
        )
      )
    } catch (e: NotFoundException) {
      ResponseEntity.status(HttpStatus.NOT_FOUND).body(
        HttpResponse(false, e.message ?: "ไม่พบส่วนลด")
      )
    } catch (e: Exception) {
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
        HttpResponse(false, e.message ?: "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน")
      )
    }
  }
}