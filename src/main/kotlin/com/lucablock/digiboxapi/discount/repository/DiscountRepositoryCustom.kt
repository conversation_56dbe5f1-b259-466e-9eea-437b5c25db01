package com.lucablock.digiboxapi.discount.repository

import com.lucablock.digiboxapi.discount.dto.DiscountDtoAmount
import com.lucablock.digiboxapi.discount.dto.UserDiscountDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface DiscountRepositoryCustom {
  fun findGetListDiscountPage(
    pageable: Pageable,
    search: String?,
    ascending: Boolean,
    categoryId: Long?
  ): Page<DiscountDtoAmount>

  fun listUserDiscount(
    pageable: Pageable,
    categoryId: Long?,
    userId: Int
  ): Page<UserDiscountDto>

  fun getDiscountById(discountId: Int): DiscountDtoAmount
}