package com.lucablock.digiboxapi.discount.repository

import com.lucablock.digiboxapi.entity.Discount
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface DiscountRepository : JpaRepository<Discount, Int>, DiscountRepositoryCustom {
  fun findDiscountByTitleContainsIgnoreCaseAndIsDeletedFalse(title: String): Optional<Discount>
  fun findDiscountByDiscountCategoryIdAndIsActiveTrue(id: Long): List<Discount>
  fun findByTitleAndIdNotAndIsActiveTrueAndIsDeletedFalse(title: String, id: Int): Optional<Discount>

  @Query(
    "SELECT discount.discount_category_id FROM discount WHERE id IN :discountId",
    nativeQuery = true
  )
  fun findCategoryByDiscountId(discountId: List<Int>): List<Int>

  @Query(
    "select * from discount where id = :discount and is_active = true and :date <= end_date",
    nativeQuery = true
  )
  fun findByDiscountAndEndDate(discount: Int, date: Date): Optional<Discount>
  fun findByDiscountCodeAndIsActiveTrueAndIsDeletedFalse(code: String): Optional<Discount>
  fun findByDiscountCodeAndIdNotAndIsActiveTrueAndIsDeletedFalse(discountCode: String, id: Int): Optional<Discount>
  fun findByIdAndIsActiveTrueAndIsDeletedFalse(discountId: Int): Optional<Discount>
}