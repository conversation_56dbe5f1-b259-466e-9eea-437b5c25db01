package com.lucablock.digiboxapi.discount.repository

import com.lucablock.digiboxapi.entity.OrderDiscount
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface OrderDiscountRepository : JpaRepository<OrderDiscount, Long> {

  @Query(
    """
    select count(discount_id)
    from order_discount
    where discount_id = :discountId
  """, nativeQuery = true
  )
  fun countByDiscountIdInAndGroupByDiscountId(discountId: Int): Int
}