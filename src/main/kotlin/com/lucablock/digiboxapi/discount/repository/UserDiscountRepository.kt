package com.lucablock.digiboxapi.discount.repository

import com.lucablock.digiboxapi.entity.UserDiscount
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface UserDiscountRepository : JpaRepository<UserDiscount, Int> {

  @Query(
    "select * from user_discount where user_id = :userId and discount_id in (:discountIds) and is_active != false",
    nativeQuery = true
  )
  fun findByUserIdAndDiscountIds(userId: Int, discountIds: List<Int>): List<UserDiscount>
  fun findAllByUserIdAndDiscountId(userId: Int, discountId: Int): Optional<UserDiscount>
  fun countAllByDiscountId(discountId: Int): Long

  fun findByUserIdAndDiscountIdAndIsActiveTrue(userId: Int, discountId: Int): Optional<UserDiscount>
  fun findAllByUserIdAndIsActiveTrue(userId: Int): List<UserDiscount>

}