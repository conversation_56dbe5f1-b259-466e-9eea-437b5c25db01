package com.lucablock.digiboxapi.discount.service

import com.lucablock.digiboxapi.discount.dto.DiscountDto
import com.lucablock.digiboxapi.discount.dto.DiscountDtoAmount
import com.lucablock.digiboxapi.discount.dto.UserDiscountDto
import com.lucablock.digiboxapi.discount.request.DiscountRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface DiscountService {
  fun createDiscount(request: DiscountRequest): DiscountDto
  fun getListDiscountPage(
    pageable: Pageable,
    search: String?,
    ascending: Boolean,
    categoryId: Long?,
  ): Page<DiscountDtoAmount>

  fun updateDiscount(id: Int, request: DiscountRequest): DiscountDto
  fun deleteDiscount(id: Int): Boolean
  fun retrieveDiscount(userId: Int, discountId: Int): Boolean
  fun listUserDiscount(
    pageable: Pageable,
    categoryId: Long?,
    userId: Int
  ): Page<UserDiscountDto>

  fun getDiscountById(discountId: Int): DiscountDtoAmount
  fun getDiscountByCode(userId: Int, code: String): DiscountDto
}