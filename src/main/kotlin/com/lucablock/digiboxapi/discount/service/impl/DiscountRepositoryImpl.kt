package com.lucablock.digiboxapi.discount.service.impl

import com.lucablock.digiboxapi.discount.dto.*
import com.lucablock.digiboxapi.discount.repository.DiscountRepositoryCustom
import com.lucablock.digiboxapi.discountCategory.dto.QDiscountCategoryDto
import com.lucablock.digiboxapi.entity.QDiscount
import com.lucablock.digiboxapi.entity.QUser
import com.lucablock.digiboxapi.entity.QUserDiscount
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class DiscountRepositoryImpl : DiscountRepositoryCustom {

  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qDiscount = QDiscount.discount
  private val qUser = QUser.user
  private val qUserDiscount = QUserDiscount.userDiscount

  override fun findGetListDiscountPage(
    pageable: Pageable,
    search: String?,
    ascending: Boolean,
    categoryId: Long?
  ): Page<DiscountDtoAmount> {

    var criteria = qDiscount.isActive.eq(true).and(qDiscount.isDeleted.isFalse)

    if (categoryId != null) {
      criteria = criteria.and(qDiscount.discountCategoryId.eq(categoryId))
    }

    if (search != null) {
      criteria = criteria.and(qDiscount.title.containsIgnoreCase(search))
    }

    val orderBy = if (ascending) {
      qDiscount.id.asc()
    } else {
      qDiscount.id.desc()
    }

    val discounts = queryFactory
      .select(
        QDiscountDtoAmount(
          qDiscount.id,
          qDiscount.title,
          qDiscount.discountCode,
          qDiscount.percentage,
          qDiscount.maxDiscount,
          qDiscount.minPrice,
          qDiscount.maxUsage,
          qDiscount.condition,
          qDiscount.description,
          QDiscountCategoryDto(
            qDiscount.category.id,
            qDiscount.category.name,
            qDiscount.category.categoryImage
          ),
          qDiscount.discountImage,
          qDiscount.startDate,
          qDiscount.endDate,
          qDiscount.isActive,
          qUserDiscount.discountId.count().goe(qDiscount.maxUsage)
        )
      )
      .from(qDiscount)
      .leftJoin(qUserDiscount).on(qUserDiscount.discountId.eq(qDiscount.id))
      .where(criteria)
      .groupBy(
        qDiscount.id,
        qDiscount.category.id,
        qDiscount.category.name,
        qDiscount.category.categoryImage,
      )
      .orderBy(orderBy)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()

    val queryTotal = queryFactory.select(qDiscount).from(qDiscount)
      .where(criteria).fetch().size.toLong()

    return PageImpl(discounts, pageable, queryTotal)
  }


  override fun listUserDiscount(
    pageable: Pageable,
    categoryId: Long?,
    userId: Int
  ): Page<UserDiscountDto> {
    var criteria = qDiscount.isActive.eq(true).and(qDiscount.isDeleted.isFalse)

    if (categoryId != null) {
      criteria = criteria.and(qDiscount.discountCategoryId.eq(categoryId))
    }

    val query = queryFactory
      .selectDistinct(
        QUserDiscountDto(
          qUserDiscount.id,
          QDiscountDto(
            qDiscount.id,
            qDiscount.title,
            qDiscount.discountCode,
            qDiscount.percentage,
            qDiscount.maxDiscount,
            qDiscount.minPrice,
            qDiscount.maxUsage,
            qDiscount.condition,
            qDiscount.description,
            QDiscountCategoryDto(
              qDiscount.category.id,
              qDiscount.category.name,
              qDiscount.category.categoryImage
            ),
            qDiscount.discountImage,
            qDiscount.startDate,
            qDiscount.endDate,
            qDiscount.isActive
          ),
          qUserDiscount.isActive,
        )
      )
      .from(qUserDiscount)
      .innerJoin(qUser).on(qUserDiscount.userId.eq(userId))
      .innerJoin(qDiscount).on(qUserDiscount.discountId.eq(qDiscount.id))
      .where(criteria)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch()

    val queryTotal = queryFactory.select(qUserDiscount)
      .from(qUserDiscount)
      .innerJoin(qUser).on(qUserDiscount.userId.eq(userId))
      .innerJoin(qDiscount).on(qUserDiscount.discountId.eq(qDiscount.id))
      .where(criteria)
      .fetch().size.toLong()

    return PageImpl(query, pageable, queryTotal)
  }

  override fun getDiscountById(discountId: Int): DiscountDtoAmount {
    val criteria = qDiscount.id.eq(discountId)

    val discount = queryFactory
      .select(
        QDiscountDtoAmount(
          qDiscount.id,
          qDiscount.title,
          qDiscount.discountCode,
          qDiscount.percentage,
          qDiscount.maxDiscount,
          qDiscount.minPrice,
          qDiscount.maxUsage,
          qDiscount.condition,
          qDiscount.description,
          QDiscountCategoryDto(
            qDiscount.category.id,
            qDiscount.category.name,
            qDiscount.category.categoryImage
          ),
          qDiscount.discountImage,
          qDiscount.startDate,
          qDiscount.endDate,
          qDiscount.isActive,
          qUserDiscount.discountId.count().goe(qDiscount.maxUsage)
        )
      )
      .from(qDiscount)
      .leftJoin(qUserDiscount).on(qUserDiscount.discountId.eq(qDiscount.id))
      .where(criteria)
      .groupBy(
        qDiscount.id,
        qDiscount.category.id,
        qDiscount.category.name,
        qDiscount.category.categoryImage,
      )
      .fetchOne()

    return discount!!
  }

}