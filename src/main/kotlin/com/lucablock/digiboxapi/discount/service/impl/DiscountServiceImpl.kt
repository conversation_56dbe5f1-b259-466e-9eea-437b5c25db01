package com.lucablock.digiboxapi.discount.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.discount.dto.DiscountDto
import com.lucablock.digiboxapi.discount.dto.DiscountDtoAmount
import com.lucablock.digiboxapi.discount.dto.UserDiscountDto
import com.lucablock.digiboxapi.discount.repository.DiscountRepository
import com.lucablock.digiboxapi.discount.repository.UserDiscountRepository
import com.lucablock.digiboxapi.discount.request.DiscountRequest
import com.lucablock.digiboxapi.discount.service.DiscountService
import com.lucablock.digiboxapi.discountCategory.repository.DiscountCategoryRepository
import com.lucablock.digiboxapi.entity.Discount
import com.lucablock.digiboxapi.entity.UserDiscount
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.util.*

@Service
class DiscountServiceImpl @Autowired constructor(
  private val discountRepository: DiscountRepository,
  private val discountCategoryRepository: DiscountCategoryRepository,
  private val s3Service: S3Service,
  private val userDiscountRepository: UserDiscountRepository,
) : DiscountService {
  @Transactional
  override fun createDiscount(request: DiscountRequest): DiscountDto {

    discountRepository.findDiscountByTitleContainsIgnoreCaseAndIsDeletedFalse(request.title)
      .ifPresent {
        throw BadRequestException("ชื่อส่วนลดซ้ำ")
      }

    discountRepository.findByDiscountCodeAndIsActiveTrueAndIsDeletedFalse(request.discountCode)
      .ifPresent {
        throw BadRequestException("โค้ดส่วนลดซ้ำ")
      }

    if (request.startDate == 0L || request.endDate == 0L) {
      throw BadRequestException("ไม่พบวันเริ่มใช้และวันหมดอายุของส่วนลด")
    }

    val discountObj = Discount(
      title = request.title,
      discountCode = request.discountCode,
      percentage = request.percentage,
      maxDiscount = request.maxDiscount,
      minPrice = request.minPrice,
      maxUsage = request.maxUsage,
      condition = request.condition,
      description = request.description,
      discountImage = request.discountImage,
      discountCategoryId = request.discountCategoryId,
      startDate = Date(request.startDate * 1000),
      endDate = Date(request.endDate * 1000)
    )

    val urlFile = s3Service.moveFile(request.discountImage).url
    discountObj.discountImage = urlFile
    val discount = discountRepository.save(discountObj)

    return discount.toDiscountDto()
  }

  override fun getListDiscountPage(
    pageable: Pageable,
    search: String?,
    ascending: Boolean,
    categoryId: Long?,
  ): Page<DiscountDtoAmount> {
//    discountRepository.findAll()
//      .forEach {
//        it.updateIsActive()
//        discountRepository.save(it)
//      }

    return discountRepository.findGetListDiscountPage(pageable, search, ascending, categoryId)
  }

  @Transactional
  override fun updateDiscount(id: Int, request: DiscountRequest): DiscountDto {
    val discount = discountRepository.findByIdAndIsActiveTrueAndIsDeletedFalse(id).orElseThrow {
      throw NotFoundException("ไม่พบส่วนลด")
    }

    if (discountRepository.findByTitleAndIdNotAndIsActiveTrueAndIsDeletedFalse(
        request.title,
        id,
      ).isPresent
    ) {
      throw BadRequestException("ชื่อส่วนลดซ้ำ")
    }

    if (discountRepository.findByDiscountCodeAndIdNotAndIsActiveTrueAndIsDeletedFalse(
        request.discountCode,
        id
      ).isPresent
    ) {
      throw BadRequestException("โค้ดส่วนลดซำ้")
    }

    if (request.startDate == 0L || request.endDate == 0L) {
      throw BadRequestException("ไม่พบวันเริ่มใช้และวันหมดอายุของส่วนลด")
    }

    discount.title = request.title
    discount.discountCode = request.discountCode
    discount.percentage = request.percentage
    discount.maxDiscount = request.maxDiscount
    discount.minPrice = request.minPrice
    discount.maxUsage = request.maxUsage
    discount.condition = request.condition
    discount.description = request.description
    discount.discountCategoryId = request.discountCategoryId
    discount.startDate = Date(request.startDate * 1000)
    discount.endDate = Date(request.endDate * 1000)

    if (request.discountImage != discount.discountImage) {
      s3Service.deleteFile(discount.discountImage)
      val newFileUrl = s3Service.moveFile(request.discountImage).url
      discount.discountImage = newFileUrl
    }

    val newDiscount = discountRepository.save(discount)

    return newDiscount.toDiscountDto()
  }

  override fun deleteDiscount(id: Int): Boolean {
    val discount = discountRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบส่วนลด")
    }

    discount.isDeleted = true
    discountRepository.save(discount)

    return true
  }

  override fun retrieveDiscount(userId: Int, discountId: Int): Boolean {
    val discount =
      discountRepository.findByIdAndIsActiveTrueAndIsDeletedFalse(discountId).orElseThrow {
        throw NotFoundException("ไม่พบส่วนลด")
      }

    if (userDiscountRepository.countAllByDiscountId(discountId) >= discount.maxUsage) {
      throw BadRequestException("ส่วนลดถูกเก็บครบตามจำนวนแล้ว")
    }

    userDiscountRepository.findAllByUserIdAndDiscountId(userId, discountId).ifPresent() {
      throw BadRequestException("ส่วนลดนี้ถูกเก็บแล้ว")
    }

    val userDiscount = UserDiscount(
      userId = userId,
      discountId = discountId,
    )

    userDiscountRepository.save(userDiscount)

    return true
  }

  override fun listUserDiscount(
    pageable: Pageable,
    categoryId: Long?,
    userId: Int
  ): Page<UserDiscountDto> {
    return discountRepository.listUserDiscount(pageable, categoryId, userId)
  }

  override fun getDiscountById(discountId: Int): DiscountDtoAmount {
    discountRepository.findByIdAndIsActiveTrueAndIsDeletedFalse(discountId).orElseThrow {
      throw NotFoundException("ไม่พบส่วนลด")
    }

    return discountRepository.getDiscountById(discountId)
  }

  @Transactional
  override fun getDiscountByCode(userId: Int, code: String): DiscountDto {
    val discount =
      discountRepository.findByDiscountCodeAndIsActiveTrueAndIsDeletedFalse(code).orElseThrow {
        throw NotFoundException("ไม่พบส่วนลด")
      }

    userDiscountRepository.findAllByUserIdAndDiscountId(userId, discount.id).ifPresent {
      throw BadRequestException("ส่วนลถูกเก็บแล้ว")
    }

    val userDiscount = UserDiscount(
      userId = userId,
      discountId = discount.id,
    )

    userDiscountRepository.save(userDiscount)

    return discount.toDiscountDto()
  }

}