package com.lucablock.digiboxapi.discount.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.discountCategory.dto.DiscountCategoryDto
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class DiscountDto
@QueryProjection constructor(
  val id: Int,
  val title: String,
  val discountCode: String,
  val percentage: Int,
  val maxDiscount: Int,
  val minPrice: Int,
  val maxUsage: Int,
  val condition: String,
  val description: String,
  val discountCategory: DiscountCategoryDto? = null,
  val discountImage: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val startDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val endDate: Date,
  val isActive: Boolean,
)

data class DiscountDtoAmount
@QueryProjection constructor(
  val id: Int,
  val title: String,
  val discountCode: String,
  val percentage: Int,
  val maxDiscount: Int,
  val minPrice: Int,
  val maxUsage: Int,
  val condition: String,
  val description: String,
  val discountCategory: DiscountCategoryDto,
  val discountImage: String,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val startDate: Date,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  val endDate: Date,
  val isActive: Boolean,
  var isFull: Boolean? = false
)


data class UserDiscountDto
@QueryProjection constructor(
  val id: Int,
  val discount: DiscountDto,
  val isActive: Boolean,
)