package com.lucablock.digiboxapi.discount.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

class DiscountRequest {
  @NotBlank(message = "กรุณากรอกชื่อ")
  val title: String = ""

  @NotBlank(message = "กรุณากรอกรหัสส่วนลด")
  val discountCode: String = ""

  @NotNull(message = "กรุณาใส่เปอร์เซ็นต์")
  val percentage: Int = 0

  @NotNull(message = "กรุณาใส่ส่วนลดสูงสุด")
  val maxDiscount: Int = 0

  @NotNull(message = "กรุณาใส่ราคาขั้นต่ำ")
  val minPrice: Int = 0

  @NotNull(message = "กรุณาใส่จำนวนการใช้งานสูงสุด")
  val maxUsage: Int = 0

  @NotBlank(message = "กรุณากรอกเงื่อนไข")
  val condition: String = ""

  val description: String = ""

  @NotBlank(message = "กรุณาใส่ Url รูปภาพ")
  val discountImage: String = ""

  @NotNull(message = "กรุณาเลือกประเภทส่วนลด")
  val discountCategoryId: Long = 1

  @NotNull(message = "กรุณาใส่วันที่เริ่มต้น")
  val startDate: Long = 0

  @NotNull(message = "กรุณาใส่วันที่สิ้นสุด")
  val endDate: Long = 0
}


