package com.lucablock.digiboxapi.item.dto

data class CreateItemSpecialTechnicDto(
  val id: Int,
  val width: Int,
  val height: Int,
)
data class CreateItemDto(
  val width: Int,
  val length: Int,
  val height: Int,
  val amountId: Int,
  val printing: Int,
  val coatingId: Int,
  val specialTechnic: List<CreateItemSpecialTechnicDto>,
  val isArtwork: Boolean,
  val artworkUrl: String?,
  val productDemo: Int,
  val description: String?,
  val modelId: Int,
  val materialId: Int,
  val unitPrice: Double,
  val totalPrice: Double,
  val status: Int,
)
data class UpdateItemCartDto(
  val width: Int,
  val length: Int,
  val height: Int,
  val amountId: Int,
  val printing: Int,
  val coatingId: Int,
  val specialTechnic: List<CreateItemSpecialTechnicDto>,
  val isArtwork: Boolean,
  val artworkUrl: String?,
  val productDemo: Int,
  val description: String?,
  val modelId: Int,
  val materialId: Int,
  val unitPrice: Double,
  val totalPrice: Double,
  val status: Int,
)

