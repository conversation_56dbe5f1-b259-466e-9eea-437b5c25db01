package com.lucablock.digiboxapi.item.repository

import com.lucablock.digiboxapi.entity.Item
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ItemRepository : JpaRepository<Item, Int> {
  fun existsItemByCoatingId(coatingId: Int): Boolean
  fun findAllByMaterialId(materialId: Int): List<Item>
  fun existsItemByModelId(modelId: Int): Bo<PERSON>an
}