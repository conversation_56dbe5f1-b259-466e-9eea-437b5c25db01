package com.lucablock.digiboxapi.item.repository

import com.lucablock.digiboxapi.entity.ItemSpecialTechnic
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface ItemSpecialTechnicRepository : JpaRepository<ItemSpecialTechnic, Int> {
  @Transactional
  @Modifying
  @Query("delete from item_special_technic where item_id = :itemId", nativeQuery = true)
  fun deleteAllByItemId(itemId: Int): Any
  
  fun existsBySpecialTechnicId(specialTechnicId: Int): Boolean
}