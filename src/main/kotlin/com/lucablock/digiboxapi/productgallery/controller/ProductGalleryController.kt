package com.lucablock.digiboxapi.productgallery.controller

import com.lucablock.digiboxapi.coating.request.CoatingRequest
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.productgallery.service.ProductGalleryService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("api/product-gallery")
class ProductGalleryController {
  private val logger: Logger = LoggerFactory.getLogger(ProductGalleryController::class.java)

  @Autowired
  lateinit var productGalleryService: ProductGalleryService

  @PostMapping()
  fun uploadFile(
    @RequestParam("productId") productId: String,
    @RequestParam("files") files: List<MultipartFile>
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างข้อมูลเคลือบสำเร็จ",
          productGalleryService.uploadFile(productId.toLong(), files)
        )
      )
    } catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, "คำร้องขออัปโหลดไฟล์ไม่ถูกต้อง"))
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่พบข้อมูลสินค้า"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "อัปโหลดไฟล์ไม่สำเร็จ ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

  @DeleteMapping("/{id}")
  fun deleteFile(
    @PathVariable id: Long
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "ลบไฟล์สำเร็จ",
          productGalleryService.deleteFile(id)
        )
      )
    }catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, "ไม่พบข้อมูลไฟล์สินค้า"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ลบไฟล์ไม่สำเร็จ ข้อผิดพลาดเซิร์ฟเวอร์ภายใน"))
    }
  }

}