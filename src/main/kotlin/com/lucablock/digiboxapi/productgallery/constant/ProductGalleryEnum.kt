package com.lucablock.digiboxapi.productgallery.constant


enum class ProductGalleryEnum(val value: Int, val description: String) {
  IMAGE(1, "รูปภาพ"),
  VIDEO(2, "วีดีโอ");

  companion object {
    fun fromValue(value: Int): ProductGalleryEnum {
      return ProductGalleryEnum.entries.firstOrNull { it.value == value }
        ?: throw IllegalArgumentException("ไม่พบสถานะสำหรับ value: $value")
    }
  }
}