package com.lucablock.digiboxapi.productgallery.service

import com.lucablock.digiboxapi.productgallery.dto.ProductGalleryDto
import com.lucablock.digiboxapi.productgallery.request.ProductGalleryRequest
import org.springframework.web.multipart.MultipartFile

interface ProductGalleryService {
  fun uploadFile(productId : Long, files: List<MultipartFile>): List<ProductGalleryDto>
  fun deleteFile(id: Long) : <PERSON><PERSON>an
}