package com.lucablock.digiboxapi.productgallery.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.entity.ProductGallery
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.product.repository.ProductRepository
import com.lucablock.digiboxapi.productgallery.dto.ProductGalleryDto
import com.lucablock.digiboxapi.productgallery.repository.ProductGalleryRepository
import com.lucablock.digiboxapi.productgallery.service.ProductGalleryService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class ProductGalleryServiceImpl @Autowired constructor(
  private val productGalleryRepository: ProductGalleryRepository,
  private val productRepository: ProductRepository,
  private val s3Service: S3Service
): ProductGalleryService{
  @Transactional
  override fun uploadFile(productId: Long, files: List<MultipartFile>): List<ProductGalleryDto> {
    val product = productRepository.findById(productId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลสินค้า")
    }
    if (files.isEmpty()) {
      throw BadRequestException("กรุณาเลือกไฟล์")
    }

    val maxFileSize = 25 * 1024 * 1024 // 25 MB

    val savedProductGallery =
      files.filterNot { it.isEmpty }.map { file ->
      val type = if (file.contentType?.startsWith("image") == true) 1 else 2

      if (type == 2 && file.size > maxFileSize) {
        throw BadRequestException("ไฟล์ ${file.originalFilename} มีขนาดใหญ่เกินไป (ไม่เกิน 25 MB)")
      }

      val imageUrl = s3Service.uploadFile(file).url
      productGalleryRepository.save(
        ProductGallery(
          productId = product.id,
          imageUrl = imageUrl,
          fileType = type
        )
      )
    }
    return savedProductGallery.map { it.toProductGalleryDto() }
  }

  override fun deleteFile(id: Long): Boolean {
    val productGallery = productGalleryRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบไฟล์สินค้า")
    }
    s3Service.deleteFile(productGallery.imageUrl)
    productGalleryRepository.delete(productGallery)
    return true

  }
}