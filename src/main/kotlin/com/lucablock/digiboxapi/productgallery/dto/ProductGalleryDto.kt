package com.lucablock.digiboxapi.productgallery.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.lucablock.digiboxapi.entity.ProductGallery
import com.querydsl.core.annotations.QueryProjection
import java.util.*

data class ProductGalleryDto
@QueryProjection
constructor(
  val id: Long,
  val productId: Long,
  val imageUrl: String,
  val fileType: Int,
  val fileTypeEnum: String,

)