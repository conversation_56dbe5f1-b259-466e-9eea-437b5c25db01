package com.lucablock.digiboxapi.revision.service.impl

import com.lucablock.digiboxapi.entity.*
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.item.repository.ItemRepository
import com.lucablock.digiboxapi.item.repository.ItemSpecialTechnicRepository
import com.lucablock.digiboxapi.order.repository.OrderItemRepository
import com.lucablock.digiboxapi.order.repository.OrderRepository
import com.lucablock.digiboxapi.revision.dto.GetRevisionItemDto
import com.lucablock.digiboxapi.revision.repository.RevisionDraftRepository
import com.lucablock.digiboxapi.revision.repository.RevisionRepository
import com.lucablock.digiboxapi.revision.request.ConfirmRevisionRequest
import com.lucablock.digiboxapi.revision.request.UpdateItemRequest
import com.lucablock.digiboxapi.revision.service.RevisionService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.text.DecimalFormat
import java.util.*

@Service
class RevisionServiceImpl @Autowired internal constructor(
  private val revisionRepository: RevisionRepository,
  private val revisionDraftRepository: RevisionDraftRepository,
  private val orderRepository: OrderRepository,
  private val orderItemRepository: OrderItemRepository,
  private val itemRepository: ItemRepository,
  private val itemSpecialTechnicRepository: ItemSpecialTechnicRepository
) : RevisionService {

}