package com.lucablock.digiboxapi.revision.service.impl

import com.lucablock.digiboxapi.entity.QRevisionDraft
import com.lucablock.digiboxapi.revision.dto.*
import com.lucablock.digiboxapi.revision.repository.RevisionDraftRepositoryCustom
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.stereotype.Service

@Service
class RevisionDraftRepositoryImpl: RevisionDraftRepositoryCustom {
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private final val qRevisionDraft = QRevisionDraft.revisionDraft

  override fun getRevisionItems(id: Int): List<GetRevisionItemDto> {
    val query = queryFactory.select(qRevisionDraft)
      .from(qRevisionDraft)
      .where(qRevisionDraft.revisionId.eq(id))
      .orderBy(qRevisionDraft.id.asc())

    return query.fetch().map { list ->
      GetRevisionItemDto(
        list.item!!.id,
        list.item!!.width,
        list.item!!.length,
        list.item!!.height,
        RevisionPrintingDto(
          list.item!!.printing,
          list.item!!.printingPrice
        ),
        list.item!!.model!!.let {
          RevisionItemModelDto(
            it.id,
            it.name,
            it.imageUrl,
            it.productId
          ) },
        list.item!!.material?.let {
          RevisionItemMaterialDto(
            it.id,
            it.name,
//            it.gram,
            it.imageUrl,
            list.item!!.amount,
            list.item!!.amountUnitPrice
          ) },
        list.item!!.coating?.let {
          RevisionItemCoatingDto(
            it.id,
            it.name,
            it.imageUrl,
            it.price
          ) },
        list.item!!.specialTechnics.map {
          RevisionItemSpecialTechnicDto(
            it!!.specialTechnic!!.id,
            it.specialTechnic!!.name,
            it.specialTechnic!!.imageUrl,
//            it.specialTechnic!!.price,
            it.width,
            it.height,
          ) },
        RevisionArtworkDto(
          list.item!!.isArtwork,
          list.item!!.artworkUrl,
          list.item!!.artworkPrice,
        ),
        RevisionProductDemoDto(
          list.item!!.productDemo,
          list.item!!.productDemoPrice
        ),
        list.item!!.description,
        list.item!!.unitPrice,
        list.item!!.totalPrice,
        list.item!!.createdDate,
        list.item!!.modifiedDate
      )
    }
  }

  override fun getRevisionItemById(id: Int): GetRevisionItemDto {
    val query = queryFactory.select(qRevisionDraft).from(qRevisionDraft).where(qRevisionDraft.itemId.eq(id))
    return query.fetchOne().let { list ->
      GetRevisionItemDto(
        list!!.item!!.id,
        list.item!!.width,
        list.item!!.length,
        list.item!!.height,
        RevisionPrintingDto(
          list.item!!.printing,
          list.item!!.printingPrice
        ),
        list.item!!.model!!.let {
          RevisionItemModelDto(
            it.id,
            it.name,
            it.imageUrl,
            it.productId
          ) },
        list.item!!.material?.let {
          RevisionItemMaterialDto(
            it.id,
            it.name,
//            it.gram,
            it.imageUrl,
            list.item!!.amount,
            list.item!!.amountUnitPrice
          ) },
        list.item!!.coating?.let {
          RevisionItemCoatingDto(
            it.id,
            it.name,
            it.imageUrl,
            it.price
          ) },
        list.item!!.specialTechnics.map {
          RevisionItemSpecialTechnicDto(
            it!!.specialTechnic!!.id,
            it.specialTechnic!!.name,
            it.specialTechnic!!.imageUrl,
//            it.specialTechnic!!.price,
            it.width,
            it.height,
          ) },
        RevisionArtworkDto(
          list.item!!.isArtwork,
          list.item!!.artworkUrl,
          list.item!!.artworkPrice,
        ),
        RevisionProductDemoDto(
          list.item!!.productDemo,
          list.item!!.productDemoPrice
        ),
        list.item!!.description,
        list.item!!.unitPrice,
        list.item!!.totalPrice,
        list.item!!.createdDate,
        list.item!!.modifiedDate
      )
    }
  }
}