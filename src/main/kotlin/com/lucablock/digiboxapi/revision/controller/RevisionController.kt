package com.lucablock.digiboxapi.revision.controller

import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.response.HttpResponse
import com.lucablock.digiboxapi.revision.request.ConfirmRevisionRequest
import com.lucablock.digiboxapi.revision.request.UpdateItemRequest
import com.lucablock.digiboxapi.revision.service.RevisionService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api/revision")
class RevisionController {

}