package com.lucablock.digiboxapi.revision.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.io.Serializable
import java.util.*

data class GetRevisionItemDto
@QueryProjection
constructor(
  var id: Int,
  var width: Int,
  var length: Int,
  var height: Int,
  var printing: RevisionPrintingDto,
  var model: RevisionItemModelDto,
  var material: RevisionItemMaterialDto?,
  var coating: RevisionItemCoatingDto?,
  var specialTechnics: List<RevisionItemSpecialTechnicDto?>,
  var artwork: RevisionArtworkDto,
  var productDemo: RevisionProductDemoDto,
  var description: String? = null,
  var unitPrice: Double,
  var totalPrice: Double,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var createdDate: Date = Date(),
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  var modifiedDate: Date = Date()
) : Serializable

data class RevisionPrintingDto
@QueryProjection
constructor(
  var id: Int,
  var price: Double? = 0.0
) : Serializable

data class RevisionItemMaterialDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
//  var gram: Int,
  var imageUrl: String,
  var amount: Int,
  var price: Double
) : Serializable

data class RevisionItemModelDto
@QueryProjection
constructor(
  var id: Long,
  var name: String,
  var imageUrl: String,
  var productId: Long,
) : Serializable

data class RevisionItemCoatingDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
  var imageUrl: String,
  var price: Double
) : Serializable

data class RevisionItemSpecialTechnicDto
@QueryProjection
constructor(
  var id: Int,
  var name: String,
  var imageUrl: String,
//  var price: Double,
  var width: Int,
  var height: Int,

  ) : Serializable

data class RevisionArtworkDto
@QueryProjection
constructor(
  var status: Boolean,
  var url: String? = null,
  var price: Double? = 0.0,
) : Serializable

data class RevisionProductDemoDto
@QueryProjection
constructor(
  var id: Int,
  var price: Double? = 0.0
) : Serializable
