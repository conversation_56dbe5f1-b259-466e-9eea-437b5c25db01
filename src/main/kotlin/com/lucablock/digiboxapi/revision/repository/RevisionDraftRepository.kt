package com.lucablock.digiboxapi.revision.repository

import com.lucablock.digiboxapi.entity.RevisionDraft
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface RevisionDraftRepository: JpaRepository<RevisionDraft, Int>, RevisionDraftRepositoryCustom {
  @Transactional
  @Modifying
  @Query("delete from revision_draft where item_id = :itemId", nativeQuery = true)
  fun deleteByItemId(itemId: Int)

  @Query("select * from revision_draft where revision_id = :id", nativeQuery = true)
  fun findAllByRevisionId(id: Int): List<RevisionDraft>

  @Query("select * from revision_draft where item_id = :id", nativeQuery = true)
  fun findByItemId(id: Int): Any
}