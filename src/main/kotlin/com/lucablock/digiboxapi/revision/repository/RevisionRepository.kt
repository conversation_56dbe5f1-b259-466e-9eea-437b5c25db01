package com.lucablock.digiboxapi.revision.repository

import com.lucablock.digiboxapi.entity.Revision
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface RevisionRepository: JpaRepository<Revision, Int> {
  @Query("SELECT * FROM revision WHERE order_id = :orderId order by id DESC ", nativeQuery = true)
  fun findCurrentRevisionNumberByOrderId(orderId: Int): Revision?

  @Query("SELECT * FROM revision WHERE order_id = :id", nativeQuery = true)
  fun findByOrderId(id: Int): Revision
}