package com.lucablock.digiboxapi.revision.request

data class ConfirmRevisionRequest(
  val revisionItems: List<RevisionItemRequest>,
  val isTax: Boolean,
  val taxPayerType: Int? = null,
  val taxId: String? = null,
  val taxPayerName: String? = null,
  val phoneNumber: String? = null,
  val address: String? = null,
  val district: String? = null,
  val subDistrict: String? = null,
  val province: String? = null,
  val zipCode: String? = null,
  val email: String? = null
)

data class RevisionItemRequest(
  val revisionItemId: Int,
  val totalPrice: Double,
  val shipping: Int,
  val shippingCost: Double,
  val recipientName: String? = null,
  val phoneNumber: String? = null,
  val address: String? = null,
  val district: String? = null,
  val subDistrict: String? = null,
  val province: String? = null,
  val zipCode: String? = null,
  val email: String? = null,
)
