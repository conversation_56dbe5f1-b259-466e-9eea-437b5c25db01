package com.lucablock.digiboxapi.revision.request

import com.lucablock.digiboxapi.cart.request.ItemSpecialTechnicRequest
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull

class UpdateItemRequest(
  @NotNull(message = "ระบุรหัสสินค้า")
  @Min(value = 1, message = "รหัสสินค้าไม่ถูกต้อง")
  val modelId: Int = 0,

  @NotNull(message = "ระบุขนาดกว้าง")
  @Min(value = 20, message = "ความกว้างต้องมากกว่า 20 มม.")
  val width: Int = 0,

  @NotNull(message = "ระบุขนาดหนา")
  @Min(value = 20, message = "ความหนาต้องมากกว่า 20 มม.")
  val length: Int = 0,

  @NotNull(message = "ระบุขนาดสูง")
  @Min(value = 20, message = "ความสูงต้องมากกว่า 20 มม.")
  val height: Int = 0,

  @NotNull(message = "ระบุจำนวน")
  @Min(value = 100, message = "จำนวนเริ่มต้น 100 ชิ้น")
  val amount: Int = 0,

  @NotNull(message = "ระบุราคาสินค้า")
  @DecimalMin(value = "0.1", message = "ราคาเริ่มต้น 0.1 บาท")
  val unitPrice: Double = 0.0,

  @NotNull(message = "ระบุรหัสวัสดุ")
  @Min(value = 1, message = "รหัสวัสดุไม่ถูกต้อง")
  val materialId: Int = 0,

  @NotNull(message = "ระบุรหัสด้านพิมพ์")
  @Min(value = 1, message = "รหัสด้านพิมพ์ไม่ถูกต้อง")
  val printingId: Int = 0,

  @NotNull(message = "ระบุราคาด้านพิมพ์")
  val printingPrice: Double = 0.0,

  @NotNull(message = "ระบุรหัสการเคลือบ")
  @Min(value = 1, message = "รหัสการเคลือบไม่ถูกต้อง")
  val coatingId: Int = 0,

  @NotNull(message = "ระบุราคาการเคลือบ")
  val coatingPrice: Double = 0.0,

  @NotNull(message = "ระบุรหัสตัวอย่างสินค้า")
  @Min(value = 1, message = "รหัสตัวอย่างสินค้าไม่ถูกต้อง")
  val productDemoId: Int = 0,

  @NotNull(message = "ระบุราคาตัวอย่างสินค้า")
  val productDemoPrice: Double,

  val isArtwork: Boolean = false,
  val artworkUrl: String? = "",

  @NotNull(message = "ระบุราคาอาร์ตเวิร์ก")
  val artworkPrice: Double,
  val description: String? = "",

  val specialTechnic: List<ItemSpecialTechnicRequest?>,
)
