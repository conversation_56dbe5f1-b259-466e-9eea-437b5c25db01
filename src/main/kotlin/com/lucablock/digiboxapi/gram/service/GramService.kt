package com.lucablock.digiboxapi.gram.service

import com.lucablock.digiboxapi.gram.dto.GramDto
import com.lucablock.digiboxapi.gram.request.GramRequest

interface GramService {
  fun createGram(gramRequest: List<GramRequest>):List<GramDto>
  fun updateGram(id:Long,gramRequest: GramRequest):GramDto
  fun getAllGram():List<GramDto>
  fun getGramById(id:Long):GramDto
  fun deleteGram(id:Long):<PERSON><PERSON><PERSON>
}