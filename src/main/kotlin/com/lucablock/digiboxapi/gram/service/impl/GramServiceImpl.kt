package com.lucablock.digiboxapi.gram.service.impl

import com.lucablock.digiboxapi.entity.Gram
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.gram.dto.GramDto
import com.lucablock.digiboxapi.gram.repository.GramRepository
import com.lucablock.digiboxapi.gram.request.GramRequest
import com.lucablock.digiboxapi.gram.service.GramService
import com.lucablock.digiboxapi.materialconfig.repository.MaterialConfigRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class GramServiceImpl @Autowired constructor(
  private val gramRepository: GramRepository,
  private val materialConfigRepository: MaterialConfigRepository
): GramService{
  @Transactional
  override fun createGram(gramRequest: List<GramRequest>): List<GramDto> {
    val gsmList = gramRequest.map { it.gsm }
    val existingGrams = gramRepository.findAllByGsmIn(gsmList)
    if (existingGrams.isNotEmpty()) {
      throw BadRequestException(
        "มีขนาดแกรม ${existingGrams.map { it.gsm }.joinToString(", ")} อยู่แล้ว"
      )
    }
    val duplicateGram = gramRequest.groupBy { it.gsm }.filter { it.value.size > 1 }
    if (duplicateGram.isNotEmpty()) {
      throw BadRequestException("ขนาดแกรมที่กรอกซ้ำกัน")
    }

    val mmList = gramRequest.map { it.mm }
    val existingMM = gramRepository.findAllByMmIn(mmList)
    if (existingMM.isNotEmpty()) {
      throw BadRequestException(
        "มีขนาดมิลลิเมตร ${existingMM.map { it.mm }.joinToString(", ")} อยู่แล้ว"
      )
    }
    val duplicateMM = gramRequest.groupBy { it.mm }.filter { it.value.size > 1 }
    if (duplicateMM.isNotEmpty()) {
      throw BadRequestException("ขนาดมิลลิเมตรที่กรอกซ้ำกัน")
    }


    val grams = gramRequest.map {
      Gram(
        gsm = it.gsm,
        mm = it.mm
      )
    }
    val savedGram = gramRepository.saveAll(grams)
    return savedGram.map { it.toGramDto() }
  }

  override fun updateGram(id: Long, gramRequest: GramRequest): GramDto {
    gramRepository.existsGramByGsmAndIdNot(gramRequest.gsm,id).let {
      if (it){
        throw BadRequestException("มีขนาดแกรมนี้อยู่แล้ว")
      }
    }
    gramRepository.existsGramByMmAndIdNot(gramRequest.mm,id).let {
      if(it){
        throw NotFoundException("มีขนาดมิลลิเมตรนี้อยู่แล้ว")
      }
    }
    val gram = gramRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลแกรม")
    }

    gram.gsm = gramRequest.gsm
    gram.mm = gramRequest.mm
    val updateGram = gramRepository.save(gram)
    return updateGram.toGramDto()
  }

  override fun getAllGram(): List<GramDto> {
    return gramRepository.findAllByOrderByGsmAsc().map { it.toGramDto() }
  }

  override fun getGramById(id: Long): GramDto {
    val gram = gramRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลแกรม")
    }
    return gram.toGramDto()
  }

  override fun deleteGram(id: Long): Boolean {
    val gram = gramRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลแกรม")
    }
    val materialConfig = materialConfigRepository.findAllByGramId(id)
    if(materialConfig.isNotEmpty()){
      throw BadRequestException("ข้อมูลแกรมนี้ถูกใช้งานอยู่ ไม่สามารถลบได้")
    }
    gramRepository.delete(gram)
    return true
  }
}