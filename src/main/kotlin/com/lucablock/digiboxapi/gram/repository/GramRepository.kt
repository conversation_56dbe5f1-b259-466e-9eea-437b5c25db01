package com.lucablock.digiboxapi.gram.repository

import com.lucablock.digiboxapi.entity.Gram
import com.lucablock.digiboxapi.entity.MaterialConfig
import com.lucablock.digiboxapi.gram.dto.GramDto
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface GramRepository : JpaRepository<Gram, Long> {
  fun existsGramByGsm(gsm: Int): Boolean
  fun findAllByOrderByGsmAsc():List<Gram>
  fun findAllByGsmIn(gsm: List<Int>): List<Gram>
  fun existsGramByGsmAndIdNot(gsm: Int,id:Long):Boolean
  fun existsGramByIdIn(ids: MutableCollection<Long>): Boolean
  fun findAllByMmIn(mms: List<Double>): MutableList<Gram>
  fun existsGramByMmAndIdNot(mm: Double, id: Long): Boolean
}