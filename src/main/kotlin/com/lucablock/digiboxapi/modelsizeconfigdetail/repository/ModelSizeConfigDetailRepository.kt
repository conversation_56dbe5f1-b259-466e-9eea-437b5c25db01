package com.lucablock.digiboxapi.modelsizeconfigdetail.repository

import com.lucablock.digiboxapi.entity.ModelSizeConfigDetail
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ModelSizeConfigDetailRepository : JpaRepository<ModelSizeConfigDetail, Long> {
  fun findByPrintingConfigId(printingConfigId: Long): MutableList<ModelSizeConfigDetail>
  fun findAllByPrintingConfigId(printingConfigId: Long): MutableList<ModelSizeConfigDetail>
  fun deleteAllByPrintingConfigIdIn(printingConfigIds: List<Long>)
  fun findAllByPrintingConfigIdIn(printingConfigIds: List<Long>): MutableList<ModelSizeConfigDetail>
//  fun deleteAllByModelSizeConfigIdIn(modelSizeConfigIds: List<Long>)
}