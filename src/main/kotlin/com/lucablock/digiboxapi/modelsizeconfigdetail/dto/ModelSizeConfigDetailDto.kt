package com.lucablock.digiboxapi.modelsizeconfigdetail.dto

import com.lucablock.digiboxapi.ModelSizeConfigCoating.dto.ModelSizeConfigCoatingDto
import com.lucablock.digiboxapi.entity.ModelSizeConfigCoating
import com.querydsl.core.annotations.QueryProjection

data class ModelSizeConfigDetailDto
@QueryProjection constructor(
  val id : Long,
  val amount : Int,
  val price : Double,
  val period : Int,
//  val printingConfigId : Long
  val modelSizeConfigCoating: List<ModelSizeConfigCoatingDto>? = mutableListOf()
)
data class ModelSizeConfigDetailListDto
@QueryProjection constructor(
  val id : Long,
  val amount : Int,
  val price : Double,
  val period : Int
)