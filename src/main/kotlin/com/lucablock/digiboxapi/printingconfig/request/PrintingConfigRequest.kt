package com.lucablock.digiboxapi.printingconfig.request

import com.lucablock.digiboxapi.modelsizeconfig.request.ModelSizeConfigDetailRequest
import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive

class PrintingConfigRequests {
  @NotNull(message = "กรุณากรอกรหัสการพิมพ์")
  @Positive(message = "กรุณากรอกรหัสการพิมพ์")
  var printingId : Long = 0
//  @NotNull(message = "กรุณากรอกขนาดโมเดลสินค้า")
//  var modelSizeConfigId : Long = 0
  @Valid
  @NotEmpty(message = "กรุณากรอกรายละเอียดการพิมพ์")
  var modelSizeConfigDetail : List<ModelSizeConfigDetailRequest> = mutableListOf()
//  @Valid
//  @NotEmpty(message = "กรุณากรอกการเคลือบ")
//  var modelSizeConfigCoating : List<ModelSizeConfigCoatingRequest> = mutableListOf()
}

class ModelSizeConfigCoatingRequest {
  @NotNull(message = "กรุณากรอกรหัสการเคลือบ")
  var coatingId: Long = 0
  @NotNull(message = "กรุณากรอกรหัสการพิมพ์")
  var printingConfigId : Long = 0
}