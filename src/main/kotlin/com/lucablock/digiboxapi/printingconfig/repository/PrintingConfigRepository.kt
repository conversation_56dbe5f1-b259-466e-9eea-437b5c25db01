package com.lucablock.digiboxapi.printingconfig.repository

import com.lucablock.digiboxapi.entity.PrintingConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface PrintingConfigRepository : JpaRepository<PrintingConfig, Long> {
  fun findAllByModelSizeConfigId(modelSizeConfigId: Long): MutableList<PrintingConfig>
  fun deletePrintingConfigsByModelSizeConfigId(modelSizeConfigId: Long)
  fun deleteAllPrintingConfigsByModelSizeConfigId(modelSizeConfigId: Long)
  fun deleteAllPrintingConfigsByModelSizeConfigIdIn(modelSizeConfigIds: List<Long>)
  fun findAllByPrintingIdAndModelSizeConfigId(printingId: Long, modelSizeConfigId: Long): MutableList<PrintingConfig>
  fun existsByPrintingIdInAndModelSizeConfigId(printingId: List<Long>, modelSizeConfigId: Long): Boolean
  fun findAllByModelSizeConfigIdIn(modelSizeConfigIds: List<Long>): MutableList<PrintingConfig>
  fun findAllByPrintingId(printingId: Long): MutableList<PrintingConfig>
}