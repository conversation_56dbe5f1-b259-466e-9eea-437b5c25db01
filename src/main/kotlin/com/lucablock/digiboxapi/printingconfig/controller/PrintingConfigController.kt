package com.lucablock.digiboxapi.printingconfig.controller

import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.model.controller.ModelController
import com.lucablock.digiboxapi.model.request.ModelPriceRequest
import com.lucablock.digiboxapi.printingconfig.request.PrintingConfigRequests
import com.lucablock.digiboxapi.printingconfig.service.PrintingConfigService
import com.lucablock.digiboxapi.response.HttpResponse
import jakarta.validation.Valid
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("api")
class PrintingConfigController {
  private val logger: Logger = LoggerFactory.getLogger(PrintingConfigController::class.java)
  @Autowired lateinit var printingConfigService: PrintingConfigService

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PostMapping("/admin/printing-config/{modelSizeConfigId}")
  fun createPrintingConfig(
    @PathVariable modelSizeConfigId: Long,
    @Valid @RequestBody printingConfigRequests: List<PrintingConfigRequests>,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "สร้างการตั้งค่าระบบพิมพ์สำเร็จ",
          printingConfigService.createPrintingConfig(modelSizeConfigId,printingConfigRequests)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูล"))
    }catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลระบบพิมพ์ซ้ำ"))
    }catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน กรุณาลองอีกครั้ง"))
    }
  }

  @PreAuthorize("hasAnyRole('ADMIN','SUPER_ADMIN')")
  @PutMapping("/admin/printing-config/{modelSizeConfigId}")
  fun updatePrintingConfig(
    @PathVariable modelSizeConfigId: Long,
    @Valid @RequestBody printingConfigRequests: List<PrintingConfigRequests>,
  ): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "บันทึกการตั้งค่าระบบพิมพ์สำเร็จ",
          printingConfigService.updatePrintingConfig(modelSizeConfigId,printingConfigRequests)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message?: "ไม่พบข้อมูล"))
    }catch (e: BadRequestException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(HttpResponse(false, e.message ?: "ข้อมูลระบบพิมพ์ซ้ำ"))
    }catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, "ข้อผิดพลาดเซิร์ฟเวอร์ภายใน กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/printing-config/{modelSizeConfigId}")
  fun findByModelSizeConfigId(
    @PathVariable modelSizeConfigId: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          printingConfigService.findByModelSizeConfigId(modelSizeConfigId)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลระบบพิมพ์นี้"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }

  @GetMapping("/web/printing-config/id/{id}")
  fun findPrintingConfigById(
    @PathVariable id: Long): ResponseEntity<Any> {
    return try {
      ResponseEntity.ok(
        HttpResponse(
          true,
          "แสดงข้อมูลสำเร็จ",
          printingConfigService.findPrintingConfigById(id)
        )
      )
    } catch (e: NotFoundException) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(HttpResponse(false, e.message ?: "ไม่มีข้อมูลระบบพิมพ์นี้"))
    } catch (e: Exception) {
      logger.error(e.message)
      ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(HttpResponse(false, e.message ?: "ไม่สามารถแสดงข้อมูลได้ กรุณาลองอีกครั้ง"))
    }
  }
}