package com.lucablock.digiboxapi.printingconfig.service

import com.lucablock.digiboxapi.modelsizeconfig.request.PrintingConfigRequest
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigDto
import com.lucablock.digiboxapi.printingconfig.request.PrintingConfigRequests

interface PrintingConfigService {
  fun createPrintingConfig(
    modelSizeConfigId : Long,
    printingConfigRequest: List<PrintingConfigRequests>
  ):List<PrintingConfigDto>
  fun updatePrintingConfig(
    modelSizeConfigId : Long,
    printingConfigRequest: List<PrintingConfigRequests>
  ):List<PrintingConfigDto>
  fun findByModelSizeConfigId(modelSizeConfigId : Long):List<PrintingConfigDto>
  fun findPrintingConfigById(id:Long):PrintingConfigDto
}