package com.lucablock.digiboxapi.printingconfig.service.impl

import com.lucablock.digiboxapi.ModelSizeConfigCoating.repository.ModelSizeConfigCoatingRepository
import com.lucablock.digiboxapi.coating.repository.CoatingRepository
import com.lucablock.digiboxapi.entity.ModelSizeConfigCoating
import com.lucablock.digiboxapi.entity.ModelSizeConfigDetail
import com.lucablock.digiboxapi.entity.PrintingConfig
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.modelsizeconfig.repository.ModelSizeConfigRepository
import com.lucablock.digiboxapi.modelsizeconfigdetail.repository.ModelSizeConfigDetailRepository
import com.lucablock.digiboxapi.printing.repository.PrintingRepository
import com.lucablock.digiboxapi.printingconfig.dto.PrintingConfigDto
import com.lucablock.digiboxapi.printingconfig.repository.PrintingConfigRepository
import com.lucablock.digiboxapi.printingconfig.request.PrintingConfigRequests
import com.lucablock.digiboxapi.printingconfig.service.PrintingConfigService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class PrintingConfigServiceImpl @Autowired constructor(
  private val printingConfigRepository: PrintingConfigRepository,
  private val modelSizeConfigRepository: ModelSizeConfigRepository,
  private val modelSizeConfigDetailRepository: ModelSizeConfigDetailRepository,
  private val modelSizeConfigCoatingRepository: ModelSizeConfigCoatingRepository,
  private val printingRepository: PrintingRepository,
  private val coatingRepository: CoatingRepository
) : PrintingConfigService {
  @Transactional
  override fun createPrintingConfig(
    modelSizeConfigId: Long,
    printingConfigRequest: List<PrintingConfigRequests>
  ): List<PrintingConfigDto> {
    val modelSizeConfig = modelSizeConfigRepository.findById(modelSizeConfigId)
      .orElseThrow { throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }

    val printingId = printingConfigRequest.map { it.printingId }
    if (printingId.size != printingId.toSet().size) {
      throw BadRequestException("ระบบพิมพ์ซ้ำกัน กรุณาเลือกระบบพิมพ์อื่น")
    }
    val existingId = printingRepository.findAllById(printingId)
      .map { it.id }
    val missingId = printingId.filterNot { it in existingId }
    if (missingId.isNotEmpty()) {
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์")
    }
    printingConfigRepository.existsByPrintingIdInAndModelSizeConfigId(printingId, modelSizeConfig.id).let {
      if (it) {
        throw BadRequestException("ข้อมูลวัสดุนี้มีระบบพิมพ์นี้แล้ว")
      }
    }

    val savedPrintingConfig = printingConfigRequest.map { req ->
      val printingConfig = printingConfigRepository.save(
        PrintingConfig(
          printingId = req.printingId,
          modelSizeConfigId = modelSizeConfig.id
        )
      )
      val details = req.modelSizeConfigDetail.map { detailReq ->
        val detail = modelSizeConfigDetailRepository.save(
            ModelSizeConfigDetail(
            printingConfigId = printingConfig.id,
            amount = detailReq.amount,
            period = detailReq.period,
            price = detailReq.price
          )
        )
        val coatings = detailReq.modelSizeConfigCoating.map { coating ->
          val coatingObj = coatingRepository.findById(coating).orElseThrow{
            throw NotFoundException("ไม่พบข้อมูลเคลือบ")
          }
          modelSizeConfigCoatingRepository.save(
            ModelSizeConfigCoating(
              modelSizeConfigDetailId = detail.id,
              coatingId = coating.toLong(),
              coating = coatingObj
            )
          )
        }
        detail.modelSizeConfigCoating = coatings
        detail
      }
      modelSizeConfigDetailRepository.saveAll(details)


      (details.map { it.modelSizeConfigCoating })
      printingConfig.modelSizeConfigDetail = details
      printingConfig
    }
    val printingMap = printingRepository.findAllById(savedPrintingConfig.map { it.printingId })
      .associateBy { it.id }
    val modelSizeMap = modelSizeConfigRepository.findAllById(savedPrintingConfig.map { it.modelSizeConfigId })
      .associateBy { it.id }
    return savedPrintingConfig.map {
      PrintingConfigDto(
        id = it.id,
        printing = printingMap[it.printingId]?.toPrintingListDto(),
        modelSizeConfig = modelSizeMap[it.modelSizeConfigId]?.toModelSizeConfigMaterialDto(),
        modelSizeConfigDetail = it.modelSizeConfigDetail.map { detail -> detail.toModelSizeConfigDetailDto() },
      )
    }
  }

  @Transactional
  override fun updatePrintingConfig(
    modelSizeConfigId: Long,
    printingConfigRequest: List<PrintingConfigRequests>
  ): List<PrintingConfigDto> {
    val modelSizeConfig = modelSizeConfigRepository.findById(modelSizeConfigId)
      .orElseThrow { throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้") }
    val printingConfigs = printingConfigRepository.findAllByModelSizeConfigId(modelSizeConfigId)
    val printingConfigId = printingConfigs.map { it.id }
    val configDetail = modelSizeConfigDetailRepository.findAllByPrintingConfigIdIn(printingConfigId)
    modelSizeConfigCoatingRepository.deleteAllByModelSizeConfigDetailIdIn(configDetail.map { it.id })
    modelSizeConfigDetailRepository.deleteAllByPrintingConfigIdIn(printingConfigId)
    printingConfigRepository.deleteAllById(printingConfigId)

    val printingId = printingConfigRequest.map { it.printingId }
    if (printingId.size != printingId.toSet().size) {
      throw BadRequestException("ระบบพิมพ์ซ้ำกัน กรุณาเลือกระบบพิมพ์อื่น")
    }
    val existingPrintingIds = printingRepository.findAllById(printingId).map { it.id }
    val missingIds = printingId.filterNot { it in existingPrintingIds }
    if (missingIds.isNotEmpty()) {
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์")
    }
    printingConfigRepository.existsByPrintingIdInAndModelSizeConfigId(printingId, modelSizeConfig.id).let {
      if (it) {
        throw BadRequestException("ข้อมูลวัสดุนี้มีระบบพิมพ์นี้แล้ว")
      }
    }

    val savedPrintingConfig = printingConfigRequest.map { req ->
      val printingConfig = printingConfigRepository.save(
        PrintingConfig(
          printingId = req.printingId,
          modelSizeConfigId = modelSizeConfig.id
        )
      )
      val details = req.modelSizeConfigDetail.map { detailReq ->
        val detail = modelSizeConfigDetailRepository.save(
          ModelSizeConfigDetail(
            printingConfigId = printingConfig.id,
            amount = detailReq.amount,
            period = detailReq.period,
            price = detailReq.price
          )
        )
        val coating = detailReq.modelSizeConfigCoating.map { coating ->
          val coatings = coatingRepository.findById(coating).orElseThrow{
            throw NotFoundException("ไม่พบข้อมูลวัสดุ")
          }
          modelSizeConfigCoatingRepository.save(
            ModelSizeConfigCoating(
              modelSizeConfigDetailId = detail.id,
              coatingId = coating.toLong(),
              coating = coatings
            )
          )
        }
        detail.modelSizeConfigCoating = coating
        detail
      }
//      modelSizeConfigDetailRepository.saveAll(details)
      printingConfig.modelSizeConfigDetail = details
      printingConfig
    }
    val printingMap = printingRepository.findAllById(savedPrintingConfig.map { it.printingId })
      .associateBy { it.id }
    val modelSizeMap = modelSizeConfigRepository.findAllById(savedPrintingConfig.map { it.modelSizeConfigId })
      .associateBy { it.id }
    return savedPrintingConfig.map {
      PrintingConfigDto(
        id = it.id,
        printing = printingMap[it.printingId]?.toPrintingListDto(),
        modelSizeConfig = modelSizeMap[it.modelSizeConfigId]?.toModelSizeConfigMaterialDto(),
        modelSizeConfigDetail = it.modelSizeConfigDetail.map { detail -> detail.toModelSizeConfigDetailDto() },
      )
    }
  }

  override fun findByModelSizeConfigId(modelSizeConfigId: Long): List<PrintingConfigDto> {
    modelSizeConfigRepository.findById(modelSizeConfigId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลโมเดลสินค้านี้")
    }
    val printingConfig = printingConfigRepository.findAllByModelSizeConfigId(modelSizeConfigId)
    return printingConfig.map { it.toPrintingConfigDto() }
  }

  override fun findPrintingConfigById(id: Long): PrintingConfigDto {
    val printing = printingConfigRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระบบพิมพ์")
    }
    return printing.toPrintingConfigDto()
  }
}