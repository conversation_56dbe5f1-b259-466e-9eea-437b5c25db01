package com.lucablock.digiboxapi.printingconfig.dto

import com.lucablock.digiboxapi.ModelSizeConfigCoating.dto.ModelSizeConfigCoatingDto
import com.lucablock.digiboxapi.entity.ModelSizeConfigCoating
import com.lucablock.digiboxapi.entity.ModelSizeConfigDetail
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigListDto
import com.lucablock.digiboxapi.modelsizeconfig.dto.ModelSizeConfigMaterialDto
import com.lucablock.digiboxapi.modelsizeconfigdetail.dto.ModelSizeConfigDetailDto
import com.lucablock.digiboxapi.printing.dto.PrintingDto
import com.lucablock.digiboxapi.printing.dto.PrintingListDto
import com.querydsl.core.annotations.QueryProjection

data class PrintingConfigListDto
@QueryProjection constructor(
  val id : Long,
  val printingId : Long,
  val printingName : String?,
  val modelSizeConfigId : Long
)
data class PrintingConfigDto
@QueryProjection constructor(
  val id : Long,
  val modelSizeConfig : ModelSizeConfigMaterialDto?,
  val printing : PrintingListDto?,
  val modelSizeConfigDetail : List<ModelSizeConfigDetailDto>,
//  val modelSizeConfigCoating : List<ModelSizeConfigCoatingDto>,
)
data class PrintingConfigModelSizeDto
@QueryProjection constructor(
  val id : Long,
  val printing : PrintingListDto?,
  val modelSizeConfigDetail : List<ModelSizeConfigDetailDto>,
//  val modelSizeConfigCoating : List<ModelSizeConfigCoatingDto>,
)