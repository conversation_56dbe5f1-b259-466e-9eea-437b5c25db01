package com.lucablock.digiboxapi.customerlevel.repository

import com.lucablock.digiboxapi.entity.CustomerLevelConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CustomerLevelConfigRepository : JpaRepository<CustomerLevelConfig,Long> {
  fun findAllByCustomerLevelId(customerLevelId: Long): MutableList<CustomerLevelConfig>
  fun findAllByCustomerLevelIdAndUserIdAndIsDeletedFalse(customerLevelId: Long, userId: Long): MutableList<CustomerLevelConfig>
  fun findAllCustomerLevelConfigByUserId(userId: Long): MutableList<CustomerLevelConfig>
  fun findByCustomerLevelIdAndUserIdAndIsDeletedTrue(customerLevelId: Long, userId: Long): CustomerLevelConfig?
  fun findByUserIdAndIsDeletedFalse(userId: Long): MutableList<CustomerLevelConfig>
}