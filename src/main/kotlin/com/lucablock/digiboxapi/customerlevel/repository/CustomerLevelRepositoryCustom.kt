package com.lucablock.digiboxapi.customerlevel.repository

import com.lucablock.digiboxapi.customerlevel.dto.CustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CustomerLevelRepositoryCustom {
//  fun getPageUserByCustomerLevel(
//    pageable: Pageable,
//    ascending: Boolean,
//    search: String?,
//    customerLevelId : Long?,
//  ): Page<UserCustomerLevelDto>

  fun getPageUserByLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    customerLevelId : Long?,
  ): Page<UserCustomerLevelDto>
  fun getPageCustomerLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?,
  ): Page<CustomerLevelDto>
}