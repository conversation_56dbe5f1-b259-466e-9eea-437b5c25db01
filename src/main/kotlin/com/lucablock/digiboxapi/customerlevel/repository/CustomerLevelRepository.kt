package com.lucablock.digiboxapi.customerlevel.repository

import com.lucablock.digiboxapi.entity.CustomerLevel
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CustomerLevelRepository : JpaRepository<CustomerLevel, Long> ,CustomerLevelRepositoryCustom{
  fun existsByNameContainsIgnoreCase(name: String): Boolean
  fun existsByNameContainsIgnoreCaseAndIdNot(name: String, id: Long): Boolean
  fun findAllByIsActiveTrue(): List<CustomerLevel>

}