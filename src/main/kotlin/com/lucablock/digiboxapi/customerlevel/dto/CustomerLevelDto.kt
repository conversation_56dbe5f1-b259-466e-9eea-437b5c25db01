package com.lucablock.digiboxapi.customerlevel.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.querydsl.core.annotations.QueryProjection
import java.time.LocalDateTime
import java.util.*

data class CustomerLevelDto
@QueryProjection constructor(
  val id : Long,
  val name : String,
  val description : String?,
  val imageUrl : String,
  val isActive : Boolean,
  val minPurchaseAmount : Double?,
  val minOrderCount : Int?,
)

data class UserCustomerLevelDto
@QueryProjection constructor(
  val id : Long,
  val name : String?,
  val email : String?,
  val customerLevelId : Long?,
  val customerLevelName : String?,
  val customerLevelImage : String?,
  val orderCount : Int,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
  val customerLevelDate : LocalDateTime? = null,
)

data class UserCustomerLevelListDto
@QueryProjection constructor(
  val id : Long,
  val userId : Long,
  val name : String?,
  val customerLevelId : Long?,
  val customerLevelName : String?,
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
  val createDate : Date = Date(),
  val isDeleted : Boolean
)
data class UserCustomerLevelNewListDto
@QueryProjection constructor(
  val userId : Long,
  val name : String?,
  val customerLevelId : Long?,
  val customerLevelName : String?,
)

data class CustomerAddressDto(
  val name: String,
  val phoneNumber: String,
  val address: String,
  val zipCode: String,
  val province: String,
  val district: String,
  val subDistrict: String,
  val isTax: Boolean,
  val taxPayerType: Int? = null,
  val taxId: String? = null,
  val email: String? = null,
)

data class AdminCustomerLevelDto
@QueryProjection constructor(
  val id: Long,
  val name: String?,
  val imageUrl: String?,
  val customerLevelId: Long?,
  val customerLevelName: String?,
  val phoneNumber: String?,
  val email: String?,
  val lineID: String?,
  val taxAddress: List<CustomerAddressDto>,
  val shippingAddress: List<CustomerAddressDto>
)