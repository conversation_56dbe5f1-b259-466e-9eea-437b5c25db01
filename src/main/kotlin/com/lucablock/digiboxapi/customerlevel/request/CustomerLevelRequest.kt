package com.lucablock.digiboxapi.customerlevel.request

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

class CustomerLevelRequest (
  @field:NotBlank(message = "กรุณาระบุชื่อระดับลูกค้า")
  val name : String,
  val description : String? = null,
  @field:NotBlank(message = "กรุณาระบุรูประดับลูกค้า")
  val imageUrl : String ,
  val minPurchaseAmount : Double? = null,
  val minOrderCount : Int? = null,
  @JsonProperty("isActive")
  val isActive : Boolean = true,
)