package com.lucablock.digiboxapi.customerlevel.service.impl

import com.lucablock.digiboxapi.awss3.service.S3Service
import com.lucablock.digiboxapi.customerlevel.dto.AdminCustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.CustomerAddressDto
import com.lucablock.digiboxapi.customerlevel.dto.CustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelListDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelNewListDto
import com.lucablock.digiboxapi.customerlevel.repository.CustomerLevelConfigRepository
import com.lucablock.digiboxapi.customerlevel.repository.CustomerLevelRepository
import com.lucablock.digiboxapi.customerlevel.request.CustomerLevelRequest
import com.lucablock.digiboxapi.customerlevel.service.CustomerLevelService
import com.lucablock.digiboxapi.entity.CustomerLevel
import com.lucablock.digiboxapi.entity.CustomerLevelConfig
import com.lucablock.digiboxapi.exception.BadRequestException
import com.lucablock.digiboxapi.exception.NotFoundException
import com.lucablock.digiboxapi.user.repository.UserRepository
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class CustomerLevelServiceImpl @Autowired constructor(
  val customerLevelRepository: CustomerLevelRepository,
  private val s3Service: S3Service,
  private val customerLevelConfigRepository: CustomerLevelConfigRepository,
  private val userRepository: UserRepository
) : CustomerLevelService {
  @Transactional
  override fun createCustomerLevel(customerLevelRequest: CustomerLevelRequest): CustomerLevelDto {
    customerLevelRepository.existsByNameContainsIgnoreCase(customerLevelRequest.name).let {
      if (it) {
        throw BadRequestException("มีระดับผู้ใช้นี้แล้ว")
      }
    }
    val urlFile = try {
      s3Service.moveFile(customerLevelRequest.imageUrl).url
    }catch (e: Exception) {
      // TODO: TRANSACTION ROLLBACK - If S3 upload fails, the customer level entity
      // is already created in memory but not yet saved. Consider using @Transactional
      // to ensure atomicity between database save and S3 upload
      throw BadRequestException("ไม่สามารถอัพโหลดไฟล์ได้ กรุณาลองอีกครั้ง")
    }

    val customerLevel = CustomerLevel(
      name = customerLevelRequest.name,
      description = customerLevelRequest.description,
      imageUrl = urlFile,
      minPurchaseAmount = customerLevelRequest.minPurchaseAmount,
      minOrderCount = customerLevelRequest.minOrderCount,
      isActive = customerLevelRequest.isActive,
    )
    val savedCustomerLevel = customerLevelRepository.save(customerLevel)
    return savedCustomerLevel.toCustomerLevelDto()
  }

  override fun updateCustomerLevel(id: Long, customerLevelRequest: CustomerLevelRequest): CustomerLevelDto {
    val customerLevel = customerLevelRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระดับลูกค้า")
    }
    customerLevelRepository.existsByNameContainsIgnoreCaseAndIdNot(customerLevelRequest.name, id).let {
      if (it) {
        throw BadRequestException("มีระดับผู้ใช้นี้แล้ว")
      }
    }
    customerLevel.name = customerLevelRequest.name
    customerLevel.description = customerLevelRequest.description
    customerLevel.minPurchaseAmount = customerLevelRequest.minPurchaseAmount
    customerLevel.minOrderCount = customerLevelRequest.minOrderCount
    customerLevel.isActive = customerLevelRequest.isActive

    try {
      if (customerLevelRequest.imageUrl != customerLevel.imageUrl) {
        val newFileUrl = s3Service.moveFile(customerLevelRequest.imageUrl).url
        val oldFileUrl = customerLevel.imageUrl
        customerLevel.imageUrl = newFileUrl
        s3Service.deleteFile(oldFileUrl)
      }
    }catch (e: Exception) {
      // TODO: FILE UPLOAD ERROR HANDLING - If S3 operations fail during update,
      // the old file might be deleted but new file upload failed. Need proper rollback mechanism
      // or consider using S3 versioning to prevent data loss
      throw BadRequestException("ไม่สามารถอัพโหลดไฟล์ได้ กรุณาลองอีกครั้ง")
    }

    val savedCustomerLevel = customerLevelRepository.save(customerLevel)
    return savedCustomerLevel.toCustomerLevelDto()
  }

  override fun getAllCustomerLevel(): List<CustomerLevelDto> {
    return customerLevelRepository.findAllByIsActiveTrue().map { it.toCustomerLevelDto() }
  }

  override fun getCustomerLevelById(id: Long): CustomerLevelDto {
    val customerLevel = customerLevelRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระดับลูกค้า")
    }
    return customerLevel.toCustomerLevelDto()
  }

  override fun getPageCustomerLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?
  ): Page<CustomerLevelDto> {
    return customerLevelRepository.getPageCustomerLevel(pageable, ascending, search, isActive)
  }

  override fun deleteCustomerLevel(id: Long): Boolean {
    val customerLevel = customerLevelRepository.findById(id).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระดับลูกค้า")
    }
    val users = userRepository.findByCustomerLevelId(customerLevel.id)
    if(users.isNotEmpty()){
      throw BadRequestException("ไม่สามารถลบข้อมูลได้ เนื่องจากกำลังถูกใช้งานอยู่")
    }
    s3Service.deleteFile(customerLevel.imageUrl)
    customerLevelRepository.delete(customerLevel)
    return true
  }

  override fun addUserLevel(customerLevelId: Long, userId: Long): UserCustomerLevelNewListDto {
    val customerLevel = customerLevelRepository.findById(customerLevelId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลระดับลูกค้า")
    }
    val user = userRepository.findById(userId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลผู้ใช้")
    }
    userRepository.existsByIdAndCustomerLevelId(userId,customerLevelId).let {
      if (it) {
        throw BadRequestException("ลูกค้าท่านนี้มีระดับผู้ใช้นี้อยู่แล้ว")
      }
    }
    user.customerLevelId = customerLevel.id
    user.customerLevel = customerLevel
    user.customerLevelDate = LocalDateTime.now()
    val saveUser = userRepository.save(user)
    return UserCustomerLevelNewListDto(
      userId = saveUser.id,
      name = saveUser.fullName,
      customerLevelId = saveUser.customerLevelId,
      customerLevelName = saveUser.customerLevel?.name
    )
  }

//  override fun getPageUserByCustomerLevel(
//    pageable: Pageable,
//    ascending: Boolean,
//    search: String?,
//    customerLevelId: Long?
//  ): Page<UserCustomerLevelDto> {
//    return customerLevelRepository.getPageUserByCustomerLevel(
//      pageable, ascending, search, customerLevelId
//    )
//  }

//  @Transactional
//  // TODO: BUSINESS LOGIC - ฟังก์ชันนี้ซับซ้อน ควรแยกเป็นฟังก์ชันย่อย ๆ เพื่อให้อ่านง่ายขึ้น
//  override fun addUserCustomerLevel(customerLevelId: Long, userId: Long): UserCustomerLevelListDto {
//    val customerLevel = customerLevelRepository.findById(customerLevelId).orElseThrow {
//      throw NotFoundException("ไม่พบข้อมูลระดับลูกค้า")
//    }
//    val user = userRepository.findById(userId).orElseThrow {
//      throw NotFoundException("ไม่พบข้อมูลผู้ใช้")
//    }
//    customerLevelConfigRepository.findAllByCustomerLevelIdAndUserIdAndIsDeletedFalse(customerLevelId, userId).let {
//      if (it.isNotEmpty()) {
//        throw BadRequestException("ลูกค้าท่านนี้มีระดับผู้ใช้นี้อยู่แล้ว")
//      }
//    }
//    // TODO: BUSINESS LOGIC - ทำ soft delete ทั้งหมดก่อนจะสร้างใหม่
//    // อาจควรตรวจสอบว่าจำเป็นต้อง soft delete ทั้งหมดหรือไม่
//    val configUser = customerLevelConfigRepository.findAllCustomerLevelConfigByUserId(userId)
//    if (configUser.isNotEmpty()) {
//      configUser.map { it.isDeleted = true }
//      customerLevelConfigRepository.saveAll(configUser)
//    }
//    val oldConfig = customerLevelConfigRepository.findByCustomerLevelIdAndUserIdAndIsDeletedTrue(customerLevelId, userId)
//
//    // TODO: REFACTOR - การสร้าง config object ควรแยกเป็น method แยกต่างหาก
//    val newConfig =
//      if (oldConfig != null) {
//        oldConfig.isDeleted = false
//        oldConfig
//      } else {
//        CustomerLevelConfig(
//          customerLevelId = customerLevel.id,
//          userId = user.id,
//          user = user,
//          customerLevel = customerLevel
//        )
//      }
//
//    val savedConfig = customerLevelConfigRepository.save(newConfig)
//    return savedConfig.toUserCustomerLevelListDto()
//  }

  override fun removeUserCustomLevel(userId: Long): Boolean {
    val user = userRepository.findById(userId).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลผู้ใช้")
    }
    user.customerLevelId = null
    user.customerLevel = null
    user.customerLevelDate = null
    userRepository.save(user)
    return true
  }

  override fun updateCustomerLevelActive(id: Long): Boolean {
    val customerLevel = customerLevelRepository.findById(id).orElseThrow{
      throw NotFoundException("ไม่พบข้อมูลระดับลูกค้า")
    }
    customerLevel.isActive = !customerLevel.isActive
    customerLevelRepository.save(customerLevel)
    return true
  }

  override fun getPageUserByLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    customerLevelId: Long?
  ): Page<UserCustomerLevelDto> {
    return customerLevelRepository.getPageUserByLevel(
      pageable,
      ascending,
      search,
      customerLevelId
    )
  }

  override fun getCustomerByUserId(userId: Long): AdminCustomerLevelDto {
    val user = userRepository.findById(userId).orElseThrow {
      throw NotFoundException("ไม่พบข้อมูลลูกค้า")
    }

    val taxAddresses = user.address.filter { it?.isTax == true }.map { address ->
      CustomerAddressDto(
        name = address?.name ?: "",
        phoneNumber = address?.phoneNumber ?: "",
        address = address?.address ?: "",
        zipCode = address?.zipCode ?: "",
        province = address?.province ?: "",
        district = address?.district ?: "",
        subDistrict = address?.subDistrict ?: "",
        isTax = true,
        taxPayerType = address?.taxPayerType,
        taxId = address?.taxId,
        email = address?.email
      )
    }

    val shippingAddresses = user.address.filter { it?.isTax == false }.map { address ->
      CustomerAddressDto(
        name = address?.name ?: "",
        phoneNumber = address?.phoneNumber ?: "",
        address = address?.address ?: "",
        zipCode = address?.zipCode ?: "",
        province = address?.province ?: "",
        district = address?.district ?: "",
        subDistrict = address?.subDistrict ?: "",
        isTax = false,
        taxPayerType = address?.taxPayerType,
        taxId = address?.taxId,
        email = address?.email
      )
    }

    return AdminCustomerLevelDto(
      id = user.id,
      name = user.fullName,
      imageUrl = user.imageUrl,
      customerLevelId = user.customerLevelId,
      customerLevelName = user.customerLevel?.name,
      phoneNumber = user.phoneNumber,
      email = user.email,
      lineID = user.providerId,
      taxAddress = taxAddresses,
      shippingAddress = shippingAddresses
    )
  }
}