package com.lucablock.digiboxapi.customerlevel.service

import com.lucablock.digiboxapi.customerlevel.dto.AdminCustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.CustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelListDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelNewListDto
import com.lucablock.digiboxapi.customerlevel.request.CustomerLevelRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface CustomerLevelService {
  fun createCustomerLevel(customerLevelRequest: CustomerLevelRequest): CustomerLevelDto
  fun updateCustomerLevel(id: Long, customerLevelRequest: CustomerLevelRequest): CustomerLevelDto
  fun getAllCustomerLevel():List<CustomerLevelDto>
  fun getCustomerLevelById(id:Long):CustomerLevelDto
  fun getPageCustomerLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive : Boolean?,
  ): Page<CustomerLevelDto>
  fun deleteCustomerLevel(id: Long):Boolean
//  fun getPageUserByCustomerLevel(
//    pageable: Pageable,
//    ascending: Boolean,
//    search: String?,
//    customerLevelId : Long?,
//  ): Page<UserCustomerLevelDto>
//  fun addUserCustomerLevel(customerLevelId:Long, userId:Long): UserCustomerLevelListDto
  fun addUserLevel(customerLevelId:Long, userId:Long): UserCustomerLevelNewListDto
  fun removeUserCustomLevel(userId : Long):Boolean
  fun updateCustomerLevelActive(id:Long):Boolean
  fun getPageUserByLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    customerLevelId : Long?,
  ): Page<UserCustomerLevelDto>
  fun getCustomerByUserId(userId: Long): AdminCustomerLevelDto
}