package com.lucablock.digiboxapi.customerlevel.service.impl

import com.lucablock.digiboxapi.customerlevel.dto.CustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.dto.UserCustomerLevelDto
import com.lucablock.digiboxapi.customerlevel.repository.CustomerLevelRepositoryCustom
import com.lucablock.digiboxapi.entity.QCustomerLevel
import com.lucablock.digiboxapi.entity.QOrder
import com.lucablock.digiboxapi.entity.QUser
import com.lucablock.digiboxapi.entity.UserRoleEnum
import com.querydsl.jpa.impl.JPAQueryFactory
import jakarta.annotation.PostConstruct
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class CustomerLevelRepositoryImpl : CustomerLevelRepositoryCustom{
  @PersistenceContext
  lateinit var entityManager: EntityManager
  lateinit var queryFactory: JPAQueryFactory

  @PostConstruct
  fun init() {
    queryFactory = JPAQueryFactory(entityManager)
  }

  private val qCustomerLevel = QCustomerLevel.customerLevel
  private val qUser = QUser.user
  private val qOrder = QOrder.order

//  override fun getPageUserByCustomerLevel(
//    pageable: Pageable,
//    ascending: Boolean,
//    search: String?,
//    customerLevelId: Long?
//  ): Page<UserCustomerLevelDto> {
//    var criteria = qUser.isDeleted.eq(false)
//    if (search != null) {
//      criteria = criteria.and(qUser.fullName.containsIgnoreCase(search))
//        .or(qUser.email.containsIgnoreCase(search))
//    }
//    if(customerLevelId != null) {
//      // TODO: PERFORMANCE - การใช้ any() อาจทำให้เกิด N+1 query problem
//      // ควรใช้ proper JOIN แทน
//      criteria = criteria.and(qUser.customerLevelConfig.any().customerLevel.id.eq(customerLevelId))
//    }
//
//    val sort = if (ascending) {
//      qUser.id.asc()
//    } else {
//      qUser.id.desc()
//    }
//
//    val query = queryFactory
//      .select(qUser)
//      .from(qUser)
//      .where(criteria)
//      .orderBy(sort)
//      .offset(pageable.offset)
//      .limit(pageable.pageSize.toLong())
//      .fetch().map {
//        // TODO: PERFORMANCE - การดึง lazy loading ใน loop อาจทำให้เกิด N+1 queries
//        // ควรใช้ fetch join หรือ projection แทน
//        val levelConfig = it.customerLevelConfig.firstOrNull { clc ->
//          clc?.isDeleted == false
//        }
//        UserCustomerLevelDto(
//          id = it.id,
//          name = it.fullName,
//          email = it.email,
//          customerLevelId = levelConfig?.customerLevel?.id,
//          customerLevelName = levelConfig?.customerLevel?.name,
//          orderCount = 0,
//          createDate = it.customerLevelConfig.firstOrNull()?.createdDate
//        )
//      }
//
//    val total = queryFactory
//      .select(qUser)
//      .from(qUser)
//      .where(criteria)
//      .fetchCount()
//
//    return PageImpl(query, pageable, total)
//  }

  override fun getPageUserByLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    customerLevelId: Long?
  ): Page<UserCustomerLevelDto> {
    var criteria = qUser.isDeleted.eq(false).and(qUser.userTypeId.eq(UserRoleEnum.ROLE_USER.value.toLong()))
    if (search != null) {
      val searchPredicate = qUser.fullName.containsIgnoreCase(search).or(qUser.email.containsIgnoreCase(search))
      criteria = criteria.and(searchPredicate)
    }
    if(customerLevelId != null) {
      criteria = criteria.and(qUser.customerLevelId.eq(customerLevelId))
    }

    val sort = if (ascending) {
      qUser.id.asc()
    } else {
      qUser.id.desc()
    }

    val query = queryFactory
      .select(qUser)
      .from(qUser)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map {
        UserCustomerLevelDto(
          id = it.id,
          name = it.fullName,
          email = it.email,
          customerLevelId = it.customerLevel?.id,
          customerLevelName = it.customerLevel?.name,
          customerLevelImage = it.customerLevel?.imageUrl,
          orderCount = 0,
          customerLevelDate = it.customerLevelDate
        )
      }

    val total = queryFactory
      .select(qUser)
      .from(qUser)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }

  override fun getPageCustomerLevel(
    pageable: Pageable,
    ascending: Boolean,
    search: String?,
    isActive: Boolean?
  ): Page<CustomerLevelDto> {
    var criteria = qCustomerLevel.isNotNull
    if (search != null) {
      criteria = criteria.and(qCustomerLevel.name.containsIgnoreCase(search))
    }
    if (isActive != null) {
      criteria = criteria.and(qCustomerLevel.isActive.eq(isActive))
    }

    val sort = if (ascending) {
      qCustomerLevel.id.asc()
    } else {
      qCustomerLevel.id.desc()
    }

    val query = queryFactory
      .select(qCustomerLevel)
      .from(qCustomerLevel)
      .where(criteria)
      .orderBy(sort)
      .offset(pageable.offset)
      .limit(pageable.pageSize.toLong())
      .fetch().map { it.toCustomerLevelDto()
      }

    val total = queryFactory
      .select(qCustomerLevel)
      .from(qCustomerLevel)
      .where(criteria)
      .fetchCount()

    return PageImpl(query, pageable, total)
  }
}