spring:
  profiles:
    active: "local"
  servlet:
    multipart:
      max-file-size: 128MB
      max-request-size: 128MB
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    generate-ddl: false
    hibernate:
      transaction:
        jta:
          platform: org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform
      default_schema: public
      naming:
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
      ddl-auto: none
    properties:
      hibernate:
        globally_quoted_identifiers: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        connection:
          characterEncoding: utf-8
          CharSet: utf-8
          useUnicode: true
        format_sql: true

    show-sql: false
    data:
      redis:
        repositories:
          enabled: true
        host: localhost
        port: 6379
        database: 0
        timeout: 60000

  liquibase:
    enabled: false
  mvc:
    async:
      request-timeout: 60000
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: '#W2Kb3?w&njb+'
    properties:
      mail:
        transport:
          protocol: smtp
        smtp:
          auth: true
          starttls:
            enable: true
mail-sender:
  host: smtp.gmail.com
  port: 587
  username: <EMAIL>
  password: 'rwko zyjy axhl dsjg'
  protocol: smtp
  auth: true
  starttls-enable: true
  debug: true

management:
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessState:
      enabled: true
    readinessstate:
      enabled: true
logging:
  level:
    org:
      hibernate:
        #        sql: DEBUG
        type:
          descriptor:
            sql: TRACE
      springframework:
        web:
          servlet:
            handler:
              HandlerMappingIntrospector: ERROR
    root: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.security.oauth2: INFO
springdoc:
  api-docs:
    path: /api-docs
  enable-native-support: true
  swagger-ui:
    tags-sorter: alpha
    operations-sorter: method
    path: /swagger-ui

appUrl: http://localhost:3000
server:
  shutdown: graceful

jwt:
  secret: digiboxs
  expirationInMs: 864000000

app:
  cors:
    allowedOrigins: http://localhost:3000,http://localhost:8080,http://localhost:50051
  oauth2:
    authorizedRedirectUris:
      - http://localhost:3000/oauth2/redirect
      - https://app-dev.digiboxs.com/oauth2/redirect
      - https://app-staging.digiboxs.com/oauth2/redirect
      - https://app.digiboxs.com/oauth2/redirect
slip-ok:
  api:
    url: https://api.slipok.com/api/line/apikey/
    key: SLIPOKPGXMKMW
  branch:
    id: 51076
---
spring:
  config:
    activate:
      on-profile: local
  cloud:
    gcp:
      sql:
        enabled: false
  datasource:
    username: root
    password: 12341234
    url: *****************************************
    #    username: doadmin
    #    password: AVNS_mrKliuZy14ahbUn6wAA
    #    url: *************************************************************************************************************************************************************************************************************************************
    hikari:
      schema: public
      connectionTimeout: 30000
      leakDetectionThreshold: 120000
  security:
    oauth2:
      client:
        registration:
          line:
            client_id: **********
            client_secret: 23c110cbb7ebb96643e1ea08ca7274cc
            authorization_grant_type: authorization_code
            redirect_uri: "{baseUrl}/oauth2/callback/{registrationId}"
            code_challenge_method: plain
            scope:
              - profile
              - openid
              - email
            client_name: Line
        provider:
          line:
            authorizationUri: https://access.line.me/oauth2/v2.1/authorize
            tokenUri: https://api.line.me/oauth2/v2.1/token
            userInfoUri: https://api.line.me/v2/profile
            userNameAttribute: userId
            jwkSetUri: https://api.line.me/oauth2/v2.1/verify

s3:
  accessKey: ********************
  secretKey: ZTFRTzET98jAFHc9eJW+FWmtUlcOgfL76kH7a0Wl
  bucket: digiboxs
  folder: /dev
  folderBuffer: /buffer
  url: https://cdn.digiboxs.com

app:
  url: http://localhost:3000

appUrl: http://localhost:3000

slip-ok:
  api:
    url: https://api.slipok.com/api/line/apikey/
    key: SLIPOKPGXMKMW
  branch:
    id: 51076
---
spring:
  jpa:
    data:
      redis:
        repositories:
          enabled: true
        host: redis-dev-0.redis-dev.digibox-dev.svc.cluster.local
        port: 6379
        database: 0
        timeout: 60000
  config:
    activate:
      on-profile: dev
  datasource:
    username: doadmin
    password: AVNS_mrKliuZy14ahbUn6wAA
    url: *************************************************************************************************************************************************************************************************************************************
    hikari:
      schema: public
      connectionTimeout: 30000
      leakDetectionThreshold: 120000
  security:
    oauth2:
      client:
        registration:
          line:
            client_id: **********
            client_secret: fe4652305a610ef96d5e77a3e60c65d4
            authorization_grant_type: authorization_code
            redirect_uri: "{baseUrl}/oauth2/callback/{registrationId}"
            code_challenge_method: plain
            scope:
              - profile
              - openid
              - email
            client_name: Line
        provider:
          line:
            authorizationUri: https://access.line.me/oauth2/v2.1/authorize
            tokenUri: https://api.line.me/oauth2/v2.1/token
            userInfoUri: https://api.line.me/v2/profile
            userNameAttribute: userId
            jwkSetUri: https://api.line.me/oauth2/v2.1/verify
s3:
  accessKey: ********************
  secretKey: ZTFRTzET98jAFHc9eJW+FWmtUlcOgfL76kH7a0Wl
  bucket: digiboxs
  folder: /dev
  folderBuffer: /buffer
  url: https://cdn.digiboxs.com
app:
  url: https://app-dev.digiboxs.com

appUrl: https://app-dev.digiboxs.com

---
spring:
  jpa:
    data:
      redis:
        repositories:
          enabled: true
        host: redis-staging-0.redis-dev.digibox-staging.svc.cluster.local
        port: 6379
        database: 0
        timeout: 60000
  config:
    activate:
      on-profile: staging
  datasource:
    username: doadmin
    password: AVNS_mrKliuZy14ahbUn6wAA
    url: *****************************************************************************************************************************************************************************************************************************************
    hikari:
      schema: public
      connectionTimeout: 30000
      leakDetectionThreshold: 120000
  security:
    oauth2:
      client:
        registration:
          line:
            client_id: **********
            client_secret: d8153e24997c7a572a9015212acf3d1b
            authorization_grant_type: authorization_code
            redirect_uri: "{baseUrl}/oauth2/callback/{registrationId}"
            code_challenge_method: plain
            scope:
              - profile
              - openid
              - email
            client_name: Line
        provider:
          line:
            authorizationUri: https://access.line.me/oauth2/v2.1/authorize
            tokenUri: https://api.line.me/oauth2/v2.1/token
            userInfoUri: https://api.line.me/v2/profile
            userNameAttribute: userId
            jwkSetUri: https://api.line.me/oauth2/v2.1/verify
s3:
  accessKey: ********************
  secretKey: ZTFRTzET98jAFHc9eJW+FWmtUlcOgfL76kH7a0Wl
  bucket: digiboxs
  folder: /staging
  folderBuffer: /buffer
  url: https://cdn.digiboxs.com
app:
  url: https://app-staging.digiboxs.com

appUrl: https://app-staging.digiboxs.com
---
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    username: develop
    password: U28Yt2EUpnO:okHx
    url: ***********************************************************************************************************************************************************************************
    hikari:
      schema: public
      connectionTimeout: 30000
      leakDetectionThreshold: 120000
  security:
    oauth2:
      client:
        registration:
          line:
            client_id: **********
            client_secret: d8153e24997c7a572a9015212acf3d1b
            authorization_grant_type: authorization_code
            redirect_uri: "{baseUrl}/oauth2/callback/{registrationId}"
            code_challenge_method: plain
            scope:
              - profile
              - openid
              - email
            client_name: Line
        provider:
          line:
            authorizationUri: https://access.line.me/oauth2/v2.1/authorize
            tokenUri: https://api.line.me/oauth2/v2.1/token
            userInfoUri: https://api.line.me/v2/profile
            userNameAttribute: userId
            jwkSetUri: https://api.line.me/oauth2/v2.1/verify
s3:
  accessKey: ********************
  secretKey: ZTFRTzET98jAFHc9eJW+FWmtUlcOgfL76kH7a0Wl
  bucket: digiboxs
  folder: /staging
  folderBuffer: /buffer
  url: https://cdn.digiboxs.com
app:
  url: https://digiboxs.com

appUrl: https://digiboxs.com
---

