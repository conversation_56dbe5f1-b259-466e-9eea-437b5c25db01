databaseChangeLog:
  - changeSet:
      id: Create UserType
      author: mos-dev
      changes:
        - createTable:
            tableName: user_type
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: is_admin
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: is_user
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: is_super_admin
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: is_anonymous
                  type: boolean
                  constraints:
                    nullable: false

  - changeSet:
      id: Create User
      author: mos-dev
      changes:
        - createTable:
            tableName: users
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: first_name
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: last_name
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: phone_number
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: phone_verified
                  type: boolean
                  constraints:
                    nullable: true
              - column:
                  name: username
                  type: varchar
                  constraints:
                    unique: true
                    uniqueConstraintName: username_unique
                    nullable: true
              - column:
                  name: email
                  type: varchar(50)
                  constraints:
                    unique: true
                    uniqueConstraintName: users_email_unique
                    nullable: true
              - column:
                  name: password
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: user_type_id
                  type: int
                  constraints:
                    foreignKeyName: fk_user_user_type_id
                    references: user_type(id)
                    nullable: false
              - column:
                  name: status
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: email_verified
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: full_name
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: provider
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: provider_id
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: created_date
                  type: datetime
              - column:
                  name: modified_date
                  type: timestamp
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false

  - changeSet:
      id: Load Data UserType
      author: mos-dev
      changes:
        - loadData:
            catalogName: user_type
            columns:
              - column:
                  header: id
                  name: id
                  type: NUMERIC
              - column:
                  header: name
                  name: name
                  type: varchar
              - column:
                  header: is_admin
                  name: is_admin
                  type: boolean
              - column:
                  header: is_user
                  name: is_user
                  type: boolean
              - column:
                  header: is_super_admin
                  name: is_super_admin
                  type: boolean
              - column:
                  header: is_anonymous
                  name: is_anonymous
                  type: boolean
            commentLineStartsWith: //
            encoding: UTF-8
            file: ../v1/data/userType.csv
            quotchar: ''''
            relativeToChangelogFile: true
            schemaName: public
            separator: ','
            tableName: user_type
            usePreparedStatements: true

  - changeSet:
      id: Load Data User
      author: mos-dev
      changes:
        - loadData:
            catalogName: user
            columns:
              - column:
                  header: id
                  name: id
                  type: NUMERIC
              - column:
                  header: first_name
                  name: first_name
                  type: varchar
              - column:
                  header: last_name
                  name: last_name
                  type: varchar
              - column:
                  header: phone_number
                  name: phone_number
                  type: varchar
              - column:
                  header: phone_verified
                  name: phone_verified
                  type: boolean
              - column:
                  header: username
                  name: username
                  type: varchar
              - column:
                  header: email
                  name: email
                  type: varchar
              - column:
                  header: last_name
                  name: last_name
                  type: varchar
              - column:
                  header: password
                  name: password
                  type: varchar
              - column:
                  header: user_type_id
                  name: user_type_id
                  type: numeric
              - column:
                  header: status
                  name: status
                  type: boolean
              - column:
                  header: email_verified
                  name: email_verified
                  type: boolean
              - column:
                  header: image_url
                  name: image_url
                  type: varchar
              - column:
                  header: full_name
                  name: full_name
                  type: varchar
              - column:
                  header: provider
                  name: provider
                  type: varchar
              - column:
                  header: provider_id
                  name: provider_id
                  type: varchar
              - column:
                  header: created_date
                  name: created_date
                  type: datetime
              - column:
                  header: modified_date
                  name: modified_date
                  type: datetime
            commentLineStartsWith: //
            encoding: UTF-8
            file: ../v1/data/user.csv
            quotchar: ''''
            relativeToChangelogFile: true
            schemaName: public
            separator: ','
            tableName: users
            usePreparedStatements: true

  - changeSet:
      id: create table productCategory
      author: mos-dev
      changes:
        - createTable:
            tableName: product_category
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    unique: true
                    nullable: false
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table productTag
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_tag
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    unique: true
                    nullable: false
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table productType
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_type
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    unique: true
                    nullable: false
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table unfolded_size
      author: nanthawat.to
      changes:
        - createTable:
            tableName: unfolded_size
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: width
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"
              - column:
                  name: height
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: Create Blog Category
      author: mos-dev
      changes:
        - createTable:
            tableName: blog_category
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: url_slug
                  type: varchar(255)
                  constraints:
                    nullable: false

  - changeSet:
      id: Create Blog Type
      author: mos-dev
      changes:
        - createTable:
            tableName: blog_type
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: url_slug
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false

  - changeSet:
      id: Create Gallery
      author: mos-dev
      changes:
        - createTable:
            tableName: gallery
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false

  - changeSet:
      id: Create Blog
      author: mos-dev
      changes:
        - createTable:
            tableName: blog
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: title
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: content
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: blog_type_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_blog_blog_type_id
                    references: blog_type(id)
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: keyword
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: thumbnail_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: url_slug
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: author
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_published
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false

  - changeSet:
      id: Create Blog Category Mapping
      author: mos-dev
      changes:
        - createTable:
            tableName: blog_category_mapping
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  incrementBy: 1
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: blog_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_blog_category_mapping_blog_id
                    references: blog(id)
              - column:
                  name: blog_category_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_blog_category_mapping_blog_category_id
                    references: blog_category(id)

  - changeSet:
      id: create table coating
      author: nanthawat.to
      changes:
        - createTable:
            tableName: coating
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    unique: true
                    nullable: false
              - column:
                  name: price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table materials
      author: nanthawat.to
      changes:
        - createTable:
            tableName: materials
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table area_size_percentage
      author: nanthawat.to
      changes:
        - createTable:
            tableName: area_size_percentage
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: percentage
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table special_technic
      author: nanthawat.to
      changes:
        - createTable:
            tableName: special_technic
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: price
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table special_technic_config
      author: nanthawat.to
      changes:
        - createTable:
            tableName: special_technic_config
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: price
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: special_technic_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_special_technic_config_special_technic_id
                    references: special_technic(id)
              - column:
                  name: area_size_percentage_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_special_technic_config_area_size_id
                    references: area_size_percentage(id)

  - changeSet:
      id: create table product
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: guide_detail
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: installment_detail
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: shipping_detail
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table product_category_config
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_category_config
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: product_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_category_config_product_id
                    references: product(id)
              - column:
                  name: product_category_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_category_config_product_category_id
                    references: product_category(id)

  - changeSet:
      id: create table product_tag_config
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_tag_config
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: product_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_category_config_product_id
                    references: product(id)
              - column:
                  name: product_tag_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_tag_config_product_tag_id
                    references: product_tag(id)

  - changeSet:
      id: create table product_type_config
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_type_config
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: product_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_category_config_product_id
                    references: product(id)
              - column:
                  name: product_type_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_type_config_product_type_id
                    references: product_type(id)

  - changeSet:
      id: create table product_coating
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_coating
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: product_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_category_config_product_id
                    references: product(id)
              - column:
                  name: coating_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_coating_coating_id
                    references: coating(id)

  - changeSet:
      id: create table product_special_technic
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_special_technic
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: product_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_category_config_product_id
                    references: product(id)
              - column:
                  name: special_technic_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_special_technic_special_technic_id
                    references: special_technic(id)

  - changeSet:
      id: create table product_gallery
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_gallery
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: file_type
                  type: int
                  constraints:
                    nullable: true
                  defaultValueNumeric: "1"
              - column:
                  name: product_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_product_category_config_product_id
                    references: product(id)

  - changeSet:
      id: create table gram
      author: nanthawat.to
      changes:
        - createTable:
            tableName: gram
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: gsm
                  type: Int
                  constraints:
                    nullable: false
              - column:
                  name: mm
                  type: double
                  constraints:
                    nullable: true
                  defaultValueNumeric: "0.0"
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table material_config
      author: nanthawat.to
      changes:
        - createTable:
            tableName: material_config
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: materials_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_material_config_material_id
                    references: materials(id)
              - column:
                  name: gram_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_material_config_gram_id
                    references: gram(id)

  - changeSet:
      id: create table address
      author: nanthawat.to
      changes:
        - createTable:
            tableName: address
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: phone_number
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: address
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: zip_code
                  type: varchar(5)
                  constraints:
                    nullable: false
              - column:
                  name: province
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: district
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: sub_district
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: is_tax
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: tax_payer_type
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: tax_id
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: email
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: is_default
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: modified_date
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: user_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_address_user_id
                    references: users(id)
  - changeSet:
      id: dropUniqueConstraint-productCategory
      author: nanthawat.to
      changes:
        - dropUniqueConstraint:
            constraintName: product_category_name_key
            schemaName: public
            tableName: product_category

  - changeSet:
      id: dropUniqueConstraint-productTag
      author: nanthawat.to
      changes:
        - dropUniqueConstraint:
            constraintName: product_tag_name_key
            schemaName: public
            tableName: product_tag

  - changeSet:
      id: dropUniqueConstraint-productType
      author: nanthawat.to
      changes:
        - dropUniqueConstraint:
            constraintName: product_type_name_key
            schemaName: public
            tableName: product_type

  - changeSet:
      id: dropUniqueConstraint-coating
      author: nanthawat.to
      changes:
        - dropUniqueConstraint:
            constraintName: coating_name_key
            schemaName: public
            tableName: coating

  - changeSet:
      id: addColumn-description
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: coating
            columns:
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true

  - changeSet:
      id: add-special-technic-description
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: special_technic
            columns:
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true

  - changeSet:
      id: dropTable-product_coating
      author: nanthawat.to
      changes:
        - dropTable:
            cascadeConstraints: true
            schemaName: public
            tableName: product_coating

  - changeSet:
      id: create table printing
      author: nanthawat.to
      changes:
        - createTable:
            tableName: printing
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: amount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: "1"
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: machinery
                  type: varchar
                  constraints:
                    nullable: true

  - changeSet:
      id: add-over-size-limit-unfolded
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: unfolded_size
            columns:
              - column:
                  name: over_size_limit
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"

  - changeSet:
      id: add-special-technic-config-period
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: special_technic_config
            columns:
              - column:
                  name: period
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: "1"

  - changeSet:
      id: create table model
      author: nanthawat.to
      changes:
        - createTable:
            tableName: model
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: model_code
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: product_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_product_id
                    references: product(id)
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
  - changeSet:
      id: create table model_size
      author: nanthawat.to
      changes:
        - createTable:
            tableName: model_size
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: width
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: height
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: length
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: is_three_d
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: model_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_model_id
                    references: model(id)
              - column:
                  name: unfolded_size_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_unfolded_size_id
                    references: unfolded_size(id)
  - changeSet:
      id: create table model_size_config
      author: nanthawat.to
      changes:
        - createTable:
            tableName: model_size_config
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: model_size_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_config_model_size_id
                    references: model_size(id)
              - column:
                  name: material_config_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_config_material_config_id
                    references: material_config(id)
              - column:
                  name: printing_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_config_printing_id
                    references: printing(id)

  - changeSet:
      id: create table model_size_config_detail
      author: nanthawat.to
      changes:
        - createTable:
            tableName: model_size_config_detail
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: amount
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: period
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: model_size_config_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_config_id
                    references: model_size_config(id)

  - changeSet:
      id: create table model_size_config_coating
      author: nanthawat.to
      changes:
        - createTable:
            tableName: model_size_config_coating
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: coating_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_coating_coating_id
                    references: coating(id)
              - column:
                  name: model_size_config_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_size_coating_config_id
                    references: model_size_config(id)

  - changeSet:
      id: dropColumn-price-special-technic
      author: nanthawat.to
      changes:
        - dropColumn:
            columnName: price
            columns:
              - column:
                  name: price
            schemaName: public
            tableName: special_technic

  - changeSet:
      id: create table company
      author: nanthawat.to
      changes:
        - createTable:
            tableName: company
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: tax_id
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: address
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: zip_code
                  type: varchar(5)
                  constraints:
                    nullable: false
              - column:
                  name: province
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: district
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: sub_district
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: phone_number
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: email
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: line_id
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: line_image
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: website_url
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: map_url
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
  - changeSet:
      id: create table province
      author: nanthawat.to
      changes:
        - createTable:
            tableName: province
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: province_code
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: geo_id
                  type: int
                  constraints:
                    nullable: false
  - changeSet:
      id: create table district
      author: nanthawat.to
      changes:
        - createTable:
            tableName: district
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: district_code
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: geo_id
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: province_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_district_province_id
                    references: province(id)
  - changeSet:
      id: create table sub_district
      author: nanthawat.to
      changes:
        - createTable:
            tableName: sub_district
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: sub_district_code
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: geo_id
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: province_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_district_province_id
                    references: province(id)
              - column:
                  name: district_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_district_district_id
                    references: district(id)
  - changeSet:
      id: create table zipCode
      author: nanthawat.to
      changes:
        - createTable:
            tableName: zipcode
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: sub_district_code
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: zipcode
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: province_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_district_province_id
                    references: province(id)
              - column:
                  name: district_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_district_district_id
                    references: district(id)
              - column:
                  name: sub_district_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_district_sub_district_id
                    references: sub_district(id)

  #  - changeSet:
  #      id: Load Data Province
  #      author: nanthawat.to
  #      changes:
  #        - loadData:
  #            catalogName: province
  #            columns:
  #              - column:
  #                  header: id
  #                  name: id
  #                  type: numeric
  #              - column:
  #                  header: province_code
  #                  name: province_code
  #                  type: varchar
  #              - column:
  #                  header: name
  #                  name: name
  #                  type: varchar
  #              - column:
  #                  header: geo_id
  #                  name: geo_id
  #                  type: numeric
  #            commentLineStartsWith: //
  #            encoding: UTF-8
  #            file: ../v1/data/province.csv
  #            quotchar: ''''
  #            relativeToChangelogFile: true
  #            schemaName: public
  #            separator: ','
  #            tableName: province
  #            usePreparedStatements: true
  #
  #  - changeSet:
  #      id: Load Data District
  #      author: nanthawat.to
  #      changes:
  #        - loadData:
  #            catalogName: district
  #            columns:
  #              - column:
  #                  header: id
  #                  name: id
  #                  type: numeric
  #              - column:
  #                  header: district_code
  #                  name: district_code
  #                  type: varchar
  #              - column:
  #                  header: name
  #                  name: name
  #                  type: varchar
  #              - column:
  #                  header: geo_id
  #                  name: geo_id
  #                  type: numeric
  #              - column:
  #                  header: province_id
  #                  name: province_id
  #                  type: numeric
  #            commentLineStartsWith: //
  #            encoding: UTF-8
  #            file: ../v1/data/district.csv
  #            quotchar: ''''
  #            relativeToChangelogFile: true
  #            schemaName: public
  #            separator: ','
  #            tableName: district
  #            usePreparedStatements: true
  #
  #  - changeSet:
  #      id: Load Data SubDistrict
  #      author: nanthawat.to
  #      changes:
  #        - loadData:
  #            catalogName: sub_district
  #            columns:
  #              - column:
  #                  header: id
  #                  name: id
  #                  type: numeric
  #              - column:
  #                  header: sub_district_code
  #                  name: sub_district_code
  #                  type: varchar
  #              - column:
  #                  header: name
  #                  name: name
  #                  type: varchar
  #              - column:
  #                  header: geo_id
  #                  name: geo_id
  #                  type: numeric
  #              - column:
  #                  header: district_id
  #                  name: district_id
  #                  type: numeric
  #              - column:
  #                  header: province_id
  #                  name: province_id
  #                  type: numeric
  #            commentLineStartsWith: //
  #            encoding: UTF-8
  #            file: ../v1/data/sub_district.csv
  #            quotchar: ''''
  #            relativeToChangelogFile: true
  #            schemaName: public
  #            separator: ','
  #            tableName: sub_district
  #            usePreparedStatements: true
  #
  #  - changeSet:
  #      id: Load Data ZipCode
  #      author: nanthawat.to
  #      changes:
  #        - loadData:
  #            catalogName: zip_code
  #            columns:
  #              - column:
  #                  header: id
  #                  name: id
  #                  type: numeric
  #              - column:
  #                  header: sub_district_code
  #                  name: sub_district_code
  #                  type: varchar
  #              - column:
  #                  header: province_id
  #                  name: province_id
  #                  type: numeric
  #              - column:
  #                  header: district_id
  #                  name: district_id
  #                  type: numeric
  #              - column:
  #                  header: sub_district_id
  #                  name: sub_district_id
  #                  type: numeric
  #              - column:
  #                  header: zipcode
  #                  name: zipcode
  #                  type: varchar
  #            commentLineStartsWith: //
  #            encoding: UTF-8
  #            file: ../v1/data/zipcode.csv
  #            quotchar: ''''
  #            relativeToChangelogFile: true
  #            schemaName: public
  #            separator: ','
  #            tableName: zipcode
  #            usePreparedStatements: true
  #
  #  - changeSet:
  #      id: Load Data Company
  #      author: nanthawat.to
  #      changes:
  #        - loadData:
  #            catalogName: company
  #            columns:
  #              - column:
  #                  header: id
  #                  name: id
  #                  type: numeric
  #              - column:
  #                  header: name
  #                  name: name
  #                  type: varchar
  #              - column:
  #                  header: image_url
  #                  name: image_url
  #                  type: varchar
  #              - column:
  #                  header: tax_id
  #                  name: tax_id
  #                  type: varchar
  #              - column:
  #                  header: address
  #                  name: address
  #                  type: varchar
  #              - column:
  #                  header: zip_code
  #                  name: zip_code
  #                  type: varchar
  #              - column:
  #                  header: province
  #                  name: province
  #                  type: varchar
  #              - column:
  #                  header: district
  #                  name: district
  #                  type: varchar
  #              - column:
  #                  header: sub_district
  #                  name: sub_district
  #                  type: varchar
  #              - column:
  #                  header: phone_number
  #                  name: phone_number
  #                  type: varchar
  #              - column:
  #                  header: email
  #                  name: email
  #                  type: varchar
  #              - column:
  #                  header: line_id
  #                  name: line_id
  #                  type: varchar
  #              - column:
  #                  header: line_image
  #                  name: line_image
  #                  type: varchar
  #              - column:
  #                  header: website_url
  #                  name: website_url
  #                  type: varchar
  #              - column:
  #                  header: map_url
  #                  name: map_url
  #                  type: varchar
  #              - column:
  #                  header: is_deleted
  #                  name: is_deleted
  #                  type: boolean
  #            commentLineStartsWith: //
  #            encoding: UTF-8
  #            file: ../v1/data/company.csv
  #            quotchar: ''''
  #            relativeToChangelogFile: true
  #            schemaName: public
  #            separator: ','
  #            tableName: company
  #            usePreparedStatements: true

  - changeSet:
      id: create table companyHoliday
      author: nanthawat.to
      changes:
        - createTable:
            tableName: company_holiday
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: holiday_date
                  type: date
                  constraints:
                    nullable: false
                  defaultValueDate: current_date
              - column:
                  name: company_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_holiday_company_id
                    references: company(id)

  - changeSet:
      id: create table companyWorking
      author: nanthawat.to
      changes:
        - createTable:
            tableName: company_working
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: day
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: sort
                  type: int
                  constraints:
                    nullable: false

  #  - changeSet:
  #      id: create table companyWorkingConfig
  #      author: nanthawat.to
  #      changes:
  #        - createTable:
  #            tableName: company_working_config
  #            columns:
  #              - column:
  #                  name: id
  #                  type: int
  #                  autoIncrement: true
  #                  constraints:
  #                    primaryKey: true
  #                    nullable: false
  #              - column:
  #                  name: is_active
  #                  type: boolean
  #                  constraints:
  #                    nullable: false
  #                  defaultValueBoolean: true
  #              - column:
  #                  name: company_working_id
  #                  type: int
  #                  constraints:
  #                    nullable: false
  #                    foreignKeyName: fk_company_working_config_working_id
  #                    references: company_working(id)
  #              - column:
  #                  name: company_id
  #                  type: int
  #                  constraints:
  #                    nullable: false
  #                    foreignKeyName: fk_company_working_config_company_id
  #                    references: company(id)
  #
  #  - changeSet:
  #      id: Load Data CompanyWorking
  #      author: nanthawat.to
  #      changes:
  #        - loadData:
  #            catalogName: company_working
  #            columns:
  #              - column:
  #                  header: id
  #                  name: id
  #                  type: numeric
  #              - column:
  #                  header: day
  #                  name: day
  #                  type: varchar
  #              - column:
  #                  header: sort
  #                  name: sort
  #                  type: numeric
  #            commentLineStartsWith: //
  #            encoding: UTF-8
  #            file: ../v1/data/companyWorking.csv
  #            quotchar: ''''
  #            relativeToChangelogFile: true
  #            schemaName: public
  #            separator: ','
  #            tableName: company_working
  #            usePreparedStatements: true
  #
  #  - changeSet:
  #      id: Load Data CompanyWorkingConfig
  #      author: nanthawat.to
  #      changes:
  #        - loadData:
  #            catalogName: company_working_config
  #            columns:
  #              - column:
  #                  header: id
  #                  name: id
  #                  type: numeric
  #              - column:
  #                  header: company_id
  #                  name: company_id
  #                  type: numeric
  #              - column:
  #                  header: company_working_id
  #                  name: company_working_id
  #                  type: numeric
  #              - column:
  #                  header: is_active
  #                  name: is_active
  #                  type: boolean
  #            commentLineStartsWith: //
  #            encoding: UTF-8
  #            file: ../v1/data/companyWorkingConfig.csv
  #            quotchar: ''''
  #            relativeToChangelogFile: true
  #            schemaName: public
  #            separator: ','
  #            tableName: company_working_config
  #            usePreparedStatements: true
  #
  #  - changeSet:
  #      id: add-isActive-materials-config
  #      author: nanthawat.to
  #      changes:
  #        - addColumn:
  #            tableName: material_config
  #            columns:
  #              - column:
  #                  name: is_active
  #                  type: boolean
  #                  constraints:
  #                    nullable: false
  #                  defaultValueBoolean: true
  #              - column:
  #                  name: is_deleted
  #                  type: boolean
  #                  constraints:
  #                    nullable: false
  #                  defaultValueBoolean: false

  - changeSet:
      id: dropColumn-amount-printing
      author: nanthawat.to
      changes:
        - dropColumn:
            columns:
              - column:
                  name: amount
            schemaName: public
            tableName: printing
        - dropColumn:
            columns:
              - column:
                  name: machinery
            schemaName: public
            tableName: printing

  - changeSet:
      id: add-columns-printing
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: printing
            columns:
              - column:
                  name: min_print
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: "1"
              - column:
                  name: max_print
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: "1"
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table machine
      author: nanthawat.to
      changes:
        - createTable:
            tableName: machine
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: printing_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_machine_printing_id
                    references: printing(id)

  - changeSet:
      id: add-isActive-printing
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: printing
            columns:
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true

  - changeSet:
      id: add-columns-modelSize
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: model_size
            columns:
              - column:
                  name: unfolded_width
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"
              - column:
                  name: unfolded_height
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"

  - changeSet:
      id: create table printingConfig
      author: nanthawat.to
      changes:
        - createTable:
            tableName: printing_config
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: model_size_config_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_printing_config_model_size_config_id
                    references: model_size_config(id)
              - column:
                  name: printing_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_printing_config_printing_id
                    references: printing(id)

  - changeSet:
      id: dropColumn-printing_id-modelSizeConfig
      author: nanthawat.to
      changes:
        - dropColumn:
            columns:
              - column:
                  name: printing_id
            schemaName: public
            tableName: model_size_config

  - changeSet:
      id: dropColumn-modelSizeConfig
      author: nanthawat.to
      changes:
        - dropColumn:
            columns:
              - column:
                  name: model_size_config_id
            schemaName: public
            tableName: model_size_config_detail
        - dropColumn:
            columns:
              - column:
                  name: model_size_config_id
            schemaName: public
            tableName: model_size_config_coating

  - changeSet:
      id: add-fk-column-printing_config_id
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: model_size_config_detail
            columns:
              - column:
                  name: printing_config_id
                  type: int
                  defaultValueNumeric: "1"
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: model_size_config_detail
            baseColumnNames: printing_config_id
            constraintName: fk_printing_config_model_size_config_detail
            referencedTableName: printing_config
            referencedColumnNames: id
        - addColumn:
            tableName: model_size_config_coating
            columns:
              - column:
                  name: printing_config_id
                  type: int
                  defaultValueNumeric: "1"
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: model_size_config_coating
            baseColumnNames: printing_config_id
            constraintName: fk_printing_config_model_size_config_coating
            referencedTableName: printing_config
            referencedColumnNames: id

  - changeSet:
      id: create table product_period
      author: nanthawat.to
      changes:
        - createTable:
            tableName: product_period
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: min_period
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: max_period
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false

  - changeSet:
      id: create table customize
      author: nanthawat.to
      changes:
        - createTable:
            tableName: customize
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: model_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_model_id
                    references: model(id)
              - column:
                  name: model_size_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_model_size_id
                    references: model_size(id)
              - column:
                  name: material_config_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_material_config_id
                    references: material_config(id)
              - column:
                  name: printing_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_printing_id
                    references: printing(id)
              - column:
                  name: model_size_config_detail_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_model_size_config_detail_id
                    references: model_size_config_detail(id)
              - column:
                  name: unfolded_size_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_unfolded_size_id
                    references: unfolded_size(id)
              - column:
                  name: product_period_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_product_period_id
                    references: product_period(id)
              - column:
                  name: width
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: height
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: length
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: period_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table customize_special_technic
      author: nanthawat.to
      changes:
        - createTable:
            tableName: customize_special_technic
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: customize_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_special_technic_customize_id
                    references: customize(id)
              - column:
                  name: special_technic_config_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_special_technic_special_technic_config_id
                    references: special_technic_config(id)

  - changeSet:
      id: create table customize_coating
      author: nanthawat.to
      changes:
        - createTable:
            tableName: customize_coating
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: customize_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_coating_customize_id
                    references: customize(id)
              - column:
                  name: model_size_config_coating_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_customize_model_size_config_coating_id
                    references: model_size_config_coating(id)

  - changeSet:
      id: Load Data ProductPeriod
      author: nanthawat.to
      changes:
        - loadData:
            catalogName: province
            columns:
              - column:
                  header: id
                  name: id
                  type: numeric
              - column:
                  header: name
                  name: name
                  type: varchar
              - column:
                  header: price
                  name: price
                  type: numeric
              - column:
                  header: minPeriod
                  name: minPeriod
                  type: numeric
              - column:
                  header: maxPeriod
                  name: maxPeriod
                  type: numeric
              - column:
                  header: imageUrl
                  name: imageUrl
                  type: varchar
            commentLineStartsWith: //
            encoding: UTF-8
            file: ../v1/data/productPeriod.csv
            quotchar: ''''
            relativeToChangelogFile: true
            schemaName: public
            separator: ','
            tableName: product_period
            usePreparedStatements: true

  - changeSet:
      id: drop-foreign-printing-coating
      author: to
      changes:
        - dropForeignKeyConstraint:
            baseTableName: model_size_config_coating
            constraintName: fk_printing_config_model_size_config_coating
        - dropColumn:
            tableName: model_size_config_coating
            columnName: printing_config_id

  - changeSet:
      id: add-fk-column-model-detail-coating
      author: to
      changes:
        - addColumn:
            tableName: model_size_config_coating
            columns:
              - column:
                  name: model_size_config_detail_id
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: "1"
        - addForeignKeyConstraint:
            baseTableName: model_size_config_coating
            baseColumnNames: model_size_config_detail_id
            referencedTableName: model_size_config_detail
            referencedColumnNames: id
            constraintName: fk_model_size_config_coating_config_detail

  - changeSet:
      id: create table cart
      author: nanthawat.to
      changes:
        - createTable:
            tableName: cart
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: true
                    foreignKeyName: fk_cart_user_id
                    references: users(id)
              - column:
                  name: uuid
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: customize_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_cart_customize_id
                    references: customize(id)
              - column:
                  name: expire_date
                  type: timestamp
                  constraints:
                    nullable: true
  - changeSet:
      id: create table banner
      author: pongpol.arm
      changes:
        - createTable:
            tableName: banner
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: status
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: start_date_time
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: end_date_time
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: modified_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp

  - changeSet:
      id: create table holiday_category
      author: nanthawat.to
      changes:
        - createTable:
            tableName: holiday_category
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false

  - changeSet:
      id: create table holiday_type
      author: nanthawat.to
      changes:
        - createTable:
            tableName: holiday_type
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: color
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: can_recur
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: holiday_category_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_holiday_type_holiday_category_id
                    references: holiday_category(id)

  - changeSet:
      id: create table holiday
      author: nanthawat.to
      changes:
        - createTable:
            tableName: holiday
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: is_recurring
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: holiday_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: true
                  defaultValueDate: current_timestamp
              - column:
                  name: holiday_type_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_holiday_holiday_type_id
                    references: holiday_type(id)
              - column:
                  name: company_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_holiday_company_id
                    references: company(id)

  - changeSet:
      id: create table faq
      author: anuchon.chon
      changes:
        - createTable:
            tableName: faq
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: question
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: answer
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
  - changeSet:
      id: add column user_id and is_active to faq
      author: anuchon.chon
      changes:
        - addColumn:
            tableName: faq
            columns:
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: true
                    foreignKeyName: fk_faq_user_id
                    references: users(id)
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
  - changeSet:
      id: create table customer_level
      author: nanthawat.to
      changes:
        - createTable:
            tableName: customer_level
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  defaultValueBoolean: true
                  constraints:
                    nullable: false
              - column:
                  name: min_purchase_amount
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: min_order_count
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp

  - changeSet:
      id: add-fk-column-user-customer-level
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: users
            columns:
              - column:
                  name: customer_level_id
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: customer_level_date
                  type: timestamp
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: users
            baseColumnNames: customer_level_id
            constraintName: fk_user_customer_level_id
            referencedTableName: customer_level
            referencedColumnNames: id



  - changeSet:
      id: create table coupon category
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon_category
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                      nullable: false
              - column:
                  name: image_url
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: is_deleted
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: modified_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
  - changeSet:
      id: create table coupon_type
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon_type
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  defaultValueBoolean: true
                  constraints:
                    nullable: false
              - column:
                  name: is_deleted
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueDate: current_timestamp
              - column:
                  name: modified_date
                  type: timestamp
  #        - addColumn:
  #            tableName: faq
  #            columns:
  #              - column:
  #                  name: user_id
  #                  type: int
  #                  constraints:
  #                    nullable: true
  #                    foreignKeyName: fk_faq_user_id
  #                    references: users(id)
  #              - column:
  #                  name: is_active
  #                  type: boolean
  #                  constraints:
  #                    nullable: false
  #                  defaultValueBoolean: false
  - changeSet:
      id: create table order
      author: anuchon.chon
      changes:
        - createTable:
            tableName: order
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  defaultValueDate: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: updated_date
                  type: timestamp
                  defaultValueDate: current_timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: create table model_reviews
      author: anuchon.chon
      changes:
        - createTable:
            tableName: model_reviews
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: model_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_reviews_model_id
                    references: model(id)
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_reviews_user_id
                    references: users(id)
              - column:
                  name: rating
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: comment
                  type: text
                  constraints:
                    nullable: true
              - column:
                  name: order_id
                  type: int
                  constraints:
                    deleteCascade: true
                    nullable: true
                    foreignKeyName: fk_model_reviews_order_id
                    references: order(id)
              - column:
                  name: created_date
                  type: timestamp
                  defaultValueDate: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: updated_date
                  type: timestamp
                  defaultValueDate: current_timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: create table model_reviews_images
      author: anuchon.chon
      changes:
        - createTable:
            tableName: model_reviews_images
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: model_reviews_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_model_reviews_images_model_reviews_id
                    references: model_reviews(id)
              - column:
                  name: image_url
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  defaultValueDate: current_timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: create view model_reviews_stat
      author: anuchon.chon
      changes:
        - createView:
            viewName: model_reviews_stat
            selectQuery: |
              SELECT DISTINCT m.id                 AS model_id,
              count(mr.model_id)                   AS count_vote,
              COALESCE(avg(mr.rating), 0::numeric) AS avg
              FROM model m
                LEFT JOIN model_reviews mr ON m.id = mr.model_id
                GROUP BY m.id
                ORDER BY model_id asc;
  - changeSet:
      id: create table web_review
      author: anuchon.chon
      changes:
        - createTable:
            tableName: web_review
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_web_review_user_id
                    references: users(id)
              - column:
                  name: rating
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: comment
                  type: text
                  constraints:
                    nullable: true
              - column:
                  name: created_date
                  type: timestamp
                  defaultValueDate: current_timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: add-columns-sample-product-to-customize
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: customize
            columns:
              - column:
                  name: sample_product
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: "1"
              - column:
                  name: zipcode
                  type: varchar
                  constraints:
                    nullable: false
                  defaultValue: ""

  - changeSet:
      id: create table coupon
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: code
                  type: varchar
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: coupon_category_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_coupon_category_id
                    references: coupon_category(id)
              - column:
                  name: coupon_type_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_coupon_type_id
                    references: coupon_type(id)
              - column:
                  name: percentage
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: max_price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: min_price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: customer_level_id
                  type: int
                  constraints:
                    nullable: true
                    foreignKeyName: fk_coupon_customer_level_id
                    references: customer_level(id)
              - column:
                  name: max_usage
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: start_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: end_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  defaultValueBoolean: true
                  constraints:
                    nullable: false
              - column:
                  name: is_deleted
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: modified_date
                  type: timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: create table user_credit
      author: pongpol.arm
      changes:
        - createTable:
            tableName: user_credit
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: order_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_user_credit_order_id
                    references: order(id)
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_user_credit_user_id
                    references: users(id)
              - column:
                  name: credit_amount
                  type: double
                  constraints:
                    nullable: false

  - changeSet:
      id: add-columns-special-technic-config-is-delete
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: special_technic_config
            columns:
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false

  - changeSet:
      id: add-columns-shipping-to-customize
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: customize
            columns:
              - column:
                  name: shipping_type
                  type: int
                  constraints:
                    nullable: false
                  defaultValue: "1"
              - column:
                  name: person_pickup_name
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: person_pickup_tell
                  type: varchar
                  constraints:
                    nullable: true

  - changeSet:
      id: create table coupon_product
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon_product
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: coupon_id
                  type: bigint
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_product_coupon_id
                    references: coupon(id)
              - column:
                  name: product_id
                  type: bigint
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_product_product_id
                    references: product(id)
  - changeSet:
      id: create table coupon_user
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon_user
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_user_user_id
                    references: users(id)
              - column:
                  name: coupon_id
                  type: bigint
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_user_coupon_id
                    references: coupon(id)
              - column:
                  name: is_used
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: order_id
                  type: int
                  constraints:
                    nullable: true
                    foreignKeyName: fk_coupon_user_order_id
                    references: order(id)
  - changeSet:
      id: create table coupon_special_technic
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon_special_technic
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: coupon_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_special_technic_coupon_id
                    references: coupon(id)
              - column:
                  name: special_technic_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_special_technic_special_technic_id
                    references: special_technic(id)
  - changeSet:
      id: create table coupon_order
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon_order
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: coupon_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_order_coupon_id
                    references: coupon(id)
              - column:
                  name: order_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_order_order_id

  - changeSet:
      id: create table order_status
      author: nanthawat.to
      changes:
        - createTable:
            tableName: order_status
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: status
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: sort
                  type: int
                  constraints:
                    nullable: false
  - changeSet:
      id: Load Data Order status
      author: nanthawat.to
      changes:
        - loadData:
            catalogName: user
            columns:
              - column:
                  header: id
                  name: id
                  type: numeric
              - column:
                  header: status
                  name: status
                  type: varchar
              - column:
                  header: sort
                  name: sort
                  type: numeric
            commentLineStartsWith: //
            encoding: UTF-8
            file: ../v1/data/orderStatus.csv
            quotchar: ''''
            relativeToChangelogFile: true
            schemaName: public
            separator: ','
            tableName: order_status
            usePreparedStatements: true

  - changeSet:
      id: add-columns-order
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: order
            columns:
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_order_user_id
                    references: users(id)
              - column:
                  name: order_no
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: total_price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: net_price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: vat_cost
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: vat
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: shipping_cost
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: status
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_order_order_status
                    references: order_status(id)
              - column:
                  name: receipt_type
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: shipping_type
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: shipping_sub_type
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: pickup_name
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: pickup_tell
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: is_deleted
                  type: boolean
                  constraints:
                    nullable: true
                  defaultValueBoolean: false

  - changeSet:
      id: create table orderItem
      author: nanthawat.to
      changes:
        - createTable:
            tableName: order_item
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: order_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_order_item_order_id
                    references: order(id)
              - column:
                  name: customize_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_order_item_customize_id
                    references: customize(id)
              - column:
                  name: shipping_cost
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: item_price
                  type: double
                  constraints:
                    nullable: false
                  defaultValueNumeric: "0"
              - column:
                  name: tracking_number
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: shipping_name
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: status
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: total_price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: delivery_date
                  type: timestamp
                  constraints:
                    nullable: true
              - column:
                  name: period_date
                  type: timestamp
                  constraints:
                    nullable: true
              - column:
                  name: order_date
                  type: timestamp
                  constraints:
                    nullable: true
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: updated_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: address_name
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: phone_number
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: email
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: address
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: zipcode
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: province
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: district
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: sub_district
                  type: varchar
                  constraints:
                    nullable: true

  - changeSet:
      id: create table orderAddress
      author: nanthawat.to
      changes:
        - createTable:
            tableName: order_address
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: order_id
                  type: int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_order_address_order_id
                    references: order(id)
              - column:
                  name: is_tax
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: phone_number
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: address
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: zipcode
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: province
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: district
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: sub_district
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: tax_player_type
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: tax_id
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: email
                  type: varchar
                  constraints:
                    nullable: true

  - changeSet:
      id: add-columns-custom-artwork
      author: nanthawat.to
      changes:
        - addColumn:
            tableName: customize
            columns:
              - column:
                  name: is_artwork
                  type: BOOLEAN
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: die_line
                  type: varchar
                  constraints:
                    nullable: true
