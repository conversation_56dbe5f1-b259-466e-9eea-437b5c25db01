databaseChangeLog:
  - changeSet:
      id: add columns is_private to coupon table
      author: pongpol.arm
      changes:
        - addColumn:
            tableName: coupon
            columns:
              - column:
                  name: is_private
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
  - changeSet:
      id: drop column order_id from coupon_user table
      author: pongpol.arm
      changes:
          - dropColumn:
              tableName: coupon_user
              columnName: order_id
  - changeSet:
      id: drop table user_credit
      author: pongpol.arm
      changes:
        - dropTable:
            tableName: user_credit
            cascadeConstraints: true
  - changeSet:
      id: drop table coupon_order
      author: pongpol.arm
      changes:
        - dropTable:
            tableName: coupon_order
            cascadeConstraints: true
  - changeSet:
      id: create table coupon_usage_log
      author: pongpol.arm
      changes:
        - createTable:
            tableName: coupon_usage_log
            columns:
              - column:
                  name: id
                  type: Int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: coupon_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_usage_log_coupon_id
                    references: coupon(id)
              - column:
                  name: order_id
                  type: Int
                  constraints:
                      nullable: false
                      foreignKeyName: fk_coupon_usage_log_order_id
                      references: order(id)
              - column:
                  name: user_id
                  type: Int
                  constraints:
                    nullable: false
                    foreignKeyName: fk_coupon_usage_log_user_id
                    references: users(id)
              - column:
                  name: discount_amount
                  type: double
                  constraints:
                      nullable: false
              - column:
                  name: total_price
                  type: double
                  constraints:
                      nullable: false
              - column:
                  name: credit_amount
                  type: double
                  constraints:
                      nullable: true
              - column:
                  name: used_at
                  type: timestamp
                  constraints:
                    nullable: false
